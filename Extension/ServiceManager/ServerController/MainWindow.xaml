<Window x:Class="ServerController.MainWindow"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mwbv="clr-namespace:ManagerWindowBase.View;assembly=ManagerWindowBase"
          xmlns:dwv="clr-namespace:DownloadWindow.View;assembly=DownloadWindow"
    Title="ServerController" Height="441" Width="810" WindowStartupLocation="CenterScreen" WindowStyle="ThreeDBorderWindow" ResizeMode="CanResizeWithGrip">
        <TabControl Name="MaintabControl" Grid.ColumnSpan="2">
            <TabItem Header="即时信息" Name="tabitem4" >
            <DockPanel LastChildFill="False">
                <DockPanel LastChildFill="False">
                    <Button Content="调整最大连接数" VerticalAlignment="Stretch" Width="90" Click="Button_Click"/>
                    <Button Click="Button_Click_2">管理窗口</Button>
                    <Button Click="Button_Click_3"  >更新文件</Button>
                    <Button Click="Button_Click_5" >启动服务器</Button>
                    <Button Click="Button_SaveData_Click">保存数据</Button>
                    <Button Click="Button_Click_4"  >关闭程序</Button>
                    <Button Click="Button_Click_6">新增服务器</Button>
                </DockPanel>
            </DockPanel>
            </TabItem>
            <TabItem Header="Log信息" Height="19" VerticalAlignment="Bottom"  >
                <mwbv:LogView></mwbv:LogView>
            </TabItem>
            <TabItem Header="文件下载"  >
                <DockPanel>
                 <dwv:DownloadView Name="downloadView" ></dwv:DownloadView>
                </DockPanel>
            </TabItem>
            <TabItem Header="管理配置" Name="configHost" />
        
            <TabItem Header="服务器配置" >
            <Grid>
                <ListView Name="ListViewServerConfig" HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                    <ListView.ContextMenu>
                        <ContextMenu>
                            <MenuItem Header="删除" Click="MenuItem_Delete_Click"/>
                            <Separator />
                            <MenuItem Header="执行脚本" Click="MenuItem_ExecuteScript_Click"/>
                        </ContextMenu>
                    </ListView.ContextMenu>
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="ID" DisplayMemberBinding="{Binding Path=ID}"/>
                            <GridViewColumn Header="服务器名" DisplayMemberBinding="{Binding Path=Name}" />
                            <GridViewColumn Header="IP" DisplayMemberBinding="{Binding Path=IP}" />
                            <GridViewColumn Header="端口" DisplayMemberBinding="{Binding Path=Port}" />
                            <GridViewColumn Header="WCF端口" DisplayMemberBinding="{Binding Path=WCFPort}" />
                            <GridViewColumn Header="服务器所在目录" DisplayMemberBinding="{Binding Path=FolderPath}" />
                            <GridViewColumn Header="结果" DisplayMemberBinding="{Binding Path=Result}"/>
                        </GridView>
                    </ListView.View>
                </ListView>
            </Grid>
        </TabItem>
    </TabControl>
</Window>
