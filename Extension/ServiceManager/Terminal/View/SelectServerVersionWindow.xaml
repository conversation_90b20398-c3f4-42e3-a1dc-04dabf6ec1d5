<Window x:Class="Terminal.View.SelectServerVersionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:model="clr-namespace:Terminal.Model"
        Title="选择服务器版本" Height="300" Width="300">
    <Window.Resources>
    </Window.Resources>
    <Grid Name="gridMain">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="25*"></ColumnDefinition>
            <ColumnDefinition Width="48*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition Height="30"></RowDefinition>
            <RowDefinition></RowDefinition>
        </Grid.RowDefinitions>
        <Label Content="branch" HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="0,0,0,2" Height="26" Width="46"/>
        <Label Content="Version Number" HorizontalAlignment="Left" Grid.Row="1" VerticalAlignment="Center" Height="26" Margin="0,2" Width="100"/>
        <TextBox Grid.Column="1" HorizontalAlignment="Left" Height="22" Text="{Binding Path=Branch}" VerticalAlignment="Center" Width="120" Margin="0,4"/>
        <TextBox Grid.Column="1" Grid.Row="1" HorizontalAlignment="Left" Height="22" Text="{Binding Path=VersionNumber}" VerticalAlignment="Center" Width="120" Margin="0,4"/>
        <Button Content="OK" HorizontalAlignment="Center" Margin="15,10,0,0" Grid.Row="3" VerticalAlignment="Top" Width="75" Click="ButtonOK_Click"/>
        <Button Content="Cancel" Grid.Column="1" HorizontalAlignment="Left" Margin="32,10,0,0" Grid.Row="3" VerticalAlignment="Top" Width="75" IsCancel="True" IsDefault="True"/>
        <Label Grid.ColumnSpan="2" Content="{Binding Path=FullPath}" HorizontalAlignment="Left" Grid.Row="2" VerticalAlignment="Center"/>
    </Grid>
</Window>
