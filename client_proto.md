## 客户端协议

[TOC]


### 概述



### 流程





### 协议




### CHANGE LOG

### 19.08 新版本游戏客户端, 修改人物/物件/地块绑定新增变动删除逻辑
Purpose：
- 物件人物地块 有很多冗余数据，展示层数据由客户端分析，游戏配置文件获得

####  [新增属性] 添加游戏配置文件 
```proto
message OP_GATEWAY_RES_CLIENT_VIRTUAL_WORLD_INIT
{
    repeated op_gameconfig.TerrainType terrain_Types       = 1;
    repeated op_gameconfig.Element_type element_Types      = 2;
    repeated op_gameconfig.ItemType item_Types             = 3;
    repeated op_gameconfig.AvatarBackbone avatarBackbone   = 4;
    repeated string resource_root                          = 5;
    optional op_def.KeyCode voice_keycode                         = 6;
    repeated op_gameconfig.Shop shops                      = 7;
    repeated string config_urls = 8;  // [新增] 游戏配置文件 给客户端
    optional op_def.MovingKeys moving_keys = 9; // [新增属性]
}
```

####  [新增属性] 添加场景初始化时设置镜头大小
```proto
message OP_CLIENT_REQ_VIRTUAL_WORLD_SCENE_CREATED
{
    optional float camera_width = 1;  // [新增属性]
    optional float camera_height = 2; // [新增属性]
}
```


####  [新增属性]   为了合并OP_CLIENT_REQ_VIRTUAL_WORLD_MOUSE_EVENT 和OP_CLIENT_REQ_VIRTUAL_WORLD_SELECT_OBJECT
```proto
message OP_CLIENT_REQ_VIRTUAL_WORLD_MOUSE_EVENT
{
    repeated op_def.MouseEvent mouse_event = 1;
    optional op_def.PBPoint3f point3f = 2;
    optional UI ui = 3;
    optional int32 id = 4; // 新增属性 选中物件/人的ID
    optional op_def.NodeType node_type = 5; // 新增属性 选中 物件/人的 node_type
}
```

#### [新接口]  BIND_ID，由BIND_ID注册Object之间的关系， （暂无卸载注册关系需求），除了游戏开始时bind之外，游戏运行时也可能对新生成的物件进行bind
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_BIND_ID {
    repeated BindID bind_ids = 1;
    optional op_def.NodeType node_type = 2; 
}
```

#### [新接口] CHANGE_BIND， 修改由BIND_ID注册的关系
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_CHANGE_BIND_ID {
    repeated BindID bind_ids = 1;
    optional op_def.NodeType node_type = 2; 
}
```

#### [新接口]  Object状态变化消息同步，包括avatar， 当前动画， 动画朝向， 昵称，展示徽章
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_OBJECT {
    repeated ObjectState object_states = 1;
    optional op_def.NodeType node_type = 2; 
}
```

####  [新接口] 新增物件/人/地块 ，仅包含id以及位置信息
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_OBJECT {
    repeated ObjectPosition object_positions = 1;
    optional op_def.NodeType node_type = 2; 
}
```

#### [新接口] 删除 物件/人/地块
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_DELETE_OBJECT {
    repeated int32 ids = 1; // 消息只删除场景中的物件，并不清除物件的bind关系
    optional op_def.NodeType node_type = 2; 
}
```

### 19.09 新版本游戏客户端， 优化人物行走
Purpose：
- 原有后端行走逻辑，操作体验差，需要优化

#### [新增协议] 同步时间
```proto
message OP_CLIENT_REQ_VIRTUAL_WORLD_SYNC_TIME //客户端发起时间同步
{
    required fixed64 client_start_ts = 1;
}

message OP_VIRTUAL_WORLD_RES_CLIENT_SYNC_TIME  // 服务器回复
{
    required fixed64 client_start_ts = 1; // 客户端发起时间
    required fixed64 server_receive_ts = 2; // 服务器接收时间
    required fixed64 server_send_ts = 3; // 服务器发送时间
}

```

#### [新属性] 新加预计到达终点时的时间戳
```proto
message MoveData {
    required int32 move_object_id = 1;
    required op_def.MoveType move_type = 2;
    required op_def.Direction direction = 3;
    required op_def.PBPoint3f destination_point3f = 4; // 终点坐标
    required int32 time_span = 5; // 时间间隔，根据发包间隔外加预计网络延时决定
    optional double timestemp = 6; // 新增属性，预计到达终点时的时间戳
}

```

#### [新协议] 客户端发起停止移动消息 并给予位置
```proto
message OP_CLIENT_REQ_VIRTUAL_WORLD_STOP_OBJECT
{
    required op_client.ObjectPosition object_positions = 1;
    optional op_def.NodeType node_type = 2;
}
```



#### [新协议] 服务端矫正位置
当A客户端发起停止消息时，服务端将校验位置，若误差在半格之内的会对其他客户端发出此协议
当误差超过半格时，除了对其他客户端发出此协议外，A客户端本身也会收到此协议
```proto

message OP_VIRTUAL_WORLD_REQ_CLIENT_ADJUST_POSITION
{
    repeated ObjectPosition object_positions = 1;
    optional op_def.NodeType node_type = 2;
}
```



### 19.09.06 不修改逻辑， 将ObjectState以及ObjectPosition结构改为Sprite

#### [新结构]
```proto
message Sprite {
    required int32 id = 1;
    optional op_def.PBPoint3f point3f = 2;
    optional op_gameconfig.Avatar avatar = 3;
    optional string current_animation_name = 4;
    optional op_def.Direction direction = 5; //动画朝向, 默认3,即面向玩家. 动画
    optional string nickname = 6;
    repeated op_def.BadgeCard display_badge_cards = 7;
    optional int32 opacity = 8 [default=100]; // [0-100] 0为透明
}

```

#### [修改协议] 不修改逻辑 单纯修改结构
```proto
// 修改协议名OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_OBJECT 为 OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_SPRITE
// ObjectState改为Sprite
message OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_SPRITE {
    repeated Sprite sprite_states = 1;
    optional op_def.NodeType node_type = 2;
}


// OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_OBJECT 为 OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_SPRITE
// ObjectPosition改为Sprite
message OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_SPRITE {
    repeated Sprite sprite_positions = 1;
    optional op_def.NodeType node_type = 2;
}


// OP_VIRTUAL_WORLD_REQ_CLIENT_DELETE_OBJECT 为 OP_VIRTUAL_WORLD_REQ_CLIENT_DELETE_SPRITE
message OP_VIRTUAL_WORLD_REQ_CLIENT_DELETE_SPRITE {
    repeated int32 ids = 1; // 消息只删除场景中的物件，并不清除物件的bind关系， 在镜头中移除后下次不会再发送bind消息，直接由ADD_OBJECT添加
    optional op_def.NodeType node_type = 2;
}



// ObjectPosition改为Sprite
message OP_VIRTUAL_WORLD_REQ_CLIENT_ADJUST_POSITION
{
    repeated Sprite sprite_positions = 1;
    optional op_def.NodeType node_type = 2;
}



// ObjectState改为Sprite
message OP_CLIENT_RES_VIRTUAL_WORLD_SYNC_SPRITE {
    required op_client.Sprite sprite_states = 1;
    optional op_def.NodeType node_type = 2; // 类型， 人 物件 地块 ,是否需要？
}

// ObjectPosition改为Sprite
message OP_CLIENT_REQ_VIRTUAL_WORLD_STOP_SPRITE
{
    required op_client.Sprite sprite_positions = 1;
    optional op_def.NodeType node_type = 2;
}

```

### 2019.09.09
Purpose:
- 为了避免添加物件时，绑定协议还未到达导致的异常， ADD_SPRITE将包含BIND_ID的信息

#### 删除了BIND_ID单独的协议
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_BIND_ID {
    repeated BindID bind_ids = 1;
    optional op_def.NodeType node_type = 2;
}
```

#### 同时删除了BIND_ID修改的协议
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_CHANGE_BIND_ID {
    repeated BindID bind_ids = 1;
    optional op_def.NodeType node_type = 2;
}
```


#### 修改了结构体
```proto
message Sprite {
    required int32 id = 1;
    optional op_def.PBPoint3f point3f = 2;
    optional op_gameconfig.Avatar avatar = 3;
    optional string current_animation_name = 4;
    optional op_def.Direction direction = 5; //动画朝向, 默认3,即面向玩家. 动画
    optional string nickname = 6;
    repeated op_def.BadgeCard display_badge_cards = 7;
    optional int32 opacity = 8 [default=100]; //透明度 [0-100] 0为透明
    optional int32 bind_id = 9; // 绑定id，如果该物品由config物件复制而来将带bind_id，否则为空
}
```

### 修改协议 如果有BindID将添加进Sprite结构体 
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_SPRITE {
    repeated Sprite sprites = 1;  // 原来叫sprite_positions 改为sprites, 它已经不在是单纯的position了
    optional op_def.NodeType node_type = 2;
}
```


## 2019.09.16
Purpose:
- 为一个协议由于包体过大（或逻辑需求）拆分成多个包时，告知当前这一批一共被拆分成多少个

#### Packet结构体
```proto
message Packet
{
    optional int32 current_frame = 1;
    optional int32 total_frame   = 2;
}


```

#### 新增属性， 描述这一批中的第几个包
```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_ADD_SPRITE {
    repeated Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
    optional op_def.Packet packet = 3; // 新增熟悉
}
```


## 2019.10.08
Purpose:
- 统一人物与物件的背包 都只有一个 原来人物背包为数组
- 将进入游戏时的移动键盘设定 改为键盘-事件绑定关系

```proto
message Actor {
    required int32 id = 1;
    optional string name = 2;
    optional int32 max_num = 3 [default = 20];
    optional string camp = 4;
    optional op_gameconfig.Avatar avatar = 5;
    repeated op_gameconfig.Attribute attributes = 6;
    optional op_gameconfig.Package package = 7; // 由repeated改为optional
    optional int32 scene_id = 8;
    optional int32 x = 9;
    optional int32 y = 10;
    optional int32 z = 11;
    optional int32 avatar_dir = 13;
    optional string walkable_area = 14;
    optional string collision_area = 12;
    repeated int32 origin_point = 15;
    repeated int32 walk_origin_point = 16;
    optional string nickname = 17;
    repeated op_gameconfig.Slot slot = 18;
    required int32 uuid = 19;
    optional bool is_in_current_scene_voice_room = 20;
    repeated op_def.BadgeCard display_badge_cards = 21;
}
```

```proto
message KeyCodeEvent
{
    required TQ_EVENT tq_event = 1;
    repeated KeyCode key_codes = 2;
}


message OP_GATEWAY_RES_CLIENT_VIRTUAL_WORLD_INIT {
    repeated op_gameconfig.TerrainType terrain_Types = 1;
    repeated op_gameconfig.Element_type element_Types = 2;
    repeated op_gameconfig.ItemType item_Types = 3;
    repeated op_gameconfig.AvatarBackbone avatarBackbone = 4;
    repeated string resource_root = 5;
    repeated op_gameconfig.Shop shops = 6;
    repeated string config_urls = 7;
    repeated op_def.KeyCodeEvent key_events = 8; // 删除voice_keycode和moving_keys，改为key_events
}
```


## 2019.10.26
Purpose:
- 关于客户端无法从pi内获取的动画资源，获取规则

// 动画资源包含(avatar，frames等）  
// 当Sprite第一次被添加时，会校验是否已经加载过  
// 若已经加载过，则不会在发送动画资源  
// 如果客户端因为某些原因（缓存资源被清除等），可以通过OP_REQ_VIRTUAL_WORLD_QUERY_SPRITE_RESOURCE协议强制获取sprite资源  
// 请求后将服务端返回带有资源的OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_SPRITE消息作为回应  
```proto
message Sprite {
    required int32 id = 1;
    optional op_def.PBPoint3f point3f = 2;
    optional op_gameconfig.Avatar avatar = 3;
    optional string current_animation_name = 4;
    optional op_def.Direction direction = 5; //动画朝向, 默认3,即面向玩家. 动画
    optional string nickname = 6;
    repeated op_def.BadgeCard display_badge_cards = 7;
    optional int32 opacity = 8 [default=100]; // [0-100] 0为透明
    optional int32 bind_id = 9;
    optional op_gameconfig.Animation animation = 10;  // 新增属性
    optional op_gameconfig.Display display = 11;      // 新增属性
}


// 新增协议 用于请求sprite的资源
message OP_REQ_VIRTUAL_WORLD_QUERY_SPRITE_RESOURCE
{
    repeated int32 ids = 1;
}

```



## 2019.10.29
Purpose:
- 关于通知客户端选中物体协议， 协议中应包含该物体所有的显示资源， 由ID确立它与原有数据的关系
 客户端新生成的物件需要通过bind_id来关联原有id。 由接收方（服务器、编辑器）绑定原有事件脚本等信息到客户端新生成的物体上

```proto

message OP_VIRTUAL_WORLD_REQ_CLIENT_CHANGE_TO_EDITOR_MODE // 由原本OP_REQ_VIRTUAL_WORLD_QUERY_SPRITE_RESOURCE 更换名称而来，协议内容不变
{
    required Actor actor = 1;
    required Scene scene = 2;
}


message OP_EDITOR_REQ_CLIENT_CHANGE_TO_EDITOR_MODE // 由原本OP_REQ_VIRTUAL_WORLD_QUERY_SPRITE_RESOURCE 更换名称而来，协议内容不变
{
    required Actor actor = 1;
    required Scene scene = 2;
}

message OP_VIRTUAL_WORLD_REQ_CLIENT_MOUSE_SELECTED_SPRITE
{
    required Sprite sprite  = 1;
}


message OP_EDITOR_REQ_CLIENT_MOUSE_SELECTED_SPRITE
{
    required Sprite sprite = 1;
}

```


## 2019.11.01
Purpose:
- 添加编辑场景相关消息 [issue](https://code.apowo.com/PixelPai/game-core/issues/54#note_2313)

```proto
// 放置(批量)物件
message OP_CLIENT_REQ_EDITOR_CREATE_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// 同步（批量）物件状态
message OP_CLIENT_REQ_EDITOR_SYNC_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// 删除（批量）
message OP_CLIENT_REQ_EDITOR_DELETE_SPRITE
{
    repeated int32 ids = 1;
}


// 控制镜头
message OP_CLIENT_REQ_EDITOR_RESET_CAMERA
{
    optional int32 x = 1;
    optional int32 y = 2;
    optional int32 width = 3;
    optional int32 height = 4;
}

```



## 2019.11.12
Purpose:
- 添加编辑场景发送给客户端 新增 删除sprite（redo undo等事件触发）
```proto


message OP_EDITOR_REQ_CLIENT_CREATE_SPRITE // 新增协议  编辑器通知客户端 新增某些sprite
{
    repeated Sprite sprites = 1;
    optional op_def.NodeType node_type = 2; // 只能批量添加同一类型的sprite
}

message OP_EDITOR_REQ_CLIENT_DELETE_SPRITE // 新增协议 编辑器通知客户端删除某些sprite
{
    repeated int32 ids = 1;
    optional op_def.NodeType node_type = 2; // 删除时需要给予类型
}

message OP_EDITOR_REQ_CLIENT_SYNC_SPRITE // 新增协议 编辑器通知客户端同步某些Sprite
{
    repeated Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

```


## 2019.11.18
Purpose:
- 添加编辑器、客户端 选中逻辑
```proto
message OP_CLIENT_REQ_EDITOR_FETCH_SPRITE
{
    repeated int32 ids = 1;
    optional op_def.NodeType node_type = 2;
}
message OP_EDITOR_REQ_CLIENT_FETCH_SPRITE
{
    repeated int32 ids = 1;
    optional op_def.NodeType node_type = 2;
}

```

## 2019.11.18
Purpose
- 添加服务器心跳包
```
// 心跳包没有包体仅仅包含包头信息
// _OP_GATEWAY_RES_CLIENT_PONG
// _OP_CLIENT_REQ_GATEWAY_PING

```


## 2019.12.30
Purpose
- 物件以及任务移动
```proto
// 弃用原有移动消息
message OP_GATEWAY_REQ_CLIENT_MOVE_CHARACTER {
    repeated MoveData move_data = 1;
}

// 弃用原有移动消息
message OP_GATEWAY_REQ_CLIENT_MOVE_ELEMENT {
    repeated MoveData move_data = 1;
}

// 新增消息
message OP_VIRTUAL_WORLD_REQ_CLIENT_MOVE_SPRITE {
    repeated MoveData move_data = 1;
    optional op_def.NodeType node_type = 2;
}

```

## 2019.12.31
Purpose
- 同步sprite位置  

```proto
// 同步sprite位置
message OP_CLIENT_REQ_VIRTUAL_WORLD_SYNC_SPRITE_POSITION
{
    required op_client.Sprite sprite_positions = 1;
    optional op_def.NodeType node_type = 2;
}

```

## 2020.01.04
Purpose:
- 编辑背包逻辑

```proto

// op_client._OP_VIRTUAL_WORLD_REQ_CLIENT_ENABLE_EDIT_MODEL= 0x00142039;
// 玩家进入游戏时，lua脚本将校验用户是否拥有编辑权限，对于拥有权限的人将会发送，激活编辑模式的消息

// op_virtual_world._OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ENTER = 0x0016104a;
// 客户端通知服务端进入编辑模式

// op_client._OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_READY = 0x0014203b
// 服务端告知客户端服务端准备完成

// 请求编辑背包
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_QUERY_EDIT_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional string query_string = 3; // 查找字符串， 暂未实现，先预留
}


// op_client._OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_QUERY_EDIT_PACKAGE = 0x0014203a;
// 返回编辑背包物品
message EditPackageItem
{
    required string id = 1;
    required int32 count = 2;
    optional Sprite sprite = 3;
}
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_QUERY_EDIT_PACKAGE
{
    optional int32 page = 1;
    optional int32 pre_page = 2;
    optional int32 max_page = 3;
    repeated EditPackageItem items = 4;
}


// 回收sprite
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_RECYCLE_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// 翻转sprite
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_FLIP_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// 修改sprite位置
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_CHANGE_SPRITE_POSITION
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// 添加sprite
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ADD_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}

// op_virtual_world._OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_SAVE   = 0x0016104c;
// 保存编辑模式下的操作


// op_virtual_world._OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_LEAVE = 0x00161051;
// 退出编辑模式，该消息将作废所有编辑模式下的更改


```


## 2020.01.07
Purpose:
- 编辑模式，选中物件

```proto
// 选中物件
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_SELECTED_SPRITE
{
    required string id = 1;
}


// 返回选中物件对应的sprite
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_SELECTED_SPRITE
{
    required Sprite sprite = 1;
    optional op_def.NodeType node_type = 2;
}


```

## 2020.01.07
Purpose:
- 修改Sprite位置，（同场景内传送）


```proto

message OP_VIRTUAL_WORLD_REQ_CLIENT_SET_SPRITE_POSITION
{
    required int32 id = 1;
    required op_def.PBPoint3f position = 2;
    optional op_def.NodeType node_type = 3;
}
```


## 2020.01.19
Purpose:
- 添加pkt market


```proto
// -----------------------------------基本结构

// 购买结构
message OrderCommodities
{
    required string id = 1;
    required int32 quantity = 2;
}

// 商品结构
message MarketCommodity
{
    required string id = 1;
    optional string name = 2;
    optional string des = 3;
    optional string icon = 4;
    optional string category = 5;
    optional string subcategory = 6;
    optional int32 price_tu_ding = 7; // 目前没有需求的情况下 两个价格只会有一个有值
    optional int32 price_gold = 8;
    optional string related_id = 9;
    optional int32 remain_number = 10; // 可能为空， 如果有则显示数量
    optional int32 each_purchase_number = 11[default=1]; // 每份的数量
}

// 
message MarketCategory
{
    required string category = 1;
    repeated string subcategory = 2;
}


// --------------消息

// _OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_GET_CATEGORIES 获取Market的目录

// 返回market目录结构
message OP_VIRTUAL_WORLD_RES_CLIENT_GET_MARKET_CATEGORIES
{
    repeated MarketCategory market_category = 1; 
}

// 请求商店内容
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY
{
    optional int32 page = 1;
    optional int32 pre_page = 2;
    optional int32 max_page = 3;
    optional string category = 4;
    optional string subcategory = 5;
}

// 返回market内的商品
message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY
{
    optional int32 page = 1;
    optional int32 pre_page = 2;
    optional int32 max_page = 3;
    optional string category = 4;
    optional string subcategory = 5;
    repeated MarketCommodity commodities = 6;
}

// 请求购买
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_BUY_ORDER_COMMODITIES
{
    repeated OrderCommodities order_commodities = 1;
}





```


## 2020.02.14
Purpose:
- 修改astar行走协议


```proto
// 按astar寻路的路径行走
message OP_VIRTUAL_WORLD_REQ_CLIENT_MOVE_SPRITE_BY_PATH
{
    required int32 id = 1;
    optional op_def.NodeType node_type = 2;
    repeated op_def.PBPoint3f path = 3;
    optional int32 time_span = 4; // 时间间隔，根据发包间隔外加预计网络延时决定
    optional double timestemp = 5;
}


```

## 2020.02.22
Purpose:
- 添加astar行走，是否可到达目标点反馈

```proto
message OP_VIRTUAL_WORLD_REQ_CLIENT_MOVE_SPRITE_BY_PATH
{
    required int32 id = 1;
    optional op_def.NodeType node_type = 2;
    repeated MovePoint path = 3;
    optional int32 time_span = 4; // 时间间隔，根据发包间隔外加预计网络延时决定
    optional double timestemp = 5;
    optional op_def.PathReachableStatus path_status = 6; // 新增可到达状态
    optional op_def.PBPoint3f target_pos = 7;  // 目标点
}

```


- 按摆放顺序 逆序回收物件协议
客户端发起OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_RECYCLE_LAST_SPRITE，即可



## 2020.02.24
Purpose:
- 

```proto

// 回收所有物件
OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_RECYCLE_ALL

// 获取出生点
OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_GET_SPAWN_POINT

// 返回出生点
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_GET_SPAWN_POINT
{
    required op_def.PBPoint3f spawn_point = 1;
}

// 设置出生点
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_SET_SPAWN_POINT
{
    required op_def.PBPoint3f spawn_point = 1;
}


```


## 2020.02.26
Purpose:
- H5编辑模式下，房间列表，房间以及密码设定

```proto
// 获取房间列表， 目前只有请求头，后续可能进一步深入会有筛选条件 目前策划层面上没有
// OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_GET_ROOM_LIST

// 返回房间列表
message EditModeRoom
{
    required string room_id = 1;
    optional string game_id = 2;
    optional string name = 3;
    optional string password = 4;
}

message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_LIST
{
    repeated EditModeRoom rooms = 1;
}

// 请求房间详细信息，
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_GET_ROOM_INFO
{
    required string room_id = 1;
}


// 返回房间详细信息，目前只有屋主可以拿到信息，另外屋主进入场景时也会更新这条信息，其他成员没必要有密码的信息，策划层面也没有进一步规定，后续对于其他人员有另外的消息返回
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
}

// 更新房间信息
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_UPDATE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
}



```

## 2020.02.27
Purpose:
- 编辑模式下，旋转物件

```proto
// 客户端通知服务端旋转，默认顺时针旋转，若reverse给true，则为逆时针旋转
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ROTATE_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
    optional bool reverse = 3 [default=false];
}

// 服务端通知客户端旋转sprite的对应动画以及是否反转
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROTATE_SPRITE
{
    repeated ChangeAnimation animations = 1;
}


```


## 2020.03.03
Purpose:
- 编辑模式下，修改位置，旋转动画等消息合并为更新Ｓｐｒｉｔｅ

```proto
// 对于未添加物件的更新都会被作废
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_UPDATE_SPRITE
{
    repeated op_client.Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
}
```



## 2020.03.04
Purpose:
- 房间列表信息更新，添加锁定，进入房间消息

```proto



// 客户端发起进入房间
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ENTER_ROOM
{
    required string room_id = 1;
    optional string password = 2; // 密码可以选，前端房间信息可能滞后
}

// 服务器返回客户端进入房间结果
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ENTER_ROOM
{
    required string room_id = 1;
    optional op_def.EditModeEnterRoomResult result = 2;
    optional string msg = 3;
    optional string game_id = 4;
}

// 添加了是否上锁的状态
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
    optional op_def.EditModeRoomPrivacy privacy = 4; // 目前只有三种状态　私有　公开　上锁，　默认公开
}


message EditModeRoom
{
    required string room_id = 1;
    optional string game_id = 2;
    optional string name = 3;
    optional int32 player_count = 4;
    optional op_def.EditModeRoomPrivacy privacy = 5;
}

message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_LIST
{
    repeated EditModeRoom popular_rooms = 1; // 设定上为点赞最多的房间，缺失点赞功能，暂时返回前三个，数量待定
    repeated EditModeRoom player_rooms = 2;
}


message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
    optional op_def.EditModeRoomPrivacy privacy = 4;
}


```


## 2020.03.05
Purpose:
- 物件批量布置

```proto
// 客户端请求批量添加物品
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ADD_SPRITE_BY_TYPE
{
    required op_client.Sprite sprite = 1;
    repeated op_def.PBPoint3f points = 2;
    optional op_def.NodeType node_type = 3;
}


// 服务端将剩余数量反馈回客户端
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ADD_SPRITE_BY_TYPE
{
    required int32 id = 1;
    optional op_def.NodeType node_type = 2;
    optional int32 remain_count = 3;
}
```

## 2020.03.06
Purpose:
- 物件扩展布置--单个，由于目前单个需要一些额外功能，而且不能确定未来是否有更多的不一样的需求 因此另外独立一条出来

```proto
//
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ADD_SINGLE_SPRITE_BY_TYPE
{
    required int32 id = 1;
    optional op_def.NodeType node_type = 2;
    optional int32 remain_count = 3;
    repeated int32 added_sprite_ids = 4; // 按顺序返回，最后一个是最后加的
}

message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ADD_SINGLE_SPRITE_BY_TYPE
{
    required op_client.Sprite sprite = 1;
    repeated op_def.PBPoint3f points = 2;
    optional op_def.NodeType node_type = 3;
}


```


## 2020.03.10
Purpose:
- 获取房间历史，以及我的房间列表


```proto
// 请求访问历史  
// OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_GET_PLAYER_ENTER_ROOM_HISTORY



// 返回访问历史
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_GET_PLAYER_ENTER_ROOM_HISTORY
{
    repeated EditModeRoom self_rooms = 1;
    repeated EditModeRoom history_rooms = 2;
}

```

## 2020.03.13
Purpose:
- 获取家具背包

```proto

// 请求背包子分类
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_GET_PACKAGE_CATEGORIES
{
    required op_def.EditModePackageCategory category = 1;
}

// 返回子分类
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_GET_PACKAGE_CATEGORIES
{
    required op_def.EditModePackageCategory category = 1;
    repeated op_def.StrMap subcategory = 2;
}



// 请求背包，（编辑场景内的背包）
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_QUERY_EDIT_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional op_def.EditModePackageCategory category = 3;
    optional string subcategory = 4;
    optional string query_string = 5;
}

// 返回背包，（编辑场景内的背包）
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_QUERY_EDIT_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    optional op_def.EditModePackageCategory category = 4;
    optional string subcategory = 5;
    repeated CountablePackageItem items = 6;
}



// 请求用户背包
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional op_def.EditModePackageCategory category = 3;
    optional string subcategory = 4;
    optional string query_string = 5;
}

// 返回用户背包
message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    optional op_def.EditModePackageCategory category = 4;
    optional string subcategory = 5;
    repeated CountablePackageItem items = 6;
}

```


```proto
// 请求背包物件资源
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY_PACKAGE_ITEM_RESOURCE
{
    required string id = 1;
}


// 返回背包物件资源
message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY_PACKAGE_ITEM_RESOURCE
{
    required string id = 1;
    repeated op_gameconfig.Animation animations = 2;
    optional op_gameconfig.Display display = 3;
    optional op_gameconfig.Avatar avatar = 4;
}

```

## 2020.03.25
Purpose:
- 开启编辑模式并选中。 人物将进入编辑模式，并且在SCENE_CREATED之后选中物件

```proto
message OP_CLIENT_REQ_VIRTUAL_WORLD_EDIT_MODE_ENABLE_AND_SELECTED_SPRITE
{
    required string id = 1; // 背包内物件的id
}

```


## 2020.04.07
Purpose:
- 交互气泡协议,显示删除以及触发交互,需要新增bubble的资源以及ui停留时间

```proto
//展示气泡
message  OP_VIRTUAL_WORLD_REQ_CLIENT_SHOW_INTERACTIVE_BUBBLE
{
    required int32 id = 1;
    required int32 receiver_id = 2;
    optional int32 duration = 3[default=-1]; // 存续时间,单位毫秒
    optional op_gameconfig.Display display = 4;
}

message OP_VIRTUAL_WORDL_REQ_CLIENT_REMOVE_INTERACTIVE_BUBBLE
{
    repeated int32 ids = 1;
}

// 交互响应,被交互的气泡id以及绑定的id
message  OP_CLIENT_RES_VIRTUAL_WORLD_ACTIVE_BUBBLE
{
    required int32 id = 1;
}

```


## 2020.04.10
Purpose:
- [矿洞] 矿洞结算面板

```proto
message CountablePackageItem
{
    required string id = 1;
    required int32 count = 2;  // 数量
    optional string name = 3;  // 道具名称
    optional string des  = 4;  // 描述
    optional op_gameconfig.Display display = 5; // 背包物件贴图
    optional string source = 6;  // 来源说明
    optional string short_name = 7;  // 名称缩写
}

// [矿洞] 展示矿洞结算面板
message OP_VIRTUAL_WORLD_REQ_CLIENT_MINING_MODE_SHOW_REWARD_PACKAGE
{
    repeated CountablePackageItem items = 1;
}

// [矿洞]结算奖励
// 只需要发请求即可,包体无内容
// OP_CLIENT_REQ_VIRTUAL_WORLD_MINING_MODE_STORAGE_REWARD

```


## 2020.04.16
Purpose:
- 矿工装备面板

```proto
message MiningEquipment
{
    required string id = 1;                  //
    optional string name = 2;               // 名称
    optional string description = 3;       // 说明
    repeated string buff_display_names = 4;       // buff的说明展示,可能会有多个
    repeated string condition_display_names = 5; // 可能存在需要条件不能购买的情况
    optional op_gameconfig.Price price = 6;     // 价格
    optional bool owned = 7;                   // 是否已经拥有
    optional bool selected = 8;               // 当前装备
    optional bool qualified = 9;             // 是否可以购买
    optional op_gameconfig_01.Display display = 10;  // 展示资源
}

message MiningEquipmenetArray
{
    required string equipment_type = 1; // 矿镐 or 矿石车
    repeated MiningEquipment mine_equipments = 2;
}

// 服务端通知客户端 展示矿工装备选择面板
// 装备存在owned属性以及condition_display_names属性,若装备owned属性为false则需要展示condition_display_names否者展示勾选按钮即可
// 装备不一定需要金币购买可能需要等级或其他限制解锁
message OP_VIRTUAL_WORLD_REQ_CLIENT_MINING_MODE_SHOW_SELECT_EQUIPMENT_PANEL
{
    repeated MiningEquipmenetArray mine_equipments = 1;
}

// 服务端返回激活的装备
message OP_VIRTUAL_WORLD_RES_CLIENT_MINING_MODE_ACTIVE_EQUIPMENT
{
    optional MiningEquipment mine_equipment = 1;
}

// 客户端通知服务端选中装备
message OP_CLIENT_RES_VIRTUAL_WORLD_MINING_MODE_EQUIP_EQUIPMENT
{
    optional string equipment_id = 1;
}


// 客户端通知服务端激活装备, 装备不一定需要金币购买可能需要等级或其他限制解锁
message OP_CLIENT_RES_VIRTUAL_WORLD_MINING_MODE_ACTIVE_EQUIPMENT
{
    optional string equipment_id = 1;
}


```



## 2020.04.28
Purpose:
- 道具回收商面板

作废
```proto

// 服务端请求客户端打开回收面板
message OP_VIRTUAL_WORLD_REQ_CLIENT_MARKET_SHOW_PROP_COLLECTOR_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    optional op_def.EditModePackageCategory category = 4;
    optional string subcategory = 5;
    repeated CountablePackageItem items = 6;
    repeated op_def.StrPair all_subcategory = 7;
}


// 客户端请求服务端具体页数的数据
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY_PROP_COLLECTOR_PACKAGE
{
    optional int32 page = 1; // 请求第几页
    optional int32 per_page = 2; // 每页多少个
    optional op_def.EditModePackageCategory category = 3; // 那个分类
    optional string subcategory = 4; // 子分类
    optional string query_string = 5;  // 查找字符串
}

// 服务端返回客户端数据
message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY_PROP_COLLECTOR_PACKAGE
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    optional op_def.EditModePackageCategory category = 4;
    optional string subcategory = 5;
    repeated CountablePackageItem items = 6;
}

// 客户端通知服务端回收道具
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_SELL_PROPS
{
    repeated op_client.CountablePackageItem items = 1;
}

```

## 2020.04.30
Purpose:
- 添加背包道具属性，可出售

```proto
message CountablePackageItem
{
    required string id = 1;
    required int32 count = 2;
    optional string name = 3;  // 道具名称
    optional string des  = 4;
    optional op_gameconfig.Display display = 5; // 背包物件贴图
    optional string source = 6;
    optional string short_name = 7;
    optional string category = 8;
    optional string subcategory = 9;
    optional op_gameconfig.Price selling_price = 10;  // 售价  COIN为银币 DIAMOND为钻石
    optional bool tradable = 11[default=false];   // 是否可以与玩家交易
    optional bool recyclable = 12[default=false]; // 是否可被系统回收
    optional bool executable = 13[default=false]; // 是否可以使用
}


// 客户端通知服务端回收道具
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_SELL_PROPS
{
    required op_def.EditModePackageCategory category = 1; // 需要回收的道具分类
    repeated op_client.CountablePackageItem items = 2; // 需要回收的道具
    optional op_gameconfig.Price total_price = 3;  // 需要回收的道具总价值
}

```


## 2020.04.30
Purpose: 
- 

```proto


// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CRAFT_SKILLS


// 服务端请求客户端打开制造界面
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_CRAFT_SKILLS
{
    repeated op_pkt_def.PKT_Skill skills = 1;
}

// 客户端请求服务端制造技能详细配方
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CRAFT_QUERY_FORMULA
{
    required string id = 1; // 制造技能id
}


// 服务端返回制造技能配方信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_CRAFT_QUERY_FORMULA
{
    required string id = 1;  // 合成技能ID
    optional string product_name = 2; // 合成技能产物名称
    optional string product_des = 3;  // 合成技能产物描述
    optional string product_source = 4;  // 合成技能产物来源
    repeated op_gameconfig.Animation product_animations = 5; // 合成技能产物动画
    optional op_gameconfig.Display product_display = 6;  // 合成技能产物资源
    optional op_gameconfig.Avatar product_avatar = 7; // 合成技能产物Avatar
    repeated CountablePackageItem materials = 8;  // 配方原料
}


// 客户端请求服务端使用制造技能 即 制作按钮
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CRAFT_ALCHEMY
{
    required string id = 1; // 合成技能id
}



```



## 2020.05.25
Purpose: 
- 

```proto

// 客户端请求服务端更换装扮
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_DRESS_UP_AVATAR
{
    required op_gameconfig.Avatar avatar = 1;
//    repeated op_pkt_def.PKT_Avatar avatars = 1;
}

// 客户端请求服务端还原装扮, 服务端将返回OP_VIRTUAL_WORLD_RES_CLIENT_PKT_RESET_AVATAR还原后的装扮信息
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_RESET_AVATAR


// 服务端返回客户端还原后的装扮信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_RESET_AVATAR
{
    required op_gameconfig.Avatar avatar = 1;
}

```



## 2020.05.25
Purpose: 
- 添加个人信息面板请求消息

```proto

// 客户端请求服务端个人信息,服务端将返回 OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SELF_PLAYER_INFO
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SELF_PLAYER_INFO



```


## 2020.05.28
Purpose:
- 

```proto

// 客户端请求服务端更换装扮
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_DRESS_UP_AVATAR
{
    repeated string avatar_item_ids = 1; // 本次装扮过的id,需要按装扮顺序排序(最好去重)
}

enum PKT_Subscript
{
    PKT_SUBSCRIPT_UNSET = 0x0000;  // 无角标
    PKT_SUBSCRIPT_CHECKMARK = 0x0001;  // 打钩
}


message CountablePackageItem
{
    // ...
    optional op_gameconfig.Avatar avatar = 17; // 新增物件装扮信息
    optional op_pkt_def.PKT_Subscript right_subscript = 18[default=PKT_SUBSCRIPT_UNSET]; // 新增物件右下角标,默认为无角标
}

```

## 2020.05.29
Purpose:
- 获取已穿avatar列表
- 请求回家

```proto

// 请求人物当前已穿avatar的id列表
// _OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CURRENT_DRESS_AVATAR_ITEM_ID

// 返回人物当前已穿装扮
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_CURRENT_DRESS_AVATAR_ITEM_ID
{
    repeated string avatar_item_ids = 1;
}


// 请求回家
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_GO_HOME

```

## 2020.06.06
Purpose:
- 任务相关

```proto

// 任务结构
enum PKT_Quest_Stage
{
    PKT_QUEST_STAGE_NOT_ACCEPTED = 0; //未获得  目前没有展示需求
    PKT_QUEST_STAGE_ACCEPTABLE = 1;   //可接取  目前没有展示需求
    PKT_QUEST_STAGE_PROCESSING = 2;   //进行中
    PKT_QUEST_STAGE_FINISHED = 3;     //完成
    PKT_QUEST_STAGE_END = 4; // 领取奖励结束  目前没有展示需求
}


enum PKT_Quest_Type
{
    QUEST_MAIN_MISSION = 1; // 主线任务
    QUEST_SIDE_MISSION = 2; // 支线任务
    QUEST_DAILY_GOAL = 3; // 每日任务
}

message PKT_Quest
{
    required string id = 1;
    optional string name = 2;
    optional string detail = 3; // 任务目标描述
    optional op_gameconfig.Display display = 4; // 任务图标
    optional op_pkt_def.PKT_Quest_Stage stage = 5[default=PKT_QUEST_STAGE_NOT_ACCEPTED]; // 任务状态
    optional op_pkt_def.PKT_Quest_Type quest_type = 6; // 任务类型
    optional CountablePackageItem targets = 7; // 任务目标 展示 可能为空
    optional CountablePackageItem rewards = 8; // 任务奖励
}


///////////////////////////////
// 任务消息


// 请求任务列表
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_QUEST_LIST
// 服务端返回更新任务
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_QUEST_LIST
{
    repeated PKT_Quest quests = 1; //
}


// 请求任务详情
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_QUEST_DETAIL
{
    required string id = 1;
}


// 服务端返回任务详情
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_QUEST_DETAIL
{
    required PKT_Quest quest = 1;
}


// 提交任务
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SUBMIT_QUEST
{
    required string id = 1;
}

// 提交任务后 服务端会自动刷新任务列表 返回 OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_QUEST_LIST
```


## 2020.06.09
Purpose:
- 新增商店名称, 名称以策划配表为准, 客户端主动打开的面板 只有shop

```proto

//////////// 新增协议

// 原来只有opcode,没有请求体 新增请求体
message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_GET_CATEGORIES
{
    optional string market_name = 1; // 目前只有shop是客户端主动发起的,即主面板的商店
}


// 新增服务端打开客户端商店面板协议
message OP_VIRTUAL_WORLD_REQ_CLIENT_MARKET_SHOW_MARKET_BY_NAME
{
    required string market_name = 1;
}


///////////// 新增属性

message OP_VIRTUAL_WORLD_RES_CLIENT_GET_MARKET_CATEGORIES
{
    repeated op_def.MarketCategory market_category = 1;
    optional string market_name = 2; // 商店名称
}


message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    optional string category = 4;
    optional string subcategory = 5;
    repeated MarketCommodity commodities = 6;
    optional string market_name = 7;  // 商店名称
}

message OP_VIRTUAL_WORLD_RES_CLIENT_MARKET_QUERY_COMMODITY_RESOURCE
{
    required string id = 1;
    optional string category = 2;
    repeated op_gameconfig_01.AnimationData animations = 3;
    optional op_gameconfig.Display display = 4;
    optional op_gameconfig.Avatar avatar = 5;
    optional string market_name = 6; // 商店名称
}


message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional string category = 3;
    optional string subcategory = 4;
    optional string query_string = 5;
    optional string market_name = 6;    // 商店名称
}

message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_BUY_ORDER_COMMODITIES
{
    repeated op_def.OrderCommodities order_commodities = 1;
    optional string market_name = 2; // 商店名称
}

message OP_CLIENT_REQ_VIRTUAL_WORLD_MARKET_QUERY_COMMODITY_RESOURCE
{
    required string id = 1;
    optional string category = 2;
    optional string market_name = 3; // 商店名称
}


// 新增默认UI设定
message PKT_UI
{
    optional string name = 1;
    optional bool visible = 2;
    optional bool disabled = 3;
}

message OP_VIRTUAL_WORLD_REQ_CLIENT_PKT_REFRESH_ACTIVE_UI
{
    repeated op_pkt_def.PKT_UI ui = 1;// PICaMain.RoomInfo PlayerInfo Navigate Quest Map Market Package GoHome
}


```


## 2020.06.30
Purpose:
- 状态同步

```proto
message State
{
    optional string name = 1;
    optional bytes packet = 2; 
    optional NodeType type = 3[default=UnknownNodeType]; // 现在都是UnknownNodeType,即发送方和接收方自定义packet结构，并解析，未来考虑将游戏原有结构类型也按此结构同步
    optional ExecCode exec_code = 4[default=EXEC_CODE_UPDATE];
}

message OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_STATE
{
    required op_gameconfig_01.Node owner = 1;
    repeated op_def.State state = 2;
}
```

## 2020.07.01
Purpose:
- 状态同步patch

```proto
message StateGroup
{
    required op_gameconfig_01.Node owner = 1;
    repeated op_def.State state = 2;
}

message OP_VIRTUAL_WORLD_REQ_CLIENT_SYNC_STATE
{
    repeated StateGroup state_group = 1;
}


```


## 2020.07.09
Purpose:
- 房间等级

```proto

message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
    optional op_def.EditModeRoomPrivacy privacy = 4;
    optional string owner_name = 5;
    optional int32 player_count = 6;
    optional op_pkt_def.PKT_Level room_level = 7;  // 新增房间等级信息，属性信息待扩展
}


message PKT_Property
{
    required string key = 1;
    optional string name = 2;
    optional float value = 3;       // 基本
    optional float temp_value = 4; // 临时增加
}



// 服务端返回客户端玩家自身信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SELF_PLAYER_INFO  // 我的信息
{
    optional int32 id = 1; // sprite id
    optional string cid = 2; // 玩家角色id, 展示
    optional int32 like = 3[default=0]; // 点赞
    optional op_pkt_def.PKT_Avatar current_avatar = 4;
    optional string nickname = 5; // 昵称
    optional op_pkt_def.PKT_Level level = 6; // 等级
    optional string current_title = 7; // 当前称号
    repeated op_pkt_def.PKT_Skill life_skills = 8;
    repeated op_pkt_def.PKT_Badge badges = 9;  // 徽章
    repeated op_pkt_def.PKT_Title titles = 10; // 称号
    repeated op_pkt_def.PKT_Property properties = 11; // 新增属性
}


message OP_VIRTUAL_WORLD_REQ_CLIENT_PKT_PLAYER_INFO
{
    optional int32 coin = 1;
    optional int32 diamond = 2;
    optional op_pkt_def.PKT_Level level = 3;
    optional op_def.ValueBar energy = 4;
    optional op_def.OpCommand command = 5[default=OP_COMMAND_PATCH];
    repeated EditModeRoom rooms = 6; // 自己的房间信息
}


```

## 2020.07.11
Purpose:
- 道具使用

```proto

message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_USE_ITEM
{
    optional string item_id = 1;
    optional int32 count = 2;
}
```

## 2020.07.14

Purpose:
- 手持物

```proto

// 服务端返回客户端玩家自身信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SELF_PLAYER_INFO  // 我的信息
{
    optional int32 id = 1; // sprite id
    optional string cid = 2; // 玩家角色id, 展示
    optional int32 like = 3[default=0]; // 点赞
    optional op_pkt_def.PKT_Avatar current_avatar = 4;
    optional string nickname = 5; // 昵称
    optional op_pkt_def.PKT_Level level = 6; // 等级
    optional string current_title = 7; // 当前称号
    repeated op_pkt_def.PKT_Skill life_skills = 8;
    repeated op_pkt_def.PKT_Badge badges = 9;  // 徽章
    repeated op_pkt_def.PKT_Title titles = 10; // 称号
    repeated op_pkt_def.PKT_Property properties = 11;
    optional CountablePackageItem handheld = 12; // 新增玩家当前手持物
}


// 更换手持物
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_PICK_HANDHELD
{
    optional string id = 1; // 手持物id
}

// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_DROP_HANDHELD 清空手持物



// 请求手持物列表
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_HANDHELD

// 返回手持物列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_HANDHELD
{
    optional string current_handheld_id = 1;
    repeated CountablePackageItem handheld = 2;
}



// 添加推荐系数  以及最后使用时间 手持物排序
message CountablePackageItem
{
    required string id = 1;
    required int32 count = 2;
    optional string name = 3;  // 道具名称
    optional string des  = 4;
    optional op_gameconfig.Display display = 5; // 背包物件贴图
    optional string source = 6;
    optional string short_name = 7;
    optional string category = 8;
    optional string subcategory = 9;
    optional op_gameconfig.Price selling_price = 10;  // 售价
    optional bool tradable = 11[default=false];   // 是否可以与玩家交易
    optional bool recyclable = 12[default=false]; // 是否可被系统回收
    optional bool executable = 13[default=false]; // 是否可以使用
    optional string quality = 14;   // 品质
    optional int32 needed_count = 15;  // 所需数量, 例如 配方中需要的数量
    optional string indexId = 16;
    optional op_gameconfig.Avatar avatar = 17;
    optional op_pkt_def.PKT_Subscript right_subscript = 18[default=PKT_SUBSCRIPT_UNSET]; // 右角标
    optional string use_button_name = 19[default="使用"];
    optional float latest_use = 20;   // 最后使用时间戳
    optional int32 recommended = 21;  // 推荐系数 由策划提供，推荐值越高，排位越靠前，具体以相应ui文档为准
}


```


## 2020.07.28

Purpose:
- 开店

```proto

message PKT_ROOM_MODEL
{
    optional string storeType = 1;  // 店铺模板类型
    optional string des = 3;        // 店铺模板说明
    optional string modelId = 4;    // 店铺模板id
    optional op_gameconfig.Price price = 5;
    optional string name = 6;
}

message PKT_INDUSTRY
{
    optional string industryType = 5;
    optional string name = 1;  // 行业名称
    repeated PKT_ROOM_MODEL room_models = 2;
    // 当前行业buff描述
}



// 客户端请求开店模板 
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_INDUSTRY_MODELS


// 服务端返回开店模板
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_INDUSTRY_MODELS
{
    repeated op_pkt_def.PKT_INDUSTRY industry = 1;
}


// 请求开店
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CREATE_STORE
{
    optional string modelId = 1;
}
```

## 2020.07.28

Purpose:
- 店铺列表

```proto
// 请求我的店铺
// _OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_MY_STORE


// 返回我的店铺
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_MY_STORE
{
    optional int32 store_limit = 1;
    repeated EditModeRoom store_list = 2; //
}

// 请求商业街
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_COMMERCIAL_STREET
{
    optional string store_type = 1;
    optional string sorted_by = 2; // popularity vote
}



// 返回商业街
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_COMMERCIAL_STREET
{
    repeated EditModeRoom commercial_street = 1;
}


```

## 2020.08.04

Purpose:
- 营销计划


```proto
message MarketPlan
{
    optional string id = 1; // 计划id
    repeated CountablePackageItem requirements = 2; // 所需物品
    optional string des = 3; // 描述
    optional string icon = 4;//  图标
    optional string name = 5; // ,名称
    optional string buff_des = 6; // 描述2 对应effect
    optional string market_plan_type = 7; // 类型
    optional int32 total_time = 8; // 总耗时 单位秒
    optional int32 end_time = 9; // 结束时间 时间戳 单位秒
}


message MarketPlanPair
{
    optional string market_plan_type = 1; // 营销计划类型
    optional MarketPlan market_plan = 2;
}


// 请求该店铺在用的营销计划
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_MARKET_PLAN
{
    optional string room_id = 1;
}

// 返回已经选择的营销计划, 必然存在market_plan_type,但是market_plan可能为空,即策划设定上 同类型的营销计划只能开启一个,未开启时槽位显示空
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_MARKET_PLAN
{
    repeated MarketPlanPair market_plan_pairs = 1;
}

// 请求某个类型可选的营销计划
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_MARKET_PLAN_MODELS_BY_TYPE
{
    optional string market_plan_type = 1;
}

// 返回可供选择的营销计划
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_MARKET_PLAN_MODELS_BY_TYPE
{
    repeated MarketPlan market_plan = 1;
}

// 开启某个营销计划
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SELECT_MARKET_PLAN
{
    optional string room_id = 1;
    optional string market_plan_id = 2;
}


```

## 2020.08.05

Purpose:
- 新增行业buff描述


```proto
message PKT_INDUSTRY
{
    optional string industryType = 5;
    optional string name = 1;  // 行业名称
    repeated PKT_ROOM_MODEL room_models = 2;
    optional string state = 3;  // 状态,buff附加
    optional string des = 4;    // 目前状态描述
    optional string buff_des = 6;   // buff本身描述
}


message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_MARKET_PLAN
{
    repeated MarketPlanPair market_plan_pairs = 1;
    optional string industry_background = 2; // 底图
    optional string industry_des = 3;        // 描述
    optional string industry_buff_des = 4;   // buff描述
}
```



## 2020.08.06
Purpose:
- 店铺排行榜


```proto
message PKT_RankChampion
{
    optional string name = 1;
    optional string champion = 2;
    optional string type = 3; // 行业类型  按规则拼底图
    optional string key = 4; // 排行数值
}


message PKT_StoreRankItem
{
    optional string name = 1;
    optional string owner_name = 2;
    optional int32 index = 3;  // 排名, -1表示不在榜内 
    optional string store_type = 4;
    optional string industry_type = 5;
    optional int32 value = 6;
}

message PKT_RewardStage
{
    optional uint32 start = 1; //  表示阶段闭区间 start= 4, end=10,表示[4, 10],表示4~10
    optional uint32 end = 2; //
    optional CountablePackageItem rewards = 3;
}


// 请求排行榜列表
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_STORE_RANKING_LIST


// 返回排行榜列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_STORE_RANKING_LIST
{
    repeated op_pkt_def.PKT_RankChampion rank_champions = 2;
}


// 请求某一排行榜详情
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_STORE_RANKING_DETAIL
{
    optional string key = 1;
    optional string type = 2; // 类型, 目前为行业类型
}

// 返回排行详情
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_STORE_RANKING_DETAIL
{
    optional string key = 1;
    optional string type = 2;
    repeated op_pkt_def.PKT_StoreRankItem stores = 3;  // 排行店铺
    optional op_pkt_def.PKT_StoreRankItem player_store = 4; // 自己的店铺
}


// 请求排行榜奖励列表
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_STORE_RANKING_REWARD
{
    optional string key = 1;
    optional string type = 2; // 类型, 目前为行业类型
}

// 返回奖励列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_STORE_RANKING_REWARD
{
    repeated PKT_RewardStage reward_stage = 1;
}

```



## 2020.08.06
Purpose:
- 店铺访问记录

```proto
// 请求店铺访问历史
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_STORE_ENTER_HISTORY


// 返回店铺访问记录
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_STORE_ENTER_HISTORY
{
    repeated EditModeRoom history = 1;
}

```



## 2020.08.10

Purpose:
- 房间点赞


```proto
// 原有房间信息上新增praise,has_praise
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODE_ROOM_INFO
{
    required string room_id = 1;
    optional string name = 2;
    optional string password = 3;
    optional op_def.EditModeRoomPrivacy privacy = 4;
    optional string owner_name = 5;
    optional int32 player_count = 6;
    optional op_pkt_def.PKT_Level room_level = 7;
    optional string room_type = 8;
    optional int32 praise = 9;     // 点赞数量
    optional bool has_praise = 10; // 是否已经点赞
}


// 对房间点赞, 点赞后服务器将更新房间信息,即 上面那条协议
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_PRAISE_ROOM
{
    optional string room_id = 1;
    optional bool praise = 2; // ture表示点赞 false 取消点赞
}

```


## 2020.08.19

Purpose:
- 背包更新

```proto

enum PKT_PackageType // 背包类型
{
    MinePackage = 1;
    FurniturePackage = 2;
    PropPackage = 3;
    AvatarPackage = 4;
    EditFurniturePackage = 5; // 小屋编辑模式下的背包
}

// 请求同步背包
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SYNC_PACKAGE
{
    optional op_pkt_def.PKT_PackageType packageName = 1;
}

// 服务端同步背包
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SYNC_PACKAGE
{
    optional op_pkt_def.PKT_PackageType packageName = 1;
    repeated CountablePackageItem items = 2;
    optional op_def.Packet packet = 3;  // 服务端将背包按32(暂定)个组发送, packet内包含 当前是第几个包以及一共有几个包的数据
}

// 部分更新背包
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_UPDATE_PACKAGE
{
    optional op_pkt_def.PKT_PackageType packageName = 1;
    repeated CountablePackageItem items = 2;  // 服务器推送更新将在第一次PKT_SYNC_PACKAGE之后开始, 应为proto不能使用undefined的定义 需要删除的项目id为0
}



```



## 2020.08.20
- 房间搜索


```proto

message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SEARCH_ROOM
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional string query = 3; // 模糊查找字段
}

message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SEARCH_ROOM
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    repeated EditModeRoom rooms = 3;
}

```

## 2020.08.27
- 好友信息

```proto
// 请求好友信息列表
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_PLAYER_LIST
{
    repeated string platform_ids = 1;
}

message PKT_PlayerInfo
{
    optional string nickname = 1;
    optional string platform_id = 2;
    optional PKT_Level level = 3;
}


// 返回好友信息列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_PLAYER_LIST
{
    repeated op_pkt_def.PKT_PlayerInfo player_infos = 1;
}


// 请求好友信息
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_ANOTHER_PLAYER_INFO
{
    optional string platform_id = 1;
}


// 返回好友信息
// 服务端返回客户端其他玩家信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_ANOTHER_PLAYER_INFO // 他人信息
{
    optional int32 id = 1; // sprite id
    optional string cid = 2;  // 玩家角色id, 展示
    optional int32 like = 3[default=0]; // 点赞
    optional op_pkt_def.PKT_Avatar current_avatar = 4;
    optional string nickname = 5; // 昵称
    optional op_pkt_def.PKT_Level level = 6; // 等级
    optional string current_title = 7; // 当前称号
    repeated op_pkt_def.PKT_Skill life_skills = 8;
    repeated op_pkt_def.PKT_Badge badges = 9;  // 徽章
    repeated op_pkt_def.PKT_Title titles = 11; // 称号
    repeated string room_ids = 12; // 房间列表 功能待定 字段预留
    optional string remark = 13; // 备注名
    optional bool friend = 14; // 是否为好友
    repeated op_pkt_def.PKT_Property properties = 15;

}


// 搜索玩家 -- 目前为名称模糊搜索
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_SEARCH_PLAYER
{
    optional string name = 1;
}

// 返回搜索结果
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SEARCH_PLAYER
{
    repeated op_pkt_def.PKT_PlayerInfo player_infos = 1;
}


```



## 2020.08.31
- 邀请好友 跟随好友

```proto

enum PKT_PlayerInteraction
{
    PKT_invitePlayer = 1;
    PKT_tracePlayer = 2;
}


// 与好友交互
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_PLAYER_INTERACTION
{
    optional op_pkt_def.PKT_PlayerInteraction method = 1;
    optional op_def.GeneralParam param = 2;
    // 邀请好友 GeneralParamType 为str, val_str 为platformId
    // 跟随好友 GeneralParamType 为str, val_str 为platformId
}
```


## 2020.09.05
- 


```proto
// 请求订单列表
// _OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_ORDER_LIST


//返回订单信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_ORDER_LIST
{
    repeated PKT_Quest orders = 1; //
    optional op_def.ValueBar royal_order_limit = 2; // 皇家订单数量
}


enum PKT_Order_Operator
{
    PKT_ORDER_DELIVERY = 0; // 送货
    PKT_ORDER_DELETE = 2; // 删除
    PKT_ORDER_SPEED_UP = 1; // 加速
    PKT_ORDER_GET_REWARD = 3; // 领取奖励
}
// 改变订单状态
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CHANGE_ORDER_STAGE
{
    optional op_pkt_def.PKT_Order_Operator op = 1;
    optional int32 index = 2; // 要改变状态的订单的索引
}




// 请求订单进度
// _OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_PLAYER_PROGRESS



message PKT_Progress
{
    optional int32 target_value = 1;
    repeated CountablePackageItem rewards = 2;
    optional bool received = 3;
}

// 返回订单进度
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_PLAYER_PROGRESS
{
    optional int32 current_progress_value = 1;
    repeated PKT_Progress steps = 2;
// name 之后可能会有各种人物进度有枚举区分 暂时就用订单进度
}


// 领取订单进度奖励
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_TAKE_PLAYER_PROGRESS_REWARD
{
    optional int32 index = 1;
}
```

## 2020.09.11
- 打工协议


```proto

// 请求工作列表 目前没有需要 是ui打开的
//OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_JOB_LIST  

// 返回工作列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_JOB_LIST
{
    optional PKT_Quest jobs = 1;
    optional int32 times = 2;
}

// 打工
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_WORK_ON_JOB
{
    optional string id = 1;
}

```


## 2020.09.15
- 房间折旧

```proto


// 请求房间翻新材料
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_QUERY_ROOM_REFURBISH_REQUIREMENTS
{
    optional string id = 1;
}

// 返回翻新房间材料
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_QUERY_ROOM_REFURBISH_REQUIREMENTS
{
    repeated CountablePackageItem requirements = 1;
}

// 翻新房间
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_ROOM_REFURBISH
{
    optional string id = 1;
}


```



## 2020.09.27
- 道具描述, 具体需求http://jira.apowo.com:8080/browse/PH-552

```proto
// 将在玩家信息同步属性值
message OP_VIRTUAL_WORLD_REQ_CLIENT_PKT_PLAYER_INFO
{

    optional int32 coin = 1; // 之后将作废,使用properties
    optional int32 diamond = 2;  // 之后将作废,使用properties
    optional op_pkt_def.PKT_Level level = 3; // 之后将作废,使用properties
    optional op_def.ValueBar energy = 4;  // 之后将作废,使用properties
    optional op_def.ValueBar workChance = 9;  // 之后将作废,使用properties
    optional op_def.OpCommand command = 5[default=OP_COMMAND_PATCH];
    repeated EditModeRoom rooms = 6;
    optional CountablePackageItem handheld = 7;
// 新增玩家属性值, 包含人物7种属性,银币,钻石,打工次数,精力,饱食度,等级等信息
    repeated op_pkt_def.PKT_Property properties = 8;
}


enum CompareType
{
    eq = 1; // 等于
    ne = 2; // 不等于
    le = 3; // 小于等于
    lt = 4; // 小于
    ge = 5; // 大于等于
    gt = 6; // 大于
}


message PKT_CompareValue
{
    optional string key = 1; // 与properties的id对应
    optional double value = 2;  // 数值
    optional op_def.CompareType compare_type = 3;
}

// 
message CountablePackageItem
{
    repeated op_pkt_def.PKT_CompareValue require_values = 27; // 新增 需要的属性值,具体信息和人物的properties对比
    repeated op_pkt_def.PKT_CompareValue affect_values = 28;  // 使用后会影响的属性值
}




```


## 2020.10.10
- 派对

```proto
// 请求开启派对面板
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CREATE_PARTY_REQUIREMENTS
{
    optional string id = 1;
}

// 返回开启派对面板
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_CREATE_PARTY_REQUIREMENTS
{
    repeated op_pkt_def.PKT_Property topics = 1;
    optional int32 tickets_count = 2;
}

// 请求开启派对
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_CREATE_PARTY
{
    optional string id = 1;
    optional string topic = 2;
    optional string name = 3;
    optional string des = 4;
    optional int32 ticket_count = 5;
}


// 请求派对列表
// OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_PARTY_LIST

// 返回派对列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_PARTY_LIST
{
    optional EditModeRoom hotel = 1;
    optional EditModeRoom picatown = 2;
    repeated EditModeRoom party = 3;
}

```

## 2020.10.21
- 
```proto
// 按sn 请求物件解锁资源
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_REQUIRE_FURNITURE_UNFROZEN_REQUIREMENTS
{
    repeated string sn = 1;
}


// 服务器返回物件解锁资源
message FurnitureRequirements
{
    optional string sn = 1;
    repeated CountablePackageItem requirements = 2;
}

message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_REQUIRE_FURNITURE_UNFROZEN_REQUIREMENTS
{
    repeated FurnitureRequirements furniture_requirements = 1;
}


```


## 2020.10.30
- 庄园api

```proto

// 请求街区列表
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_STREET_LIST
{
    optional int32 page = 1;
    optional int32 per_page = 2;
}


message EditModeRoom
{
.....
    optional int32 manor_count = 17; // 庄园已购买数量
    optional int32 manor_limit = 18; // 庄园可购买数量
}


// 返回街区列表
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_STREET_LIST
{
    optional int32 page = 1;
    optional int32 per_page = 2;
    optional int32 max_page = 3;
    repeated EditModeRoom street = 4;
}


// 请求购买/编辑庄园
message OP_CLIENT_REQ_VIRTUAL_WORLD_PKT_MODIFY_MANOR
{
    optional string room_id = 1;
    optional int32 index = 2;
    optional op_pkt_def.PKT_MANOR_OP op = 3;
}


```


## 2020.11.18
-- avatar


```proto
// 道具新增属性
message CountablePackageItem
{
    // ....
    optional string suit_type = 29;
    optional string sn = 30;
}


// 
message PKT_SUIT_GROUP
{
    repeated CountablePackageItem avatar_suit = 1;
}
// 服务器返回创角面板 新增
message OP_VIRTUAL_WORLD_REQ_CLIENT_SHOW_CREATE_ROLE_UI
{
    // 
    repeated PKT_SUIT_GROUP avatar_suits = 4;
}

// 创角请求
message OP_CLIENT_REQ_VIRTUAL_WORLD_CREATE_ROLE
{
    required string name = 1;
    required op_gameconfig.Avatar avatar = 2; // 原来直接给avatar结构 ,作废 
    optional int32 index = 3;   // 请求avatar_suits的索引
}


// 商品新增属性
message MarketCommodity
{
    // ....
    optional string sn = 16;
    optional string suit_type = 17;
}

// 个人面板
// 服务端返回客户端玩家自身信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_SELF_PLAYER_INFO  // 我的信息
{
    repeated CountablePackageItem avatar_suit = 14;
}

// 服务端返回客户端其他玩家信息
message OP_VIRTUAL_WORLD_RES_CLIENT_PKT_ANOTHER_PLAYER_INFO // 他人信息
{
    repeated CountablePackageItem avatar_suit = 16;
}


// 场景内人物
message Sprite {
    optional op_gameconfig.Avatar avatar = 3; // 保留
    
    repeated op_def.StrPair attrs = 15;  // 当attrs内存在 TQ_AVATAR_SUITS时, 解析内容 '[{id="IA0001", sn="sfsdfse", suit_type="costume"}]'
}


```

## 2020.12.04
-- 添加TitleMask


```proto
message Sprite {
    optional uint32 title_mask = 19; // op_def.TitleMask.TQ_NickName << 16; 后16位由用户定义
}

// title_mask通过与op_def.TitleMask位运算获得展示情况

```


## 2021.2.20
-- 编辑模式调整为前端编辑

```proto


// _OP_CLIENT_REQ_VIRTUAL_WORLD_START_EDIT_MODEL  客户端请求开始编辑模式        = 0x00162100; // 开始编辑

// 服务端返回是否可以进入编辑模式 可能会有权限限制  不是所有人都有编辑权限的
message OP_VIRTUAL_WORLD_RES_CLIENT_START_EDIT_MODEL
{
    optional bool status = 1; // 是否成功
    optional string msg = 2; // 描述
}

// 如果可以进入编辑模式, 服务端将同步相关的所有Sprite对象   消息是拆分多个包的逻辑和add_sprite拆分包的逻辑一样
message OP_VIRTUAL_WORLD_REQ_CLIENT_CURRENT_SCENE_ALL_SPRITE {
    repeated Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
    optional op_def.Packet packet = 3;
}



/////////// 编辑完成后 客户端发起同步编辑结果

// _OP_CLIENT_REQ_VIRTUAL_WORLD_SYNC_EDIT_MODEL_RESULT = 0x00162101; // 同步编辑结果

message SpriteModifyResult
{
    required int32 id = 1; // id 不可以小于等于0
    optional op_def.PBPoint3f point3f = 2;  // 有改过坐标或者是新增物件就填 没有就不填
    optional string current_animation_name = 4; // 改过朝向 或者动画就填写 否则空
    optional op_def.Direction direction = 5; //改过朝向 或者动画就填写 否则空, 默认3,即面向玩家. 动画
    optional string sn = 6;
    optional int32 command_mask = 7; // 0xffff代表删除, 0x0001代表新增, 0x0002代表修改,  删除操作高于所有, 即假定已经收回的物件 重新摆放出来是新的id
    // 即 1代表新增 2代表修改 3代表新增并更改  0xffff代表删除
    // 每次 操作对command_mask进行或运算即可
}

message OP_CLIENT_REQ_VIRTUAL_WORLD_SYNC_EDIT_MODEL_RESULT
{
    repeated op_client.CountablePackageItem item = 1;  // furniturePackage的item, 只需要给id和count就好了, 如果可以就只给有修改的数据 否则全部给也行
    repeated op_client.SpriteModifyResult sprite = 2;
    // 地块需要拆出来其他逻辑吗?
    optional op_def.Packet packet = 3;  // 此次同步一共有几个包, 当前为第几
}



//    _OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODEL_RESULT           = 0x00142100; // 返回客户端编辑结果
message OP_VIRTUAL_WORLD_RES_CLIENT_EDIT_MODEL_RESULT
{
    optional bool status = 1; // 是否成功
    optional string msg = 2; // 描述
}

// 如果编辑成功 服务端将会广播重新加载场景的消息   消息是拆分多个包的逻辑和add_sprite拆分包的逻辑一样
message OP_VIRTUAL_WORLD_REQ_CLIENT_NOTICE_RELOAD_SCENE
{
    repeated Sprite sprites = 1;
    optional op_def.NodeType node_type = 2;
    optional op_def.Packet packet = 3;
}
```
