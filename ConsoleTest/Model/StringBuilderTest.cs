using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BenchmarkDotNet.Attributes;

namespace ConsoleTest.Model
{
    [MemoryDiagnoser]
    public class StringBuilderTest
    {
        private const int Times = 100000;

        private static readonly string Format = "fsafjowejfowefjsdkl{0}fljfklkwfkwf{1}sfjdsjowjofjf{2}";
        private static readonly object[] Arguments = new object[] { "sfdsjfkjew", 458, new List<int>() { 12, 333 } };

        [Benchmark]
        public void StringFormat()
        {
            for (var i = 0; i < Times; i++)
            {
                string text = string.Format(Format, Arguments);
            }
        }

        [Benchmark]
        public void StringBuilderFormat()
        {
            for (var i = 0; i < Times; i++)
            {
                var builder = new StringBuilder();
                builder.AppendFormat(Format, Arguments);
            }
        }
    }
}
