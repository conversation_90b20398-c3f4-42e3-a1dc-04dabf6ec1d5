using ILogger = Grpc.Core.Logging.ILogger;

namespace GrpcBase
{
    public class GrpcLogger: ILogger
    {
        public static GrpcLogger Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new GrpcLogger();
                }

                return _instance;
            }
        }

        private static GrpcLogger? _instance;

        public ILogger ForType<T>()
        {
            return Instance;
        }
        
        /// <summary>Logs a message with severity Debug.</summary>
        public void Debug(string message)
        {
            Logger.Debug.Write(message);
        }

        /// <summary>Logs a formatted message with severity Debug.</summary>
        public void Debug(string format, params object[] formatArgs)
        {
            Logger.Debug.Write(format, formatArgs);
        }

        /// <summary>Logs a message with severity Info.</summary>
        public void Info(string message)
        {
            Logger.GrpcAccepted.Write(message);
        }

        /// <summary>Logs a formatted message with severity Info.</summary>
        public void Info(string format, params object[] formatArgs)
        {
            Logger.Grpc.Write(format, formatArgs);
        }

        /// <summary>Logs a message with severity Warning.</summary>
        public void Warning(string message)
        {
            Logger.Warning.Write("grpc message:{0}", message);
        }

        /// <summary>Logs a formatted message with severity Warning.</summary>
        public void Warning(string format, params object[] formatArgs)
        {
            Logger.Warning.Write("grpc message:{0}", string.Format(format, formatArgs));
        }

        /// <summary>Logs a message and an associated exception with severity Warning.</summary>
        public void Warning(Exception exception, string message)
        {
            Logger.Warning.Write("grpc message:{0} error:{1}", message, exception);
        }

        /// <summary>Logs a message with severity Error.</summary>
        public void Error(string message)
        {
            Logger.Error.Write("grpc message:{0}", message);
        }

        /// <summary>Logs a formatted message with severity Error.</summary>
        public void Error(string format, params object[] formatArgs)
        {
            Logger.Error.Write("grpc message:{0}", string.Format(format, formatArgs));
        }

        /// <summary>Logs a message and an associated exception with severity Error.</summary>
        public void Error(Exception exception, string message)
        {
            Logger.Error.Write("grpc message:{0} error:{1}", message, exception);
        }
    }    
}

