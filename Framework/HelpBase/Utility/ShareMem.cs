using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.Linq;
using System.Threading;

namespace HelpBase
{
    public class ShareMem
    {
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr SendMessage(IntPtr hWnd, int Msg, int wParam, IntPtr lParam);

        [DllImport("Kernel32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr CreateFileMapping(int hFile, IntPtr lpAttributes, uint flProtect, uint dwMaxSizeHi, uint dwMaxSizeLow, string lpName);
       
        [DllImport("Kernel32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr OpenFileMapping(int dwDesiredAccess,[MarshalAs(UnmanagedType.Bool)] bool bInheritHandle,string lpName);

        [DllImport("Kernel32.dll", CharSet = CharSet.Auto)]
        public static extern IntPtr MapViewOfFile(IntPtr hFileMapping,uint dwDesiredAccess, uint dwFileOffsetHigh, uint dwFileOffsetLow,uint dwNumberOfBytesToMap);

        [DllImport("Kernel32.dll", CharSet = CharSet.Auto)]
        public static extern bool UnmapViewOfFile(IntPtr pvBaseAddress);

        [DllImport("Kernel32.dll", CharSet = CharSet.Auto)]
        public static extern bool CloseHandle(IntPtr handle);

        [DllImport("kernel32", EntryPoint="GetLastError")]
        public static extern int GetLastError ();

        const int ERROR_ALREADY_EXISTS = 183;

        const int FILE_MAP_COPY = 0x0001;
        const int FILE_MAP_WRITE = 0x0002;
        const int FILE_MAP_READ = 0x0004;
        const int FILE_MAP_ALL_ACCESS = 0x0002 | 0x0004;

        const int PAGE_READONLY = 0x02;
        const int PAGE_READWRITE = 0x04;
        const int PAGE_WRITECOPY = 0x08;
        const int PAGE_EXECUTE = 0x10;
        const int PAGE_EXECUTE_READ = 0x20;
        const int PAGE_EXECUTE_READWRITE = 0x40;

        const int SEC_COMMIT = 0x8000000;
        const int SEC_IMAGE = 0x1000000;
        const int SEC_NOCACHE = 0x10000000;
        const int SEC_RESERVE = 0x4000000;

        const int INVALID_HANDLE_VALUE = -1;

        IntPtr m_hSharedMemoryFile = IntPtr.Zero;
        IntPtr m_pwData = IntPtr.Zero;

        public IntPtr PwData
        {
            get { return m_pwData; }
            set { m_pwData = value; }
        }
        bool m_bAlreadyExist = false;
        bool m_bInit = false;
        long m_MemSize=0;

        public ShareMem()
        {
        }
        ~ShareMem()
        {
            Close();
        }

        /// <summary>
        /// 初始化共享内存
        /// </summary>
        /// <param name="strName">共享内存名称</param>
        /// <param name="lngSize">共享内存大小</param>
        /// <returns></returns>
        public int Init(string strName, long lngSize)
        {
            if (lngSize <= 0 || lngSize > 0x00800000) lngSize = 0x00800000;
            m_MemSize = lngSize;
            if (strName.Length > 0)
            {
                //创建内存共享体(INVALID_HANDLE_VALUE)
                m_hSharedMemoryFile = CreateFileMapping(INVALID_HANDLE_VALUE, IntPtr.Zero, (uint)PAGE_READWRITE, 0, (uint)lngSize, strName);
                if (m_hSharedMemoryFile == IntPtr.Zero)
                {
                    m_bAlreadyExist = false;
                    m_bInit = false;
                    return 2; //创建共享体失败
                }
                else
                {
                    if (GetLastError() == ERROR_ALREADY_EXISTS)  //已经创建
                    {
                        m_bAlreadyExist = true;
                    }
                    else                                         //新创建
                    {
                        m_bAlreadyExist = false;
                    }
                }
                //---------------------------------------
                //创建内存映射
                m_pwData = MapViewOfFile(m_hSharedMemoryFile, FILE_MAP_WRITE, 0, 0, (uint)lngSize);
                if (m_pwData == IntPtr.Zero)
                {
                    m_bInit = false;
                    CloseHandle(m_hSharedMemoryFile);
                    return 3; //创建内存映射失败
                }
                else
                {
                    m_bInit = true;
                    if (m_bAlreadyExist == false)
                    {
                        //初始化
                    }
                }
                //----------------------------------------
            }
            else
            {
                return 1; //参数错误    
            }

            return 0;     //创建成功
        }
        /// <summary>
        /// 关闭共享内存
        /// </summary>
        public void Close()
        {
            if (m_bInit)
            {
                UnmapViewOfFile(m_pwData);
                CloseHandle(m_hSharedMemoryFile);
            }
        }

        /// <summary>
        /// 读数据
        /// </summary>
        /// <param name="bytData">数据</param>
        /// <param name="lngAddr">起始地址</param>
        /// <param name="lngSize">个数</param>
        /// <returns></returns>
        public int Read(ref byte[] bytData, int lngAddr, int lngSize)
        {
            if (lngAddr + lngSize > m_MemSize) return 2; //超出数据区
            if (m_bInit)
            {              
                Marshal.Copy(m_pwData, bytData, lngAddr, lngSize);
            }
            else
            {
                return 1; //共享内存未初始化
            }
            return 0;     //读成功
        }

        /// <summary>
        /// 写数据
        /// </summary>
        /// <param name="bytData">数据</param>
        /// <param name="lngAddr">起始地址</param>
        /// <param name="lngSize">个数</param>
        /// <returns></returns>
        public int Write(byte[] bytData, int lngAddr, int lngSize)
        {
            if (lngAddr + lngSize > m_MemSize) return 2; //超出数据区
            if (m_bInit)
            {
                Marshal.Copy(bytData, lngAddr, m_pwData, lngSize);
            }
            else
            {
                return 1; //共享内存未初始化
            }
            return 0;     //写成功
        }
    }

    public static class ShareMemManager
    {
        public static ShareMem MemDB = new ShareMem();
        static ShareMemManager()
        {
            MemDB.Init("YFMemTest", 10240);
            //TimerManager.AddToTimer(200, new TimeAction(OnTimerChecked));
        }

        static void OnTimerChecked()
        {
        }
    }

    public class Win32API
    {
        public const int USER = 0x0400;
        public const int WM_TEST = USER + 101;
        public const int WM_MSG = USER + 102;
        public const int WM_COPYDATA = 0x004A;

        [DllImport("User32.dll", EntryPoint = "FindWindow")]
        public static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("User32.dll", EntryPoint = "FindWindowEx")]
        public static extern IntPtr FindWindowEx(IntPtr hwndParent, IntPtr hwndChildAfter, string lpClassName, string lpWindowName);
        //消息发送API
        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hWnd,        // 信息发往的窗口的句柄
            int Msg,            // 消息ID
            int wParam,         // 参数1
            IntPtr lParam          //参数2
        );

        [DllImport("user32", EntryPoint = "GetWindowThreadProcessId")]
        public static extern int GetWindowThreadProcessId(IntPtr hwnd, out int pid);
        //消息发送API
        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hWnd,        // 信息发往的窗口的句柄
            int Msg,            // 消息ID
            int wParam,         // 参数1
            ref CopyDataStruct lParam  //参数2
        );

        //消息发送API
        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hWnd,        // 信息发往的窗口的句柄
            int Msg,            // 消息ID
            int wParam,         // 参数1
            ref CopyStringDataStruct lParam  //参数2
        );

        //消息发送API
        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        public static extern int SendMessage(
            IntPtr hWnd,        // 信息发往的窗口的句柄
            int Msg,            // 消息ID
            int wParam,         // 参数1
            ref CommandStruct lParam  //参数2
        );

        //消息发送API
        [DllImport("User32.dll", EntryPoint = "PostMessage")]
        public static extern int PostMessage(
            IntPtr hWnd,        // 信息发往的窗口的句柄
            int Msg,            // 消息ID
            IntPtr wParam,         // 参数1
            IntPtr lParam            // 参数2
        );
        /// <summary>
        /// 使用COPYDATASTRUCT来传递字符串
        /// </summary>
        [StructLayout(LayoutKind.Sequential)]
        public struct CopyStringDataStruct
        {
            public IntPtr dwData;

            public int cbData;

            [MarshalAs(UnmanagedType.LPWStr)]
            public string lpData;
        }

        public struct CopyDataStruct
        {
            public IntPtr dwData;
            public int cbData;
            public IntPtr lpData;
        }

        /// <summary>
        /// 自定义的结构
        /// </summary>
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct CommandStruct
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string Command;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string Content;
        }

        /// <summary>
        /// SendMessage To Window
        /// </summary>
        /// <param name="windowName">window的title，建议加上GUID，不会重复</param>
        /// <param name="strMsg">要发送的字符串</param>
        public static void SendMessage(string windowName, string strMsg)
        {
            if (strMsg == null)
                return;

            IntPtr hwnd = FindWindow(null, windowName);
            if (hwnd != IntPtr.Zero)
            {
                SendMessage(hwnd, strMsg);
            }
        }

        /// <summary>
        /// SendMessage To Window
        /// </summary>
        /// <param name="windowName">window的title，建议加上GUID，不会重复</param>
        /// <param name="strMsg">要发送的字符串</param>
        public static void SendMessageByProcess(string processName, string strMsg)
        {
            if (strMsg == null) return;

            var process = Process.GetProcessesByName(processName);
            if (process.FirstOrDefault() == null) return;
            var hwnd = process.FirstOrDefault().MainWindowHandle;
            if (hwnd == IntPtr.Zero) return;

            if (hwnd != IntPtr.Zero)
            {
                SendMessage(hwnd, strMsg);

            }
        }

        public static void GetData(string text, out CopyStringDataStruct dsPtr)
        {
            dsPtr = new CopyStringDataStruct();
            dsPtr.dwData = IntPtr.Zero;
            dsPtr.cbData = Encoding.Unicode.GetByteCount(text) + 1;
            dsPtr.lpData = text;
        }

        public static IntPtr GetData(byte[] sbs)
        {
            CopyDataStruct cds = new CopyDataStruct();

            cds.dwData = IntPtr.Zero;
 
            int len = sbs.Length;
            cds.cbData = len;

            //cds.lpData = strMsg;
            cds.lpData = Marshal.AllocHGlobal(len);
         
            Marshal.Copy(sbs, 0, cds.lpData, len);
            IntPtr iPtr = Marshal.AllocHGlobal(Marshal.SizeOf(cds));
            Marshal.StructureToPtr(cds, iPtr, true);

            //注意：长度为字节数

            Marshal.FreeCoTaskMem(cds.lpData);
            Marshal.FreeCoTaskMem(iPtr);
            return iPtr;
        }

        public static int SendMessage(IntPtr hwnd, string message)
        {
            // 消息来源窗体
            int fromWindowHandler = 0;
            CopyStringDataStruct dsPtr;
            GetData(message, out dsPtr);
            return SendMessage(hwnd, WM_COPYDATA, fromWindowHandler, ref dsPtr);
        }

        public static void SendMessage(IntPtr hwnd, byte[] msg)
        {
            var iPtr = GetData(msg);
            // 消息来源窗体
            int fromWindowHandler = 0;
            SendMessage(hwnd, WM_COPYDATA, fromWindowHandler, iPtr);
        }
    }

    public class ProcessMessageHelper
    {
        [DllImport("kernel32.dll")]
        static extern bool ReadProcessMemory(IntPtr hProcess, IntPtr lpBaseAddress, byte[] buff, uint nSize, ref uint lpNumberOfBytesRead);

        public static void SendMessage(IntPtr hwndTest, byte[] msg)
        {
            Win32API.SendMessage(hwndTest, msg); 
        }

        public static int SendMessage(IntPtr hwndTest, string msg)
        {
            return Win32API.SendMessage(hwndTest, msg);
        }

        public static void SendMessageByProcessName(string processName, string msg = "test")
        {
            var pss = Process.GetProcessesByName(processName);
            if (pss.Length == 0)
                return;

            Win32API.CopyStringDataStruct dsPtr;
            Win32API.GetData(msg, out dsPtr);
            foreach (var ps in pss)
            {
                Win32API.SendMessage(ps.Handle, Win32API.WM_COPYDATA, 5, ref dsPtr);//传递整型参数和不定长的字符串成功
            }
        }

        public static void SendCommand(IntPtr hwndTest, string command, string content)
        {
            Win32API.CommandStruct mds = new Win32API.CommandStruct();
            mds.Command = command;
            mds.Content = content;
            if (hwndTest != (IntPtr)0)
            {
                int size = Marshal.SizeOf(mds);
                IntPtr ip = Marshal.AllocHGlobal(size);
                Marshal.StructureToPtr(mds, ip, false);
                byte[] tb = new byte[size];
                Marshal.Copy(ip, tb, 0, size);
                Marshal.FreeHGlobal(ip);
                ShareMemManager.MemDB.Write(tb, 0, size);
                Win32API.SendMessage(hwndTest, Win32API.WM_MSG, 5, ref mds);
            }
        }

        public static void SendCommandByProcessName(string processName, string command, string content)
        {
            Win32API.CommandStruct mds = new Win32API.CommandStruct();
            mds.Command = command;
            mds.Content = content;

            int size = Marshal.SizeOf(mds);
            IntPtr ip = Marshal.AllocHGlobal(size);
            Marshal.StructureToPtr(mds, ip, false);
            byte[] tb = new byte[size];
            Marshal.Copy(ip, tb, 0, size);
            Marshal.FreeHGlobal(ip);
            ShareMemManager.MemDB.Write(tb, 0, size);

            var pss = Process.GetProcessesByName(processName);
            foreach (var ps in pss)
            {
                var re = Win32API.SendMessage(ps.MainWindowHandle, Win32API.WM_MSG, 5, ref mds);//传递整型参数和不定长的字符串成功
            }
        }
    }
    class ShareMemTest
    {
        ShareMem MemDB = new ShareMem();

        public void Initialize()
        {
            MemDB.Init("YFMemTest", 10240);
        }

        public void Read()
        {
            byte[] bytData = new byte[16];
            int intRet = MemDB.Read(ref bytData, 0, 16);
        }

        public void Write()
        {
            byte[] bytData = new byte[16];
            bytData[0]++;
            bytData[1] += 2;
            if (bytData[0] > 200) bytData[0] = 0;
            if (bytData[1] > 200) bytData[1] = 0;
            MemDB.Write(bytData, 0, 16);
        }
    }
}