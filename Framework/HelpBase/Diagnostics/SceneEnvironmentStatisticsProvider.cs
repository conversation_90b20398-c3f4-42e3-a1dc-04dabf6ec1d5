using System;
using System.Collections.Generic;

namespace HelpBase.Diagnostics
{
    public class SceneEnvironmentStatisticsProvider
    {
        private readonly Func<IReadOnlyDictionary<string, object>> loader;

        public SceneEnvironmentStatisticsProvider(string name, Func<IReadOnlyDictionary<string, object>> loader)
        {
            Name = name;
            this.loader = loader;
        }

        public string Name { get; }

        public IReadOnlyDictionary<string, object> GetItems()
        {
            return loader();
        }
    }
}
