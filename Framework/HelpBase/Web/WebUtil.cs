using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using HelpBase.Json;

namespace HelpBase.Web
{
    public class WebUtil
    {
        private static IEnumerable<PropertyInfo> GetProperties(Type type)
        {
            var depth = 0;
            var nextType = type;
            var bottomType = typeof(object);
            var dictionary = new Dictionary<Type, int>();

            do
            {
                dictionary[nextType] = depth++;
                nextType = nextType.BaseType;
            } while (nextType != bottomType);

            var bindingAttr = BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic;
            return type.GetProperties(bindingAttr).OrderByDescending(p => dictionary[p.DeclaringType]);
        }

        public static WebMemberAttribute GetMember(PropertyInfo property)
        {
            if (property == null)
                throw new ArgumentNullException(nameof(property));

            var att = property.GetCustomAttribute<WebMemberAttribute>();
            if (att != null)
            {
                att.PropertyInfo = property;
                var member = property.GetCustomAttribute<JsonMemberAttribute>();
                if (member != null)
                {
                    att.FormatType = member.FormatType;
                }
                if (att.UIFormat == null && att.FormatType != null)
                {
                    att.UIFormat = att.FormatType.Name;
                }
            }

            return att;
        }

        public static IEnumerable<WebMemberAttribute> GetMembers(Type type)
        {
            if (type == null)
                throw new ArgumentNullException(nameof(type));

            foreach (var property in GetProperties(type))
            {
                var att = GetMember(property);
                if (att != null)
                    yield return att;
            }
        }

        public static string DownloadWebUri(string uri)
        {
            var wr = WebRequest.Create(new Uri(uri));
            var st = wr.GetResponse().GetResponseStream();
            List<byte> bls = new List<byte>();
            int rb = st.ReadByte();
            while (rb != -1)
            {
                bls.Add((byte)rb);
                rb = st.ReadByte();
            }
            Encoding encode = new UTF8Encoding(false);
            string ts = encode.GetString(bls.ToArray());
            var tt = encode.GetBytes(ts);
            ts = System.Text.RegularExpressions.Regex.Unescape(ts);
            ts = ts.Trim((char)65279);
            tt = encode.GetBytes(ts);
            return ts;
        }
    }
}