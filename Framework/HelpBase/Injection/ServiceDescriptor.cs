using System;

namespace HelpBase.Injection
{
    public class ServiceDescriptor
    {
        private readonly Type serviceType;
        private readonly ServiceLifetime lifetime;
        private Type implementationType;
        private object implementationInstance;
        private Func<IContainer, object> implementationFactory;

        internal ServiceDescriptor(Type serviceType, ServiceLifetime lifetime)
        {
            this.serviceType = serviceType;
            this.lifetime = lifetime;
        }

        internal ServiceDescriptor(Type serviceType, Type implementationType, ServiceLifetime lifetime)
            : this(serviceType, lifetime)
        {
            this.implementationType = implementationType;
        }

        internal ServiceDescriptor(Type serviceType, object implementationInstance)
            : this(serviceType, ServiceLifetime.Singleton)
        {
            this.implementationInstance = implementationInstance;
        }

        internal ServiceDescriptor(Type serviceType, Func<IContainer, object> implementationFactory, ServiceLifetime lifetime)
            : this(serviceType, lifetime)
        {
            this.implementationFactory = implementationFactory;
        }

        public ServiceLifetime Lifetime => lifetime;

        public Type ServiceType => serviceType;

        public Type ImplementationType
        {
            get
            {
                if (implementationType == null)
                {
                    if (implementationInstance != null) implementationType = implementationInstance.GetType();
                    else throw new InvalidOperationException("get implementation type error.");
                }

                return implementationType;
            }
        }

        public object ImplementationInstance
        {
            get => implementationInstance;
            internal set => implementationInstance = value;
        }

        public Func<IContainer, object> ImplementationFactory => implementationFactory;
    }
}
