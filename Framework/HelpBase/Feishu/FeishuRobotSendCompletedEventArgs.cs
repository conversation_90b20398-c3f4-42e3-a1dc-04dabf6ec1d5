using System;

namespace HelpBase.Feishu
{
    public class FeishuRobotSendCompletedEventArgs : EventArgs
    {
        public static readonly FeishuRobotSendCompletedEventArgs Success = new FeishuRobotSendCompletedEventArgs();

        public FeishuRobotSendCompletedEventArgs()
        { }

        public FeishuRobotSendCompletedEventArgs(Exception exception)
        {
            if (exception == null)
                throw new ArgumentNullException(nameof(exception));

            Exception = exception;
        }

        public Exception Exception { get; }
    }
}
