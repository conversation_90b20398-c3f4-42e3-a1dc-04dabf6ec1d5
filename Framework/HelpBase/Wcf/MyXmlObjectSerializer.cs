using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Xml.Linq;

namespace HelpBase
{
    public class MyXmlObjectSerializer : XmlObjectSerializer
    {
        public override bool IsStartObject(System.Xml.XmlDictionaryReader reader)
        {
            return true;
        }

        public override object ReadObject(System.Xml.XmlDictionaryReader reader, bool verifyObjectName)
        {
            XElement result = new XElement("root");

            var ts = reader.ReadOuterXml();
            result = XElement.Parse(ts);

            return result;
        }

        public override void WriteEndObject(System.Xml.XmlDictionaryWriter writer)
        {

        }

        public override void WriteObjectContent(System.Xml.XmlDictionaryWriter writer, object graph)
        {

        }

        public override void WriteStartObject(System.Xml.XmlDictionaryWriter writer, object graph)
        {

        }
    }
}
