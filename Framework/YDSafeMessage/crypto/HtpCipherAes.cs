using System;
using System.Collections.Generic;
using System.Security.Cryptography;
using System.Text;

namespace SafeCommCS
{
    class HtpCipherAes : HtpCipher
    {
        private RijndaelManaged mRijndael;
        private ICryptoTransform mEncryptor;
        private ICryptoTransform mDecryptor;

        public HtpCipherAes(byte[] key, byte[] iv) : base(key, iv, 16)
        {
            if (key == null || (key.Length != 16 && key.Length != 32) || iv == null || iv.Length != 16)
            {
                throw new SafeCommException(SafeCommError.HTP_ERROR_PARAM_ERROR, "input param is invalid");
            }
            mRijndael = new RijndaelManaged();
            mRijndael.Key = key;
            mRijndael.IV = iv;
            mRijndael.KeySize = key.Length * 8;
            mRijndael.BlockSize = 128;
            mRijndael.Mode = CipherMode.ECB;
            mRijndael.Padding = PaddingMode.None;

            mEncryptor = mRijndael.CreateEncryptor(key, iv);
            mDecryptor = mRijndael.CreateDecryptor(key, iv);
        }

        public override byte[] Encrypt(int mode, byte[] plain)
        {
            switch (mode)
            {
                case HtpAlg.CBC:
                    return new HtpCbcCrypto().encrypt(plain, this);
                case HtpAlg.CFB:
                    return new HtpCfbCrypto().encrypt(plain, this);
                case HtpAlg.OFB:
                    return new HtpOfbCrypto().encrypt(plain, this);
                case HtpAlg.CTR:
                    return new HtpCtrCrypto().encrypt(plain, this);
            }

            throw new SafeCommException(SafeCommError.HTP_ERROR_PARAM_ERROR, "unsupport alg");
        }

        public override byte[] Decrypt(int mode, byte[] cipher)
        {
            switch (mode)
            {
                case HtpAlg.CBC:
                    return new HtpCbcCrypto().decrypt(cipher, this);
                case HtpAlg.CFB:
                    return new HtpCfbCrypto().decrypt(cipher, this);
                case HtpAlg.OFB:
                    return new HtpOfbCrypto().decrypt(cipher, this);
                case HtpAlg.CTR:
                    return new HtpCtrCrypto().decrypt(cipher, this);
            }
            throw new SafeCommException(SafeCommError.HTP_ERROR_DECRYPT_ERROR, "unsupport alg");
        }

        public override byte[] EncryptBlock(byte[] data)
        {
            if (data == null || data.Length != mBlockLength)
            {
                throw new SafeCommException(SafeCommError.HTP_ERROR_ENCRYPT_ERROR, "input param is invalid");
            }
            return mEncryptor.TransformFinalBlock(data, 0, mBlockLength);
        }

        public override byte[] DecryptBlock(byte[] data)
        {
            if (data == null || data.Length != mBlockLength)
            {
                throw new SafeCommException(SafeCommError.HTP_ERROR_ENCRYPT_ERROR, "input param is invalid");
            }
            return mDecryptor.TransformFinalBlock(data, 0, mBlockLength);
        }

        static void MainAes(string[] args)
        {
            Console.WriteLine("Hello World!");
            String key = "hbzxcdewashjnbvfioknjhgbvcfrtgfd";
            String iv = "1234567812345678";
            String plain = "1234567890123hhhaaadbbiaheiuwdiab";
            try
            {
                HtpCipherAes htpCipher = new HtpCipherAes(Encoding.UTF8.GetBytes(key), Encoding.UTF8.GetBytes(iv));

                byte[] cipher = htpCipher.Encrypt(3, Encoding.UTF8.GetBytes(plain));
                Console.WriteLine("cipher = " + ByteStringUtil.ByteToHexString(cipher));

                byte[] result = htpCipher.Decrypt(3, cipher);
                Console.WriteLine("plain = " + Encoding.UTF8.GetString(result));

                cipher = htpCipher.Encrypt(3, Encoding.UTF8.GetBytes(plain));
                Console.WriteLine("cipher = " + ByteStringUtil.ByteToHexString(cipher));
            }
            catch (SafeCommException sc)
            {
                Console.WriteLine(sc.Message);
                Console.WriteLine(sc.getMsgDes());
                Console.WriteLine(sc.getRetCd());
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
        }

    }
}
