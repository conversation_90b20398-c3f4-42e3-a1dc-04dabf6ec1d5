using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Linq.Expressions;
using System.Net;
using System.Text;
using AppLibrary.Format;
using DataBase;
using DataBase.Entity;
using GameModel;
using GameModel.Activities;
using GameModel.Award;
using GameModel.Db;
using GameModel.Dimensions;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using Google.Api;
using GrpcGameProto;
using HelpBase;
using HelpBase.Json;
using HelpBase.Linq;
using Proto.GameModel;
using StackExchange.Redis;
using static HelpBase.DingTalk.DingRobotSendRequest;

namespace GrpcFramework.GrpcCommand
{


    public partial class GrpcCommand
    {
        enum KickOutTypeEnum
        {
            OnlineTimeMax = 1,
            LimitedTime = 2,
            KickOutByAdmin = 3
        }

        private Character? GetCharacterByRoleId(string rid)
        {
            return !long.TryParse(rid, out var id) ? null : GameApplication.DataManager.FindValue<Character>(id);
        }


        private JsonObject LeitingSystemMailObject(SystemMail mail, string languageStr = "cn")
        {
            if (mail == null)
            {
                return new JsonObject();
            }

            var language = mail.Languages.GetOrDefault(languageStr);
            var jo = new JsonObject
            {
                ["mailId"] = mail.LeitingForeignKey,
                ["title"] = language.Title,
                ["content"] = language.Content,
                ["sender"] = GameModel.Character.System.Name,
                ["sendDate"] = mail.SendTime.ToString("yyyy-MM-dd HH:mm:ss"),
                ["orderDate"] = mail.OrderTime.ToString("yyyy-MM-dd HH:mm:ss"),
                ["expiredDate"] = mail.DeadLine.ToString("yyyy-MM-dd HH:mm:ss"),
                ["isDelete"] = mail.Deleted,
                ["isAllServers"] = true
            };
            if (mail.Awards != null && mail.Awards.Count > 0)
            {
                var awards = new JsonArray();
                mail.Awards.ForEach(award =>
                {
                    award?.GetAwardPreview()?.ForEach(awardSpec =>
                    {
                        awards.Add(new JsonObject()
                        {
                            ["itemId"] = awardSpec.Type.ToString(),
                            ["itemName"] = awardSpec.Name,
                            ["num"] = awardSpec.Value
                            //["detail"] = awardSpec.Type
                        });
                    });
                });

                jo["attachment"] = awards;
            }

            jo["isBroadcast"] = false;
            if (mail.Conditions != null)
            {
                var property_condition_list = mail.Conditions.GetSpecialConditions(typeof(PropertyAnyCondition));
                var registerTime_condition_list =
                    mail.Conditions.GetSpecialConditions(typeof(PlayerRegisterTimeCondition));
                var channel_condition_list = mail.Conditions.GetSpecialConditions(typeof(ContainsChannelCondition));
                var playerLevel_condition_list = mail.Conditions.GetSpecialConditions(typeof(PlayerLevelCondition));

                if (property_condition_list.Count > 0)
                {
                    var receiver_list = "";
                    property_condition_list.ForEach(p =>
                    {
                        if ((p as PropertyAnyCondition)?.PropertyName == "ID")
                        {
                            (p as PropertyAnyCondition)?.Value.ForEach(c => receiver_list += c + ",");

                        }
                    });
                    if (receiver_list.Length > 0)
                    {
                        receiver_list = receiver_list.Remove(receiver_list.Length - 1);
                        jo["receiverList"] = receiver_list;
                    }
                }
                else
                {
                    jo["isBroadcast"] = true;
                }

                if (channel_condition_list.Count > 0)
                {
                    var channel_list = "";
                    channel_condition_list.ForEach(p =>
                    {
                        (p as ContainsChannelCondition)?.ChannelList.ForEach(c => channel_list += c + ",");
                    });
                    if (channel_list.Length > 0)
                    {
                        channel_list = channel_list.Remove(channel_list.Length - 1);
                        jo["channels"] = channel_list;
                    }
                }

                if (registerTime_condition_list.Count > 0)
                {
                    var registerTime_condition = (registerTime_condition_list[0] as PlayerRegisterTimeCondition);
                    jo["minRegisterTime"] = registerTime_condition?.MinTime.ToString("yyyy-MM-dd HH:mm:ss");
                    jo["maxRegisterTime"] = registerTime_condition?.MaxTime.ToString("yyyy-MM-dd HH:mm:ss");
                }

                if (playerLevel_condition_list.Count > 0)
                {

                    var playerLevel_condition = (playerLevel_condition_list[0] as PlayerLevelCondition);
                    jo["maxLevel"] = playerLevel_condition?.MaxLevel;
                    jo["minLevel"] = playerLevel_condition?.MinLevel;
                }
            }

            jo["zoneId"] = GameApplication.Service.Config.ID.ToString();

            return jo;
        }

        private EntityBase? LoadFromDB(Expression<Func<EntityBase, bool>> whereExp)
        {
            var type = GameApplication.DataManager.LoadGameValue<EntityBase, long>(p => p.Type, whereExp, null);
            var template = GameApplication.DataManager.FindValueNoConstraint<EntityType>(type);
            if (template == null) return null;

            switch (template)
            {
                case EquipType:
                    return GameApplication.DataManager.LoadGameObject<Equip>(
                        Expression.Lambda<Func<Equip, bool>>(whereExp.Body, whereExp.Parameters), typeof(EntityBase),
                        false, false, false);
                case ItemType:
                    return GameApplication.DataManager.LoadGameObject<Item>(
                        Expression.Lambda<Func<Item, bool>>(whereExp.Body, whereExp.Parameters), typeof(EntityBase),
                        false, false, false);
                case SceneObjectType:
                    return GameApplication.DataManager.LoadGameObject<SceneObject>(
                        Expression.Lambda<Func<SceneObject, bool>>(whereExp.Body, whereExp.Parameters),
                        typeof(EntityBase), false, false, false);
                default:
                    throw new InvalidOperationException($"unknown template:{type}");
            }
        }

        private JsonObject LeitingMailObject(Mail mail)
        {
            var jo = new JsonObject
            {
                ["zoneId"] = GameApplication.Service.Config.ID.ToString(),
                ["rid"] = mail.OwnerID.ToString(),
                ["mailId"] = mail.ID.ToString(),
                ["title"] = mail.GetTitle(),
                ["content"] = mail.GetContent(),
                ["sender"] = mail.GetSenderName() ?? GameModel.Character.System.Name,
                ["sendDate"] = mail.GetSendTime().ToString("yyyy-MM-dd HH:mm:ss"),
                ["expiredDate"] = mail.GetDeadLine().ToString("yyyy-MM-dd HH:mm:ss"),
                ["isRead"] = mail.Status == MailStatus.Readed || mail.Status == MailStatus.Archived,
                ["isReceive"] = mail.Status == MailStatus.Archived,
                ["isDelete"] = mail.Deleted
            };
            if (mail.HaveAwards)
            {
                var awards = new JsonArray();
                mail?.GetAwardsContent().ForEach(award =>
                {
                    award?.GetAwardPreview()?.ForEach(awardSpec =>
                    {
                        awards.Add(new JsonObject()
                        {
                            ["itemName"] = awardSpec.Name,
                            ["num"] = awardSpec.Value,
                            ["itemId"] = awardSpec.Type.ToString()
                        });
                    });
                });

                jo["attachment"] = awards;
            }

            return jo;
        }

        [OperationContract]
        public string SaveData()
        {
            string msg = "";
            bool success = false;
            try
            {
                success = GameApplication.Instance.SaveServer(SaveMode.Sync, true, (e) => { msg = e; });
                if (success)
                {
                    msg = "success save data";
                }
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return WrapResult(success, msg);
        }

        [OperationContract]
        public string CheckSaveDataCount()
        {
            string msg = "";
            bool success = false;
            try
            {
                var res_json = new JsonObject();
                var res_json_array = new JsonArray();
                res_json["TotalUnsaveDataCount"] = 0;
                foreach (var dbManager in DbConnectionManager.DbManagers.Values)
                {
                    var db_json = new JsonObject();
                    var rowCount = 0;
                    var totalInsertCount = 0;
                    var totalUpdateCount = 0;
                    foreach (var tableEntry in dbManager.Tables)
                    {
                        var table = tableEntry.Value;
                        var insertCount = table.InsertRowCount;
                        var updateCount = table.UpdateRowCount;
                        if (insertCount != 0 || updateCount != 0)
                        {
                            rowCount++;
                            totalInsertCount += insertCount;
                            totalUpdateCount += updateCount;
                        }
                    }

                    if (rowCount + totalInsertCount + totalUpdateCount > 0)
                    {
                        db_json["EntityName"] = dbManager.Config.ShowName;
                        if (rowCount > 0)
                        {
                            db_json["RowCount"] = rowCount;
                        }

                        if (totalInsertCount > 0)
                        {
                            db_json["TotalInsertCount"] = totalInsertCount;
                        }

                        if (totalUpdateCount > 0)
                        {
                            db_json["TotalUpdateCount"] = totalUpdateCount;
                        }
                        //db_json["RowCount"] = rowCount;
                        //db_json["TotalInsertCount"] = totalInsertCount;
                        //db_json["TotalUpdateCount"] = totalUpdateCount;
                    }

                    if (db_json.Count > 0)
                    {
                        res_json_array.Add(db_json);
                    }

                    res_json["TotalUnsaveDataCount"] += totalInsertCount + totalUpdateCount;
                    //ms.Add(dbManager.Config.ShowName, $"-- Row: {rowCount} Insert: {totalInsertCount} Update: {totalUpdateCount}------------------------------------");
                }

                res_json["DetailedDBInfor"] = res_json_array;
                msg = res_json.ToString();
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return WrapResult(success, msg);
        }

        [OperationContract]
        public string ShutdownService(int delaySeconds = 30)
        {
            string error = "";
            bool failed = false;
            try
            {
                GameApplication.Instance.service.CloseAllConnection();
                GameApplication.Instance.service.Shutdown(errorHandler: s => { error += s; }, throwOnError: true);
                TimerManager.Repeat(1000, delaySeconds, seconds => { }, () => { Environment.Exit(0); });
            }
            catch (Exception ex)
            {
                failed = true;
                error += ex.ToString();
            }

            string msg = failed ? "exit error" : $"service will exit in {delaySeconds} seconds";

            return $"{{ \"success\": {!failed}, \"error\": \"{error}\", \"msg\": \"{msg}\" }}";
        }


        [OperationContract]
        public string ExcuteScriptBase64(string scriptBase64)
        {
            try
            {
                var base64EncodedBytes = System.Convert.FromBase64String(scriptBase64);
                var script = System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
                return $"{nameof(ExcuteScriptBase64)} return: {GameApplication.Instance.ExcuteScript(script)}";
            }
            catch (Exception e)
            {
                return e.ToString();
            }
        }

        [OperationContract(SkipClientImpl = true)]
        public ServiceStatusReply ServiceStatus()
        {
            var provider = GameApplication.Instance.service.SceneEnvironmentStatisticsProviders.First(p =>
                p.Name == GameServiceBase.GameServiceBase.ServerEnvironmentStatisticsProviderName);
            var reply = new ServiceStatusReply();
            foreach (var p in provider.GetItems())
            {
                reply.Pairs.Add(new GrpcStringPair()
                {
                    Key = p.Key,
                    Value = p.Value.ToString()
                });
            }

            return reply;
        }

        [OperationContract]
        public string SetLimitRegisterCharacter(int LimitRegisterCharacter)
        {
            int oldLimitRegisterCharacter = GameApplication.Instance.GameConfig.LimitRegisterCharacter;
            GameApplication.Instance.GameConfig.LimitRegisterCharacter = LimitRegisterCharacter;
            return
                $"Success {nameof(SetLimitRegisterCharacter)} {oldLimitRegisterCharacter} to {LimitRegisterCharacter}";
        }


        [OperationContract]
        public string SetLeitingPasswordLogin(bool enable, string password)
        {
            GameApplication.Instance.GameConfig.EnableSuperPassword = password;
            GameApplication.Instance.GameConfig.EnableSuperPasswordLogin = enable;
            return $"Success {nameof(SetLeitingPasswordLogin)} to {enable}, password: {password}";
        }


        [OperationContract]
        public string GetGameModelConfig()
        {
            return JsonSerializer.Serialize(GameApplication.Instance.GameConfig).ToString();
        }

        [OperationContract(SkipClientImpl = true)]
        public GameVersionReply GetVersion()
        {
            var configVersion = GameApplication.DataManager.FindValue<ConfigVersion>(1);
            var serverVersion = GameApplication.DataManager.FindValue<ServerVersion>(1);
            return new GameVersionReply()
            {
                ConfigTag = configVersion?.Tag ?? "",
                ConfigVersion = configVersion?.GetVersion(ProtoUtility.TypeGroupDataCache) ?? "",
                ServerTag = serverVersion?.Tag ?? "",
                ServerVersion = serverVersion?.Version ?? ""
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingQuestionnaireReply LeitingQuestionnaire(
            string RoleID, string LimitType,
            string ZoneId, string GameType,
            string Survey, string UniqueId,
            string Sid, string ChannelNo)
        {
            bool success = false;
            string msg;

            var character = GetCharacterByRoleId(RoleID);
            if (character == null)
            {
                msg = $"找不到对应玩家，rid {nameof(RoleID)}: {RoleID}";
                goto Reply;
            }

            try
            {
                success = character.QuestionnaireSuccess(Survey, out string error);
                msg = success ? "Questionnaire reward Successfully" : error;
            }
            catch (Exception e)
            {
                success = false;
                msg = e.ToString();
            }

            Reply:
            return new LeitingQuestionnaireReply()
            {
                Success = success,
                Msg = msg
            };
        }

        // https://leiting.feishu.cn/docs/doccnkKzOZ4Ihz3k5gXeHwxJCfh#FIh0Ai

        #region 雷霆GM接口

        /// <summary>
        /// 下线
        /// </summary>
        /// <param name="LeitingId"></param>
        /// <param name="KickOutType"></param>
        /// <returns></returns>
        /// https://www.yuque.com/docs/share/d72aca3b-b9d9-4e00-a544-3ddc10ecc624?#%20%e3%80%8a%e9%80%9a%e7%9f%a5%e4%b8%8b%e7%ba%bf%e3%80%8b
        [OperationContract(SkipClientImpl = true)]
        public LeitingKickOutUserReply LeitingKickOutUser(string LeitingId, int KickOutType, int age,
            string game, int isFast, int isHoliday, long onlineTime, long timestamp
        )
        {
            bool success = false;
            string msg = "";
            var c = GameApplication.DataManager.FirstValue<Character>(p => p.AccountName == LeitingId);
            if (c == null)
            {
                success = false;
                msg = $"找不到对应玩家，rid : {LeitingId}";
                goto LeitingKickOutUserRe;
            }

            string illegalMsg =
                "根据您的账号信息，您已被识别为未成年人。根据《关于进一步严格管理切实防止未成年人沉迷网络游戏的通知》，18岁以下未成年人只可以在周五、周六、周日和法定节假日的20时至21时体验游戏。请您在指定的时间回来体验游戏吧。";
            if (KickOutType == 1) // 1：达到在线时长上限 
            {
                msg = "达到在线时长上限";
            }
            else if (KickOutType == 2) // 2：限制时间段不允许体验游戏 
            {
                msg = "限制时间段不允许体验游戏";
            }
            else if (KickOutType == 3) // 3：客服踢下线
            {
                msg = "您已被GM踢下线";
            }
            else
            {
                // 不期待到这里 文档里面只有1 2 3
                msg = $"您已被迫下线, type: {KickOutType}";
            }

            if ((KickOutType == 1 || KickOutType == 2) &&
                age < 18 && isFast == 2)
            {
                msg = illegalMsg;
            }

            c.User.SendGoogleProto(ClientMethod.LogoutLeitingSDK);
            c.User.Disconnect(LocalityStrings.GetUndefinedString(msg));
            success = true;

            LeitingKickOutUserRe:
            return new LeitingKickOutUserReply()
            {
                Success = success,
                Msg = msg
            };

        }


        [OperationContract]
        public string SetLogLevel(int logLevel)
        {
            string msg = $"success change to logLevel {logLevel}";
            try
            {
                GameApplication.Instance.GameConfig.LogLevel = (LogLevel)logLevel;
            }
            catch (Exception e)
            {
                msg = $"{nameof(SetLogLevel)} error: {e}";
            }

            return msg;
        }

        [OperationContract(SkipClientImpl = true)]
        public DeliverOrderReply DeliverOrderByRid(string OrderID, Int64 CreateTime, string PlatformID,
            string ProductPrice, string ProductID, string CharacterID, string type)
        {
            if (long.TryParse(CharacterID, out var player_id))
            {
                return DeliverOrder(OrderID, CreateTime, PlatformID, ProductPrice, ProductID, player_id, type);
            }
            else
            {
                return new DeliverOrderReply() { Success = true, Msg = "not available format" + CharacterID };
            }
        }


        [OperationContract(SkipClientImpl = true)]
        public DeliverOrderReply DeliverOrder(string OrderID, Int64 CreateTime, string PlatformID,
            string ProductPrice, string ProductID, Int64 CharacterID, string type)
        {
            //Logger.Error.Write("hhhhhhhhhh" + CharacterID);k
            //ProductPrice = ProductPrice.Replace(".", String.Empty);
            //Logger.Error.Write("DeliverOrder on " + CharacterID);
            string msg = String.Empty;
            bool success = false;
            try
            {
                var d_price = double.Parse(ProductPrice);
                var price = Convert.ToInt32(d_price);
                var r = Pay(OrderID, ProductID, CreateTime / 1000, CharacterID, price, PlatformID, type);
                success = r == (int)PayResult.成功;
                msg = success ? "success" : $"Fail {r}";
            }
            catch (Exception e)
            {
                success = false;
                msg = e.ToString();
            }

            return new DeliverOrderReply
            {
                Success = success,
                Msg = msg
            };
        }


        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply RefundOrder(string OrderID, string CharacterID)
        {
            var character = GetCharacterByRoleId(CharacterID);
            var record = GameApplication.DataManager.LoadObject<PayRecord>(
                GameApplication.DataManager.AccountDBManager, p => p.OrderNumber == OrderID).SingleOrDefault();
            if (record.Equals(default(KeyValuePair<long, PayRecord>)))
            {
                return new LeitingCommonReply { Success = false, Msg = "未找到对应订单id" };
            }

            if (record.Value.CharacterID != character.ID)
            {
                return new LeitingCommonReply { Success = false, Msg = "该订单不属于这个玩家" };
            }

            //if (record.Value.GetVirtualCurerncies.Count == 0)
            //{
            //    return new LeitingCommonReply { Success = false, Msg = "玩家未从该订单获得金币或金钻" };
            //}

            var dvc = character.GetVirtualCurrency(100, true);
            dvc.ForcePay((int)(record.Value.Dollar / 10), "订单退款");
            dvc.UpdateToClient();
            var res = new LeitingCommonReply { Success = true };
            record.Value.GetVirtualCurerncies.ForEach(v =>
            {
                var cvc = character.GetVirtualCurrency(v.Key, false);

                if (cvc == null)
                {
                    res.Success = false;
                    res.Msg = "玩家未获得过货币" + v.Key;
                    return;
                }

                var oldValue = cvc.Value;
                cvc.ForcePay(v.Value, "订单退款");

                cvc.UpdateToClient();
                var extend_info = new JsonObject() { { "OrderID", OrderID } };
            });

            //var cut_diamonds = record.Value.GetVirtualCurerncies.ContainsKey(100) ? record.Value.GetVirtualCurerncies[100] : 0;
            var cut_coins = record.Value.GetVirtualCurerncies.ContainsKey(2) ? record.Value.GetVirtualCurerncies[2] : 0;

            character.AddMail("订单退款通知",
                $"亲爱的玩家您好\n我们收到系统通知，您账号在{record.Value.Date}充值的{(int)(record.Value.Dollar / 100)}元订单已成功申请退款，为反映您退款情况，我们已相应扣除您账号{(int)(record.Value.Dollar / 10)}金钻和{cut_coins}金币，请知悉。",
                null,
                true,
                DateTimeExtension.Now.AddMonths(1),
                MailType.Common, "订单退款", "", "皮卡小镇政务厅");
            if (res.Success)
            {
                record.Value.Refund = true;
                character.PayMoney -= (int)(record.Value.Dollar);
                var act = GameApplication.ActivityManager.FirstActivity<TotalPayActivity>();
                if (act != null)
                {
                    var ac_record = act.GetCharacterRecord(character, true, true);
                    ac_record.TotalValue -= (int)(record.Value.Dollar);
                    act.UpdateToClient(character);
                }

                record.Value.SaveToDB(false);

                character.UpdateToClient(c =>
                {
                    c.FirstChargePanelData = character.GetFirstChargePanelData();
                    c.FirstChargeLevel = character.GetRookieChargePackLevel;
                    c.TotalChargeScore = character.PayMoney / 10;
                });
            }

            return res;
        }

        public int GetGoldCrownDaysByProductId(string productId)
        {
            
            return 0;
        }

        //private void RecordAction(string action, params object[] pars)
        //{
        //    var finalString = string.Format(action, pars);

        //    string userName = "陌生人";
        //    string useString = string.Format("[{0}][{1}][{2}]", userName, "", 0);
        //    Logger.Grpc.Write("{0}{1}", useString, finalString);
        //}

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply BlockAccount(string account, string prompt, string reason, Int64 time)
        {
            string msg = "";
            bool success = true;

            var list = GameApplication.DataManager.FindValue<Character>(p => p.AccountName == account || p.AccountName == account);

            if (list.Count == 0)
            {
                return new LeitingCommonReply
                {
                    Success = false,
                    Msg = $"封号失败，找不到Account为 {account} 的玩家"
                };
            }
            else
            {
                foreach (var c in list)
                {
                    try
                    {
                        BlockCharacter(c, prompt, reason, time);
                        msg += $"封号成功: 玩家ID: {c.ID} Name: {c.Name}\n";
                    }
                    catch (Exception e)
                    {
                        success = false;
                        msg += $"封号失败: 玩家ID: {c.ID} Name: {c.Name} Error: {e.ToString()}\n";
                    }
                }
            }

            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        public LeitingCommonReply BlockCharacter(long cid, string prompt, string reason, Int64 time)
        {
            string msg;
            bool success = false;

            var c = GameApplication.DataManager.FindValue<Character>(cid);
            if (c == null)
            {
                success = false;
                msg = $"fail {nameof(BlockCharacter)}, 找不到对应玩家，cid : {cid}";
                goto LeitingBlockAccountRe;
            }
            try
            {
                BlockCharacter(c, prompt, reason, time);
                success = true;
                msg = "success";
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        LeitingBlockAccountRe:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        private void BlockCharacter(Character c, string prompt, string reason, Int64 time)
        {
            c.StopLoginDate = time == -1 ? DateTimeExtension.Now.AddYears(99) : DateTimeExtension.Now.AddMinutes(time);
            c.Save();
            // c.ShowError(LocalityStrings.GetUndefinedString($"{prompt}"));
            c.User.Disconnect(LocalityStrings.GetUndefinedString(prompt));
            RecordAction("修改玩家[{0}]的封号时间到[{1}] Leiting Reason: {2}", c.Name, c.StopLoginDate, reason);

            if (Constants.AllowTaLog)
            {
                LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                    .WriteProperties()
                        .WriteEntry("action", "封号")
                        .WriteEntry("reason", reason)
                        .WriteObject("extend_info")
                            .WriteRawEntry("account", c.AccountName)
                            .WriteRawEntry("prompt", prompt)
                            .WriteEntry("time", time)
                            .WriteStringEntry("player_id", c.ID)
                            .WriteEntry("player_name", c.Name)
                        .WriteToTaLogger();
            }
        }

        [OperationContract]
        public LeitingCommonReply LeitingDeleteActivity(long id)
        {
            var re = GameApplication.ActivityManager.DeleteActivity(id);

            if (re == false)
            {
                return new LeitingCommonReply(new LeitingCommonReply()
                {
                    Success = false,
                    Msg = $"activity id:{id} deleted failed,maybe it's running now, check it pls."
                });
            }

            return new LeitingCommonReply(new LeitingCommonReply()
            {
                Success = true
            });
        }

        [OperationContract]
        public LeitingCommonReply LeitingKickOffChractors(string userid)
        {
            if (!string.IsNullOrWhiteSpace(userid))
            {
                long _id = -1;
                long.TryParse(userid, out _id);
                var c = GameApplication.DataManager.FirstValue<Character>(c => c.ID == _id || c.AccountName == userid);
                c.User.Disconnect();
            }
            else
            {
                //如果没有userid，则全部断线
                var cs = GameApplication.UserManager.Onlines;
                if (cs != null && cs.Count() > 0)
                {
                    foreach (var c in cs)
                    {
                        c.User.Disconnect();
                    }
                }
            }

            return new LeitingCommonReply(new LeitingCommonReply()
            {
                Success = true
            });
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply ForbidSpeak(string rid, string prompt, string reason, Int64 time)
        {
            string msg;
            bool success = false;

            try
            {
                var c = GetCharacterByRoleId(rid);
                if (c == null)
                {
                    success = false;
                    msg = $"{nameof(ForbidSpeak)} 找不到对应玩家，rid : {rid}";
                    goto ForbidSpeakRe;
                }

                c.CanSpeakTime = time == -1 ? DateTimeExtension.Now.AddYears(99) : DateTimeExtension.Now.AddMinutes(time);
                c.StopSpeak(c.CanSpeakTime);
                c.Save();
                RecordAction("修改玩家[{0}]的禁言时间到[{1}]", c.Name, c.CanSpeakTime);
                c.ShowError(PicaLocalityStrings.GetUndefinedString($"{prompt}"));
                // c.SystemMessage($"{prompt}");
                msg = "success";
                success = true;
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "禁言")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("reason", reason)
                                .WriteStringEntry("player_id", c.ID)
                                .WriteEntry("player_name", c.Name)
                            .WriteToTaLogger();
                }
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        ForbidSpeakRe:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply FakeForbidSpeak(string rid, string reason, System.Int64 time)
        {
            // TODO
            string msg = String.Empty;
            bool success = false;

            try
            {
                var c = GetCharacterByRoleId(rid);
                if (c == null)
                {
                    success = false;
                    msg = $"{nameof(FakeForbidSpeak)} 找不到对应玩家，rid : {rid}";
                    goto FakeForbidSpeakReq;
                }

                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "伪禁言")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("reason", reason)
                                .WriteStringEntry("player_id", c.ID)
                                .WriteEntry("player_name", c.Name)
                            .WriteToTaLogger();
                }

                var date = time == -1 ? DateTimeExtension.Now.AddYears(99) : DateTimeExtension.Now.AddMinutes(time);
                c.StopSpeakButSendSelf(date);
                c.Save();
                RecordAction("修改玩家[{0}]的伪禁言时间到[{1}]", c.Name, date);
                // c.ShowError(LocalityStrings.GetUndefinedString($"{reason}"));
                msg = "success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        FakeForbidSpeakReq:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply UnblockAccount(string account, string reason)
        {
            string msg = "";
            bool success = true;
            var list = GameApplication.DataManager.FindValue<Character>(p => p.AccountName == account || p.AccountName == account);

            foreach (var c in list)
            {
                try
                {
                    UnblockCharacter(c, reason);
                    msg += $"解封成功。玩家 ID: {c.ID}, Name {c.Name}\n";
                }
                catch (Exception e)
                {
                    success = false;
                    msg += $"解封失败。玩家 ID: {c.ID}, Name {c.Name} - {e.ToString()}\n";
                }
            }

            if (list.Count == 0)
            {
                success = false;
                msg = $"解封失败。找不到账号为 {account} 的玩家";
            }

            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        private void UnblockCharacter(Character c, string reason)
        {
            c.StopLoginDate = DateTimeExtension.Now;
            c.Save();
            RecordAction("解封玩家, 修改玩家[{0}]的封号时间到[{1}] Leiting Reason: {2}", c.Name, c.StopLoginDate, reason);

            if (Constants.AllowTaLog)
            {
                LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                    .WriteProperties()
                        .WriteEntry("action", "解封")
                        .WriteEntry("reason", reason)
                        .WriteObject("extend_info")
                            .WriteStringEntry("player_id", c.ID)
                            .WriteEntry("player_name", c.Name)
                        .WriteToTaLogger();
            }
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply UnblockCharacter(long cid, string reason)
        {
            string msg;
            bool success = false;

            try
            {
                var c = GameApplication.DataManager.FindValue<Character>(cid);
                if (c == null)
                {
                    success = false;
                    msg = $"找不到对应玩家，cid : {cid}";
                    goto LeitingUnblockAccountRe;
                }
                UnblockCharacter(c, reason);
                success = true;
                msg = "success";
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        LeitingUnblockAccountRe:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply AllowSpeak(string rid, string reason)
        {
            string msg;
            bool success = false;
            try
            {
                var c = GetCharacterByRoleId(rid);
                if (c == null)
                {
                    success = false;
                    msg = $"{nameof(ForbidSpeak)} 找不到对应玩家，rid : {rid}";
                    goto AllowSpeakRe;
                }

                c.CanSpeakTime = DateTimeExtension.Now;
                c.StopSpeak(c.CanSpeakTime);
                c.StopSpeakButSendSelf(c.CanSpeakTime);
                c.Save();
                RecordAction("解除玩家禁言,修改玩家[{0}]的禁言时间到[{1}]", c.Name, c.CanSpeakTime);
                msg = $"success {nameof(AllowSpeak)}, {c.Name}";
                success = true;
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "解禁言")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("reason", reason)
                                .WriteStringEntry("player_id", c.ID)
                                .WriteEntry("player_name", c.Name)
                            .WriteToTaLogger();
                }
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        AllowSpeakRe:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply MaintainSettings(long stopLoginTime, long openLoginTime, string openLoginMessage, List<string> privilegedIPs, List<string> privilegedAccounts)
        {
            string msg;
            bool success = false;
            try
            {
                var conf = GameApplication.Instance.GameConfig;
                conf.StopLoginTime = DateTimeExtension.ConvertFromTimestamp(stopLoginTime);
                conf.OpenLoginMessage = openLoginMessage;
                conf.OpenLoginTime = DateTimeExtension.ConvertFromTimestamp(openLoginTime);
                conf.PrivilegedAccounts = privilegedAccounts;
                conf.PrivilegedIPs = privilegedIPs;
                success = true;
                msg = $"success to change {nameof(MaintainSettings)}";
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply Rename(string rid, string newName, string reason, bool sendRenameCard)
        {
            string msg;
            bool success = false;

            try
            {
                var c = GetCharacterByRoleId(rid);
                if (c == null)
                {
                    success = false;
                    msg = $"{nameof(Rename)} 找不到对应玩家，rid : {rid}";
                    goto RenameRe;
                }

                string oldName = c.Name;

                if (!GameApplication.UserManager.CheckCharacterNameAvailable(newName, c.AdminType, c.User.Account,
                        out var error, checkSilence : false))
                {
                    msg = $"{nameof(Rename)} Character {rid}, nameIllegal: {newName}, 输入昵称不合规, {error.StringCode}";
                    goto RenameRe;
                }

                if (!GameApplication.UserManager.TryAddCharacterName(newName))
                {
                    msg = $"{nameof(Rename)} Character: {rid} rename duplicateName: {newName}";
                    goto RenameRe;
                }

                # region == 邮件发送改名卡 ==
                try
                {
                    if (sendRenameCard)
                    {
                        //放入改名卡
                        var award = new ObjectAward(202100, 1);
                        var ads = new JsonDynamicList<AwardBase>
                    {
                        award
                    };
                        //地下需要用i18n替换
                        c.AddMail("昵称修改通知",
                            "亲爱的玩家，检测到您的昵称含有敏感字符，已被系统重置，您可以从附件中领取改名卡重新修改昵称，感谢您的理解及支持。",
                            ads,
                            true,
                            DateTimeExtension.Now.AddMonths(1),
                            MailType.Award, "昵称修改通知");
                    }
                    else
                    {
                        c.AddMail("昵称修改通知",
                            "亲爱的玩家，检测到您的昵称含有敏感字符，已被系统重置，给您带来的不便，敬请谅解。",
                            new JsonDynamicList<AwardBase> { },
                            true,
                            DateTimeExtension.Now.AddMonths(1),
                            MailType.Notice, "昵称修改通知");
                    }
                }
                catch (Exception ex) { }

                #endregion
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "修改角色名")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("newName", newName)
                                .WriteEntry("sendRenameCard", sendRenameCard)
                                .WriteRawEntry("reason", reason)
                                .WriteStringEntry("player_id", c.ID)
                                .WriteEntry("player_name", c.Name)
                            .WriteToTaLogger();
                }
                c.ChangeName(newName, force: true);
                c.Save();
                RecordAction("修改玩家名称[{0}]改为[{1}]", oldName, c.Name);
                c.ActionTime.NextChangeNameTime = DateTimeExtension.Now;

                msg = $"success {nameof(Rename)}, change {oldName} to {c.Name}";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        RenameRe:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply GetRoleList(string rid, string account, string name, string channelNo)
        {
            //todo yuyusong:增加了channelNo
            string msg;
            string data = "[]";
            bool success = false;

            List<Character> cs = new List<Character>();
            try
            {
                //if (!String.IsNullOrEmpty(rid))
                //{
                //    var c = GetCharacterByRoleId(rid);
                //    if (c != null)
                //    {
                //        cs.Add(c);
                //    }
                //}
                //else if (!String.IsNullOrEmpty(account))
                //{
                //    cs = GameApplication.DataManager.FindValue<Character>(p => p.AccountName == account);
                //}
                //else if (!String.IsNullOrEmpty(name))
                //{
                //    cs = GameApplication.DataManager.FindValue<Character>(p => p.Name.Contains(name));
                //}
                //else
                //{
                //    msg = $"fail {nameof(GetRoleList)}, at least need one non-empty args, rid, account, name";
                //    goto GetRoleListReq;
                //}

                cs = GameApplication.DataManager.FindValue<Character>(p =>
                {
                    var res = true;
                    long id = 0;
                    if (!String.IsNullOrEmpty(rid))
                    {
                        res = res && !long.TryParse(rid, out id) ? false : p.ID == id;
                    }
                    if (!String.IsNullOrEmpty(account))
                    {
                        res = res && p.AccountName == account;
                    }
                    if (!String.IsNullOrEmpty(name))
                    {
                        res = res && p.Name.Contains(name);
                    }

                    return res;
                });

                var dataJson = new JsonArray();
                cs.ForEach(p => dataJson.Add(p.CharacterInfo));
                data = dataJson.ToString();
                success = true;
                msg = "success";
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        GetRoleListReq:
            return new LeitingCommonReply
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply ItemList()
        {
            string msg;
            string data = "{}";
            bool success = false;

            try
            {
                JsonObject jsonData = new JsonObject();
                GameApplication.DataManager.FindValueNoConstraint<EntityType>(p => p.Available)
                    .ForEach(e => jsonData[e.ID.ToString()] = e.Name);
                GameApplication.DataManager.FindValueNoConstraint<VirtualCurrencyType>(p => true)
                    .ForEach(e => jsonData[e.ID.ToString()] = e.Name);
                data = jsonData.ToString();
                msg = "Success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply CoinList(string rid, int pageNum, int pageSize)
        {
            string msg;
            string data = "{}";
            bool success = false;
            pageNum = pageNum == 0 ? 1 : pageNum;
            pageSize = pageSize == 0 ? 20 : pageSize;
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(PackageInfo)} 找不到对应玩家，rid : {rid}";

                return new LeitingCommonReply()
                {
                    Success = success,
                    Msg = msg,
                    Data = data
                };
            }
            try
            {
                JsonObject jsonData = new JsonObject();

                jsonData["total"] = character.VirtualCurrencies.Count;
                jsonData["list"] = new JsonArray(character.VirtualCurrencies.Skip((pageNum - 1) * pageSize).Take(pageSize).Select(vc =>
                {
                    return new JsonObject()
                    {
                        {"coinId", vc.Type.ToTaLogID() },
                        {"coinName", I18nManager.VirtualCurrencyName(vc.Type) },
                        {"remainNum", vc.Value }
                    };
                }));
                data = jsonData.ToString();
                msg = "Success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply DeductCoin(string rid, string coinId, int num)
        {
            string msg;
            string data = "{}";
            bool success = false;
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(DeductCoin)} 找不到对应玩家，rid : {rid}";

                return new LeitingCommonReply()
                {
                    Success = success,
                    Msg = msg,
                    Data = data
                };
            }

            long currencyIdLong = 0;
            if (!string.IsNullOrEmpty(coinId))
            {
                if (!long.TryParse(coinId, out currencyIdLong))
                {
                    msg = $"{nameof(DeductCoin)} 货币id格式错误，id : {coinId}";

                    return new LeitingCommonReply()
                    {
                        Success = success,
                        Msg = msg,
                        Data = data
                    };
                }
            }
            try
            {
                var currency = character.GetVirtualCurrency(currencyIdLong, true);
                //if (num >= 0)
                //{
                //    character.AddVirtualCurrency(currencyIdLong, num, "后台修改", "", 0);
                //}
                //else
                //{
                //    currency.ForcePay(num, "后台修改");
                //    currency.UpdateToClient();
                //}

                currency.ForcePay(num, "后台修改");
                currency.UpdateToClient();
                msg = "更改成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply PlayerTaskList(string rid, string taskId, string taskName, string taskType, int pageNum, int pageSize)
        {
            string msg;
            string data = "{}";
            bool success = false;
            pageNum = pageNum == 0 ? 1 : pageNum;
            pageSize = pageSize == 0 ? 20 : pageSize;
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(PackageInfo)} 找不到对应玩家，rid : {rid}";

                return new LeitingCommonReply()
                {
                    Success = success,
                    Msg = msg,
                    Data = data
                };
            }
            try
            {
                JsonObject jsonData = new JsonObject();
                var mission_list = character.GetAllMissions().ToList().Where(m => m.Group != MissionGroup.Order && (string.IsNullOrEmpty(taskId) ? true : m.ID.ToString() == taskId) && (string.IsNullOrEmpty(taskName) ? true : I18nManager.MissionName(m.Type) == taskName) && (string.IsNullOrEmpty(taskType) ? true : m.GetLeitingGroupName() == taskType)).ToList();
                jsonData["total"] = mission_list.Count;
                jsonData["list"] = new JsonArray(mission_list.Skip((pageNum - 1) * pageSize).Take(pageSize).Select(ms => ms.GetLeitingInfoJson()));
                data = jsonData.ToString();
                msg = "Success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply GetPlayerOrganizationData(string rid)
        {
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                return new LeitingCommonReply() { Msg = "未找到目标玩家", Success = false };
            }
            var res = new JsonObject();
            res["OrganizationID"] = character.OrganizationAgent.OrganizationID;
            res["Sid"] = character.AccountName;
            if (character.OrganizationAgent.OrganizationID > 0)
            {
                res["OrganizationJoinDate"] = character.OrganizationAgent.LastJoinTime.ToString("yyyy-MM-dd HH:mm:ss");
                var contribution = character.OrganizationAgent.Contribution;
                res["TotalContribution"] = character.OrganizationAgent.TotalContribution;
                res["LastContributionDate"] = contribution == null ? "无" : contribution.LastChangeTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                res["OrganizationJoinDate"] = "无";
                res["TotalContribution"] = 0;
                res["LastContributionDate"] = "无";
            }
            return new LeitingCommonReply() { Data = res.ToString(), Msg = "", Success = true };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply UpdatePlayerTaskStatus(string rid, string taskId, string taskStatus, string reason)
        {
            string msg = "";
            string data = "{}";
            bool success = false;
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(PackageInfo)} 找不到对应玩家，rid : {rid}";

                return new LeitingCommonReply()
                {
                    Success = success,
                    Msg = msg,
                    Data = data
                };
            }
            try
            {
                var mission = character.GetAllMissions().FirstOrDefault(m => m.ID.ToString() == taskId);
                if (mission == null)
                {
                    return new LeitingCommonReply()
                    {
                        Success = success,
                        Msg = $"找不到对应任务 {taskId}",
                        Data = data
                    };
                }
                if (!Enum.TryParse(typeof(MissionStatus), taskStatus, out _))
                {
                    return new LeitingCommonReply()
                    {
                        Success = success,
                        Msg = $"无法反序列化任务状态 {taskStatus}",
                        Data = data
                    };
                }
                MissionStatus status = (MissionStatus)Enum.Parse(typeof(MissionStatus), taskStatus);

                mission.Status = status;
                mission.UpdateToClient();

                msg = "Success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply PackageInfo(string rid, int pageNum, int pageSize, string itemId, string type)
        {
            //::DONE:: todo yuyasong:增加了type  
            string msg;
            JsonObject data = new JsonObject();
            bool success = false;
            if (pageNum <= 0) { pageNum = 1; }
            if (pageSize <= 0) { pageSize = 20; }

            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(PackageInfo)} 找不到对应玩家，rid : {rid}";
                goto Replay;
            }

            try
            {
                //var entity = character.FindInAllEntity((e) => e.Deleted == false && (e is not SceneObject so || so.AreaID == 0));
                var entity = /*character.FindInAllEntity((e) => e.Deleted == false);*/GameApplication.DataManager.FindValueNoConstraint<EntityBase>(c => c.OwnerID == character.ID);
                //增加Type的判断
                if (!string.IsNullOrWhiteSpace(type))
                {
                    int _type = 0;
                    int.TryParse(type, out _type);
                    entity = (entity ?? new List<EntityBase>()).FindAll(it => it.TypeData?.TypeID == _type);
                }

                //增加ItemId的判断
                if (!string.IsNullOrEmpty(itemId))
                {
                    long _itemId = 0;
                    long.TryParse(itemId, out _itemId);
                    entity = (entity ?? new List<EntityBase>()).FindAll(it => it.Type == _itemId);
                }

                if (entity == null)
                {
                    msg = "empty Entity";
                    goto Replay;
                }

                JsonArray list = new JsonArray();
                entity.Skip(pageSize * (pageNum - 1)).Take(pageSize).ToList().ForEach(e =>
                {
                    var jsonEntity = new JsonObject
                    {
                        ["itemId"] = e.Type.ToString(),
                        ["name"] = e.TypeData.Name,
                        ["num"] = e.Count,
                        ["quality"] = e.TypeData.Quality.ToString(),
                        //["getTime"] = e.BirthDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        ["isDelete"] = false,
                        ["getTime"] = e.BirthDate.ToString("yyyy-MM-dd HH:mm:ss"),
                        //["detail"] = new JsonObject
                        //{
                        //    ["quality"] = e.TypeData.Quality.ToString(),
                        //    ["typeId"] = e.Type
                        //}

                    };
                    switch (e)
                    {
                        case Item:
                            jsonEntity["type"] = "道具";
                            jsonEntity["place"] = "背包";
                            break;
                        case Equip:
                            jsonEntity["type"] = "装备";
                            jsonEntity["place"] = "背包";
                            break;
                        case SceneObject:
                            jsonEntity["type"] = "家具";
                            jsonEntity["place"] = e.Area == null ? "身上" : "摆放中";
                            break;
                    }
                    list.Add(jsonEntity);
                });
                data["total"] = entity.Count;
                data["list"] = list;
                data["nickname"] = character.Name;
                data["account"] = character.AccountName;
                msg = "success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        Replay:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data.ToString()
            };
        }


        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply PlaceItem(string rid, int pageNum, int pageSize)
        {
            string msg;
            JsonObject data = new JsonObject();
            bool success = false;
            if (pageNum <= 0) { pageNum = 1; }
            if (pageSize <= 0) { pageSize = 20; }

            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(PlaceItem)} 找不到对应玩家，rid : {rid}";
                goto Replay;
            }

            try
            {
                var areas = GameApplication.DataManager.FindValue<Area>(area => area.OwnerID == character.ID);
                var sos = new List<SceneObject>();
                foreach (var area in areas)
                {
                    sos.AddRange(area.FindObject(p => true));
                }
                // 数据有延迟，这里为了保证数据准确性是从数据库里拿的数据，注意这个接口本来就有延迟
                // var sos = GameApplication.DataManager.LoadGameObjects<SceneObject>((p => p.OwnerID == character.ID && p.Deleted != true && p.AreaID != 0),
                //     typeof(EntityBase), false, null, 0).Values.ToList();
                JsonArray list = new JsonArray();
                sos.Skip(pageSize * (pageNum - 1)).Take(pageSize).ToList().ForEach(so =>
                {
                    var jsonEntity = new JsonObject
                    {
                        ["itemId"] = so.ID.ToString(),
                        ["name"] = so.TypeData?.Name,
                        ["num"] = so.Count,
                        ["place"] = so.Transform?.Position.ToString(),
                        ["typeID"] = so.Type
                    };
                    list.Add(jsonEntity);
                });
                data["total"] = sos.Count;
                data["list"] = list;
                msg = "success";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        Replay:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data.ToString()
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply TradeItem(string rid, int pageNum, int pageSize, string type)
        {
            var ch = GetCharacterByRoleId(rid);
            if (ch == null)
            {
                return new LeitingCommonReply()
                {
                    Success = false,
                    Msg = "找不到角色",
                };
            }

            pageNum = pageNum == 0 ? 1 : pageNum;
            pageSize = pageSize == 0 ? 20 : pageNum;

            var item_datas = GameApplication.DataManager.LoadGameObjects<AuctionItem>(i => i.OwnerId == ch.ID, cache: false);

            var selecet_items = item_datas.Values.Skip(pageSize * (pageNum - 1)).Take(pageSize).ToList();

            var item_json_array = new JsonArray();
            selecet_items.ForEach(i => item_json_array.Add(i.GetLeitingJson()));

            return new LeitingCommonReply()
            {
                Success = true,
                Data = (new JsonObject() { { "total", item_datas.Count }, { "list", item_json_array } }).ToString()
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply DeductItem(string rid, string itemId, int num)
        {
            string msg;
            bool success = false;

            try
            {
                var character = GetCharacterByRoleId(rid);
                if (character == null)
                {
                    msg = $"扣除失败, {nameof(DeductItem)} 找不到对应玩家，rid : {rid}";
                    goto Replay;
                }

                if (!long.TryParse(itemId, out var itemIdLong))
                {
                    msg = $"格式错误, {nameof(DeductItem)} {itemId}不是long";
                    goto Replay;
                }

                var entity = character.FindInAllEntity(e => e.Type == itemIdLong).FirstOrDefault();
                var virtual_currency = character.GetVirtualCurrency(itemIdLong, false);
                if (entity == null && virtual_currency == null)
                {
                    msg = $"扣除失败, {nameof(DeductItem)} Can not find itemId {itemId}";
                    goto Replay;
                }

                if (entity != null && !character.DeleteEntity(entity.Type, num, nameof(DeductItem)))
                {
                    msg = $"扣除{entity.TypeData.Name}失败， {nameof(DeductItem)} can not delete entity {itemId}, count: {num}";
                    goto Replay;
                }

                if (virtual_currency != null && virtual_currency.ForcePay(num, nameof(DeductItem)) == null)
                {
                    msg = $"扣除{virtual_currency.TaSetName}失败， {nameof(DeductItem)} can not delete entity {itemId}, count: {num}";
                    goto Replay;
                }

                msg = $"扣除{itemId} 成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"扣除失败, {e}";
            }


        Replay:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply RecoveryItem(string rid, string itemId, int num, string detail)
        {
            string msg;
            bool success = false;

            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"{nameof(RecoveryItem)} 找不到对应玩家，rid : {rid}";
                goto Replay;
            }

            try
            {
                if (!long.TryParse(itemId, out var itemIdLong))
                {
                    msg = $"格式错误, {nameof(RecoveryItem)} Can not find itemId {itemId}";
                    goto Replay;
                }

                //// 缓存中的数据, 假设恢复物件已存在，只是改数量
                //EntityBase? entity = character.FindInAllEntity(itemIdLong);
                //if (entity == null)
                //{
                //    // 数据库的Deleted数据
                //    entity = LoadFromDB(p => p.ID == itemIdLong);
                //}
                //if (entity == null)
                //{
                //    msg = $"恢复失败, {nameof(RecoveryItem)} Can not find itemId {itemId}";
                //    goto Replay;
                //}
                //if (entity.OwnerID != character.ID)
                //{
                //    msg = $"恢复失败, {nameof(RecoveryItem)} item {itemId} not belong to {rid}";
                //    goto Replay;
                //}

                //entity.Count = num;
                //entity.Deleted = false;
                //entity.Save();

                character.CreateEntity(itemIdLong, num, null, "恢复删除道具");

                msg = $"恢复{itemIdLong}成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        Replay:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }


        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply OffItem(string rid, string tradeId)
        {
            return new LeitingCommonReply()
            {
                Success = false,
                Msg = "TODO 没有交易的功能目前",
                Data = "{}"
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply RetractItem(string rid, string itemId)
        {
            string msg;
            bool success = false;
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                msg = $"收回物品失败 {nameof(RetractItem)} 找不到对应玩家，rid : {rid}";
                goto Replay;
            }

            try
            {
                if (!long.TryParse(itemId, out var itemIdLong))
                {
                    msg = $"收回物品失败, {nameof(RetractItem)} 找不到对应的物品 {itemId}";
                    goto Replay;
                }
                var sos = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(i => i.OwnerID == character.ID && i.Type == itemIdLong && i.AreaID != 0);
                //var so = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(itemIdLong);
                foreach (var so in sos)
                {

                    if (so == null)
                    {
                        msg = $"收回物品失败, {nameof(RetractItem)} 找不到对应的物品 {itemId}";
                        goto Replay;
                        // so = GameApplication.DataManager.LoadGameObject<SceneObject>(
                        //     (p => p.OwnerID == character.ID && p.Deleted != true && p.AreaID != 0),
                        //     typeof(EntityBase), false, false, true);
                    }
                    else
                    {
                        if (so.AreaID == 0)
                        {
                            msg = $"收回物品失败, {nameof(RetractItem)} 物品 {so.Name} 已经被回收";
                            goto Replay;
                        }
                    }

                    if (so == null)
                    {
                        msg = $"收回物品失败, {nameof(RetractItem)} 找不到对应的物品 {itemId}";
                        goto Replay;
                    }

                    if (!so.TypeData.CanPickUp)
                    {
                        msg = $"收回物品失败, {nameof(RetractItem)} 物品不可回收 {itemId}";
                        goto Replay;
                    }

                    //if (so.TypeData.Category == SceneObjectCategory.未设置)
                    //{
                    //    return WrapResult(false, LocalityStrings.limitedPickUp.DefaultString);
                    //}
                    if (so.Owner?.LeftBackpackCapacity <= 0)
                    {
                        //背包已满
                        if (so.TypeData.Unit == 1)
                        {
                            //不可叠加
                            msg = $"收回物品失败, {nameof(RetractItem)} 背包已满";
                            goto Replay;
                        }

                        var maxCount = so.TypeData.Unit == 0 ? int.MaxValue : so.TypeData.Unit;
                        if (so.Owner.FindInAllEntity(p => p.Type == so.Type && p.Count < maxCount, checkBag: true).Count == 0)
                        {
                            //没有可叠加的分类
                            // return WrapResult(false, LocalityStrings.limitedBackpackCapacity.DefaultString);
                            msg = $"收回物品失败, {nameof(RetractItem)} 无法回收的分类";
                            goto Replay;

                        }
                    }

                    if (!so.CanPickUp(out LocalityString error))
                    {
                        // return WrapResult(false, error);
                        msg = $"收回物品失败, {nameof(RetractItem)} {error}";
                        goto Replay;
                    }


                    if (so.Area != null)
                    {
                        so.Area.IncreaseSceneObjectCacheVersion(so);
                        so.Area.Save();
                        character.Recycling(so.Area, so, nameof(RetractItem));
                        so.Save();
                    }
                    else
                    {
                        var now = DateTimeExtension.Now;
                        so.AreaID = 0;
                        so.Area = null;
                        so.LastPickUpTime = now;
                        so.LastChangeTime = now;
                        so.ParentID = 0;
                        so.Transform = null;
                        if (so.Owner == null || so.OwnerID == character.ID)
                        {
                            character.AddOrOverlayEntity(so, nameof(RetractItem), null, so.AreaID, ClientNotificationMode.Ignore);
                        }
                        else
                        {
                            so.Owner.AddOrOverlayEntity(so, nameof(RetractItem), null, so.AreaID, ClientNotificationMode.Ignore);
                        }
                        so.Save();
                    }
                    if (Constants.AllowTaLog)
                    {
                        LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                            .WriteProperties()
                                .WriteEntry("action", "回收家具")
                                .WriteEntry("reason", "后台操作")
                                .WriteObject("extend_info")
                                    .WriteRawEntry("rid", rid)
                                    .WriteRawEntry("itemId", itemId)
                                    .WriteStringEntry("player_id", character.ID)
                                    .WriteEntry("player_name", character.Name)
                                .WriteToTaLogger();
                    }
                }
                msg = $"收回成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }
        Replay:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply SendMailByReceiverList(string title, string content, int validTime, string receiverList, string attachment, string author)
        {
            string msg;
            List<string> errorId = new List<string>();
            List<long> receiverIdLong = new List<long>();
            if (!string.IsNullOrEmpty(receiverList))
            {
                string[] receiverIds = receiverList.Split(',');
                foreach (var idStr in receiverIds)
                {
                    if (!long.TryParse(idStr, out var receiverId))
                    {
                        errorId.Add(idStr);
                        continue;
                    }
                    receiverIdLong.Add(receiverId);
                }
            }

            if (errorId.Count > 0)
            {
                msg = $"发送邮件失败, 无法解析receiverList, receiverList: {receiverList}, error list {errorId}";

                return new LeitingCommonReply()
                {
                    Success = false,
                    Msg = msg
                };
            }

            JsonDynamicList<AwardBase>? awards = null;

            if (!string.IsNullOrEmpty(attachment) && !string.IsNullOrWhiteSpace(attachment))
            {
                awards = new JsonDynamicList<AwardBase>();
                JsonArray jsonArray = (JsonArray)JsonSerializer.Deserialize(attachment);
                foreach (var jo in jsonArray)
                {
                    if (!long.TryParse(jo["itemId"], out var itemTypeId))
                    {
                        msg = $"发送邮件失败, 无法解析的itemId {jo["itemId"]}";
                        return new LeitingCommonReply()
                        {
                            Success = false,
                            Msg = msg
                        };
                    }
                    int num = jo["num"];
                    // jo["detail"]
                    if (itemTypeId > 100)
                    {
                        awards.Add(new ObjectAward(itemTypeId, num));
                    }
                    else
                    {
                        awards.Add(new VirtualCurrencyAward(itemTypeId, num));
                    }
                }
            }
            var deadline = DateTimeExtension.Now.AddSeconds(validTime);
            receiverIdLong.ForEach(chara_id =>
            {
                var ch = GameApplication.DataManager.FindValue<Character>(chara_id);
                if (ch != null)
                {
                    if (awards != null && awards.Count > 0)
                    {
                        ch.AddMail(title, content, awards, true, deadline, MailType.Award, "后台发送个人邮件", "", author);
                    }
                    else
                    {
                        ch.AddMail(title, content, null, true, deadline, MailType.Common, "后台发送个人邮件", "", author);
                    }
                }
            });


            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "发送成功"
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply SendMail(string title, string content, int validTime,
            bool isAllServers, bool isBroadcast, string receiverList, string attachment, string minRegisterTime, string maxRegisterTime, string mailOrderTime,
            int minLevel, int maxLevel, string channels, string author, string foreignkey)
        {
            //::DONE:: todo yuyasong：增加了参数string minRegisterTime,string maxRegisterTime,int minLevel,int maxLevel,string channels,string author 

            bool onlyRegistered = true;
            string msg;
            bool success = false;
            List<string> errorId = new List<string>();
            try
            {
                ConditionGroup conditionGroup = new ConditionGroup();
                if (!isAllServers && !isBroadcast)
                {
                    if (!string.IsNullOrEmpty(receiverList))
                    {
                        string[] receiverIds = receiverList.Split(',');
                        List<long> receiverIdLong = new List<long>();
                        foreach (var idStr in receiverIds)
                        {
                            if (!long.TryParse(idStr, out var receiverId))
                            {
                                errorId.Add(idStr);
                                continue;
                            }
                            receiverIdLong.Add(receiverId);
                        }
                        conditionGroup.Add(new PropertyAnyCondition()
                        {
                            PropertyName = "ID",
                            Value = receiverIdLong
                        });
                    }

                    if (errorId.Count > 0)
                    {
                        msg = $"发送邮件失败, 无法解析receiverList, receiverList: {receiverList}, error list {errorId}";
                        goto Reply;
                    }
                    if (conditionGroup.Count == 0)
                    {
                        msg = $"发送邮件失败, 没有可接受的对象, isAllServers: {isAllServers}, isBroadcast: ${isBroadcast}, receiverList: {receiverList}";
                        goto Reply;
                    }
                }
                if (!string.IsNullOrEmpty(maxRegisterTime))
                {
                    conditionGroup.Add(new PlayerRegisterTimeCondition(minRegisterTime, string.IsNullOrEmpty(maxRegisterTime) ? "" : maxRegisterTime));
                }

                if (minLevel > 0 || maxLevel > 0)
                {
                    conditionGroup.Add(new PlayerLevelCondition() { MinLevel = minLevel, MaxLevel = maxLevel });
                }

                if (!string.IsNullOrEmpty(channels) && channels != "")
                {
                    var channel_list = channels.Split(",").ToList();
                    conditionGroup.Add(new ContainsChannelCondition() { ChannelList = channel_list });
                }

                var deadline = DateTimeExtension.Now.AddSeconds(validTime);
                List<MailLanguage> languages = new List<MailLanguage>()
                {
                    new ()
                    {
                        Language = "cn",
                        Content = content,
                        Title = title,
                        Sender = GameModel.Character.System.Name
                    }
                };

                //更改发件人
                if (!string.IsNullOrWhiteSpace(author))
                {
                    if (languages != null)
                    {
                        foreach (var l in languages)
                        {
                            l.Sender = author;
                        }
                    }
                }

                JsonDynamicList<AwardBase>? awards = null;

                if (!string.IsNullOrEmpty(attachment) && !string.IsNullOrWhiteSpace(attachment))
                {
                    awards = new JsonDynamicList<AwardBase>();
                    JsonArray jsonArray = (JsonArray)JsonSerializer.Deserialize(attachment);
                    foreach (var jo in jsonArray)
                    {
                        if (!long.TryParse(jo["itemId"], out var itemTypeId))
                        {
                            msg = $"发送邮件失败, 无法解析的itemId {jo["itemId"]}";
                            goto Reply;
                        }
                        int num = jo["num"];
                        // jo["detail"]
                        if (itemTypeId > 100)
                        {
                            awards.Add(new ObjectAward(itemTypeId, num));
                        }
                        else
                        {
                            awards.Add(new VirtualCurrencyAward(itemTypeId, num));
                        }
                    }
                }

                var mailType = string.IsNullOrEmpty(attachment) ? MailType.Notice : MailType.Award;
                DateTime orderTime;
                if (string.IsNullOrEmpty(mailOrderTime))
                {
                    orderTime = onlyRegistered ? DateTimeExtension.Now : DateTimeExtension.MinTime;
                }
                else
                {
                    orderTime = Convert.ToDateTime(mailOrderTime);
                }

                //GameApplication.UserManager.SendSystemMail(languages, mailType, awards, orderTime, deadline,
                //    conditionGroup, minRegisterTime, maxRegisterTime, minLevel, maxLevel, channels, author);
                var mail = GameApplication.DataManager.FindValue<SystemMail>(m => m.LeitingForeignKey == foreignkey).FirstOrDefault();
                if (mail != null)
                {
                    if (languages != null)
                    {
                        languages.First(l => l.Language == "cn").Title = string.IsNullOrEmpty(title) ? mail.Title : title;
                        languages.First(l => l.Language == "cn").Content = string.IsNullOrEmpty(content) ? mail.Content : content;
                        mail.Languages = languages;
                    }
                    mail.Type = mailType;
                    mail.Awards = awards;

                    //这里需要加入判断，否则编辑的时候不传值会全部强行重置
                    if (!string.IsNullOrEmpty(mailOrderTime))
                    {
                        mail.OrderTime = orderTime;
                    }
                    if (validTime != 0)
                    {
                        mail.DeadLine = deadline.GetNextRefreshTime();
                    }
                    //mail.Conditions = conditionGroup;
                    mail.LeitingForeignKey = string.IsNullOrEmpty(foreignkey) ? mail.ID.ToString() : foreignkey;
                    mail.Save();
                    msg = "";
                }
                else
                {
                    GameApplication.UserManager.SendSystemMail(languages, mailType, awards, orderTime, deadline, conditionGroup, leitingkey: foreignkey);

                    msg = "发送邮件成功";
                }
                success = true;
            }
            catch (Exception e)
            {
                msg = e.ToString();
            }

        Reply:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract]
        public LeitingCommonReply SendGift(string account, string rid, string op, long timestamp, string title, string content, string rewards, string requestId)
        {
            long user_id = 0;
            if (!long.TryParse(rid, out user_id))
            {
                return new LeitingCommonReply()
                {
                    Msg = "rid" + rid + "格式错误",
                    Success = false
                };
            }
            var character = GameApplication.DataManager.FindValue<Character>(user_id);

            if (character == null)
            {
                return new LeitingCommonReply()
                {
                    Msg = "找不到玩家" + rid,
                    Success = false
                };
            }

            //if (!character.ListDatas.GiftCodeStatus.ContainsKey(requestId))
            //{
            //    Logger.Error.Write("玩家" + character.ID + "未经发送就请求了收礼接口");
            //    return new LeitingCommonReply()
            //    {
            //        Msg = "玩家" + character.ID + "未经发送就请求了收礼接口",
            //        Success = false
            //    };
            //}

            //if (character.ListDatas.GiftCodeStatus[requestId] == GiftCodeStatus.已接收)
            //{
            //    Logger.Error.Write("玩家" + character.ID + "已经接受了这个礼包");
            //    return new LeitingCommonReply()
            //    {
            //        Msg = "玩家" + character.ID + "已经接受了这个礼包",
            //        Success = false
            //    };
            //}

            //character.ListDatas.GiftCodeStatus[requestId] = GiftCodeStatus.已接收;
            //character.ListDatas.Save();

            var awards = new JsonDynamicList<AwardBase>();
            if (!String.IsNullOrEmpty(rewards))
            {
                var array = JsonObject.Parse(rewards) as JsonArray;
                if (array == null)
                {
                    return new LeitingCommonReply()
                    {
                        Msg = "奖励参数错误" + rewards,
                        Success = false
                    };
                }
                foreach (var json_ob in array)
                {
                    var award_str_id = json_ob["class_id"];
                    var count = json_ob["amount"];
                    if (String.IsNullOrEmpty(award_str_id) || count == 0)
                    {
                        return new LeitingCommonReply()
                        {
                            Msg = "奖励参数错误" + rewards,
                            Success = false
                        };
                    }

                    long award_id;
                    if (!long.TryParse(award_str_id, out award_id))
                    {
                        return new LeitingCommonReply()
                        {
                            Msg = "奖励参数错误" + rewards,
                            Success = false
                        };
                    }

                    if (award_id <= 100)
                    {
                        awards.Add(new VirtualCurrencyAward() { Type = award_id, Value = count });
                    }
                    else
                    {
                        awards.Add(new ObjectAward() { Type = award_id, Count = count });
                    }
                }
            }

            character.AddMail(title, content, awards, true, DateTimeExtension.Now.AddDays(360), MailType.Award, "雷霆礼包发放"/*, extendInfo: requestId*/);

            return new LeitingCommonReply()
            {
                Msg = string.Empty,
                Success = true
            };
        }

        //[OperationContract(SkipClientImpl = true)]
        //public LeitingCommonReply SendMail(string title, string content, int validTime,
        //    bool isAllServers, bool isBroadcast, bool onlyRegistered, string receiverList, string attachment)
        //{
        //    string msg;
        //    bool success = false;
        //    List<string> errorId = new List<string>();
        //    try
        //    {
        //        ConditionGroup? conditionGroup = null;
        //        if (!isAllServers && !isBroadcast)
        //        {
        //            conditionGroup = new ConditionGroup();
        //            if (!string.IsNullOrEmpty(receiverList))
        //            {
        //                string[] receiverIds = receiverList.Split(',');
        //                List<long> receiverIdLong = new List<long>();
        //                foreach (var idStr in receiverIds)
        //                {
        //                    if (!long.TryParse(idStr, out var receiverId))
        //                    {
        //                        errorId.Add(idStr);
        //                        continue;
        //                    }
        //                    receiverIdLong.Add(receiverId);
        //                }
        //                conditionGroup.Add(new PropertyAnyCondition()
        //                {
        //                    PropertyName = "ID",
        //                    Value = receiverIdLong
        //                });
        //            }

        //            if (errorId.Count > 0)
        //            {
        //                msg = $"发送邮件失败, 无法解析receiverList, receiverList: {receiverList}, error list {errorId}";
        //                goto Reply;
        //            }
        //            if (conditionGroup.Count == 0)
        //            {
        //                msg = $"发送邮件失败, 没有可接受的对象, isAllServers: {isAllServers}, isBroadcast: ${isBroadcast}, receiverList: {receiverList}";
        //                goto Reply;
        //            }
        //        }

        //        var deadline = DateTimeExtension.Now.AddSeconds(validTime);
        //        List<MailLanguage> languages = new List<MailLanguage>()
        //    {
        //        new ()
        //        {
        //            Language = "cn",
        //            Content = content,
        //            Title = title,
        //            Sender = Character.System.Name
        //        }
        //    };

        //        JsonDynamicList<AwardBase>? awards = null;

        //        if (!string.IsNullOrEmpty(attachment) && !string.IsNullOrWhiteSpace(attachment))
        //        {
        //            awards = new JsonDynamicList<AwardBase>();
        //            JsonArray jsonArray = (JsonArray)JsonSerializer.Deserialize(attachment);
        //            foreach (var jo in jsonArray)
        //            {
        //                if (!long.TryParse(jo["itemId"], out var itemTypeId))
        //                {
        //                    msg = $"发送邮件失败, 无法解析的itemId {jo["itemId"]}";
        //                    goto Reply;
        //                }
        //                int num = jo["num"];
        //                // jo["detail"]
        //                awards.Add(new ObjectAward(itemTypeId, num));
        //            }
        //        }

        //        var mailType = string.IsNullOrEmpty(attachment) ? MailType.Common : MailType.Award;
        //        var orderTime = onlyRegistered ? DateTimeExtension.Now : DateTimeExtension.MinTime;
        //        GameApplication.UserManager.SendSystemMail(languages, mailType, awards, orderTime, deadline,
        //            conditionGroup);

        //        msg = "发送邮件成功";
        //        success = true;
        //    }
        //    catch (Exception e)
        //    {
        //        msg = e.ToString();
        //    }

        //Reply:
        //    return new LeitingCommonReply()
        //    {
        //        Success = success,
        //        Msg = msg
        //    };
        //}


        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply MailList(string rid, int pageNum, int pageSize)
        {
            string msg;
            JsonObject data = new JsonObject();
            bool success = false;
            if (pageNum <= 0) { pageNum = 1; }
            if (pageSize <= 0) { pageSize = 20; }

            try
            {
                var character = GetCharacterByRoleId(rid);
                if (character == null)
                {
                    msg = $"{nameof(MailList)} 找不到对应玩家，rid : {rid}";
                    goto Reply;
                }

                var list = new JsonArray();
                var mails = GameApplication.DataManager.LoadGameObjects<Mail>(m => m.OwnerID == character.ID).Values.OrderByDescending(m => m.ID);
                mails.Skip(pageSize * (pageNum - 1)).Take(pageSize).ToList().ForEach(mail =>
                {
                    list.Add(LeitingMailObject(mail));
                });
                data["total"] = mails.Count();
                data["list"] = list;
                msg = "获取邮件列表成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"获取邮件列表失败 {e}";
            }


        Reply:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data.ToString()
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply ZoneMailList(int pageNum, int pageSize)
        {
            string msg;
            JsonObject data = new JsonObject();
            bool success = false;
            if (pageNum <= 0) { pageNum = 1; }
            if (pageSize <= 0) { pageSize = 20; }

            try
            {
                var sms = GameApplication.DataManager.FindValue<SystemMail>(p => p.Deleted == false).OrderByDescending(p => p.ID)
                    .ToList();
                var list = new JsonArray();
                // sms.Skip(pageSize * (pageNum-1)).Take(pageSize).ToList().ForEach(sysMail =>
                // {
                //     list.Add(LeitingSystemMailObject(sysMail));
                // });
                foreach (var sysMail in sms.Skip(pageSize * (pageNum - 1)).Take(pageSize))
                {
                    list.Add(LeitingSystemMailObject(sysMail));
                }
                data["total"] = sms.Count;
                data["list"] = list;
                msg = "获取系统邮件列表成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"获取系统邮件列表失败 {e}";
            }

            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg,
                Data = data.ToString()
            };
        }


        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply DeleteMail(string rid, string mailId)
        {
            string msg;
            bool success = false;

            try
            {
                var character = GetCharacterByRoleId(rid);
                if (character == null)
                {
                    msg = $"删除邮件失败 {nameof(DeleteMail)} 找不到对应玩家，rid : {rid}";
                    goto Reply;
                }

                if (!long.TryParse(mailId, out var mailIdLong))
                {
                    msg = $"删除邮件失败 mailId 格式不对，找不到邮件{mailId}";
                    goto Reply;
                }

                var mail = character.Mails.FirstOrDefault(mail => mail.ID == mailIdLong && !mail.Deleted);
                if (mail == null)
                {
                    msg = $"删除邮件失败 找不到邮件{mailId}";
                    goto Reply;
                }
                character.DeleteMail(mailIdLong);

                msg = "删除邮件成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"删除邮件失败 {e}";
            }
        Reply:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply DeleteAllMail(string mailId)
        {
            string msg;
            bool success = false;

            try
            {
                //if (!long.TryParse(mailId, out var mailIdLong))
                //{
                //    msg = $"删除全服邮件失败 mailId 格式不对，找不到邮件{mailId}";
                //    goto Reply;
                //}

                var systemMail = GameApplication.DataManager.FindValue<SystemMail>(m => m.LeitingForeignKey == mailId).FirstOrDefault();
                if (systemMail == null)
                {
                    msg = $"删除全服邮件失败 找不到邮件{mailId}";
                    goto Reply;
                }

                GameApplication.DataManager.FindValue<Mail>(m => m.Source == systemMail.ID).ForEach(mail =>
                {
                    mail.Delete();
                    if (mail.Owner.IsOnline)
                    {
                        mail.Owner.SendGoogleProto(ClientMethod.DeleteMail, new IDMessage() { Id = mail.ID });
                    }
                });

                systemMail.Deleted = true;
                systemMail.Save();

                msg = "删除全服邮件成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"删除全服邮件失败 {e}";
            }
        Reply:
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply StartBroadcast(string content, bool isAllServers, string startTime, string endTime, long broadCastId, int interval, string timeSpan)
        {
            string msg = string.Empty;
            var test_msg = "";
            //todo yuyasong:这块内容没有不全，业务中需要将循环次数更改成开始时间
            try
            {
                int defaultTimes = 1;

                long _startTime = DateTimeExtension.ToTimestamp(Convert.ToDateTime(startTime));
                long _endTime = DateTimeExtension.ToTimestamp(Convert.ToDateTime(endTime));
                var req = new RollMessageDataWithTimeSet();

                req.RollMessage = new RollMessageData();
                req.RollMessage.Message = new PicaLocalityStringMessage();

                req.RollMessage.Times = defaultTimes;

                int rollType = 10;
                req.RollMessage.Type = rollType;
                req.StartTime = _startTime;
                req.Deadline = _endTime;

                DateTime repeatTime = DateTimeExtension.Now;
                DateTime.TryParse(startTime, out repeatTime);
                req.RepeatTime = DateTimeExtension.ToTimestamp(repeatTime);
                req.Id = broadCastId;
                req.RollMessage.Message.DefaultFormatString = content;
                var roll_message = GameApplication.DataManager.FindValue<RollMessage>(r => r.Foreignkey == broadCastId).FirstOrDefault();
                if (roll_message == null)
                {
                    var rm = RowAdapter.Create<RollMessage>();
                    rm.SetData(req);
                    if (string.IsNullOrEmpty(timeSpan))
                    {
                        if (interval <= 60)
                        {
                            rm.RunForEachMinute = true;
                        }
                        else
                        {

                            for (long i = req.StartTime; i <= req.Deadline; i += interval)
                            {
                                rm.IntervalTimes.Add(DateTimeExtension.ConvertFromTimestamp(i));
                            }
                        }
                    }
                    else
                    {
                        var time_spam = TimeSpan.Parse(timeSpan);

                        rm.IntervalTimes.Add(DateTime.Today.Add(time_spam));
                    }
                    rm.Save();
                    var rms = GameApplication.DataManager.FindValue<RollMessage>(p => true).OrderBy(p => p.ID).ToList();

                    foreach (var rm0 in rms)
                    {
                        test_msg += rm0.Content + " ";
                    }

                }
                else
                {
                    roll_message.SetData(req);
                }

            }
            catch (Exception ex)
            {
                return new LeitingCommonReply()
                {
                    Success = false,
                    Msg = ex.ToString()
                };
            }

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "添加成功"
            };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply DeleteBroadcast(long broadcastId)
        {
            long _id = broadcastId;
            //long.TryParse(broadcastId, out _id);
            //todo yuyasong:这块删除跑马灯没有功能，需要补全
            var roll_message = GameApplication.DataManager.FindValue<RollMessage>(r => r.Foreignkey == _id).FirstOrDefault();

            if (roll_message != null)
            {
                roll_message.DeadLine = DateTimeExtension.Now;
                roll_message.Save();

                return new LeitingCommonReply()
                {
                    Success = true,
                    Msg = "删除成功"
                };
            }

            return new LeitingCommonReply()
            {
                Success = false,
                Msg = $"删除失败，请确认改BroadCaseId:{broadcastId}是否存在."
            };
        }


        /// <summary>
        /// 这个StartBroacast是直接推送到前端去
        /// </summary>
        /// <param name="content"></param>
        /// <param name="num"></param>
        /// <param name="interval"></param>
        /// <returns></returns>
        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply StartBroadcast1(string content, int num, int interval)
        {
            string msg;
            bool success = false;
            if (interval <= 0) interval = 1;

            try
            {
                RollMessageData rmd = new RollMessageData();
                rmd.Message = PicaLocalityStrings.GetLocalityStringMessage(PicaLocalityStrings.GetUndefinedString(content));
                rmd.Times = num;
                rmd.Type = 10;
                rmd.Interval = interval;
                GameApplication.UserManager.BroadCast(ClientMethod.ShowRollMessage, rmd);
                msg = "推送广播成功";
                success = true;
            }
            catch (Exception e)
            {
                msg = $"推送广播失败 {e}";
            }
            return new LeitingCommonReply()
            {
                Success = success,
                Msg = msg
            };
        }


        [OperationContract]
        public LeitingCommonReply SetServerPlayerCountBySDKMedia(string media, int count)
        {
            var conf = GameApplication.DataManager.FindValue<ServerPlayerLimitType>(s => s.SDKMedia == media).FirstOrDefault();
            if (conf == null)
            {
                conf = RowAdapter.Create<ServerPlayerLimitType>();
                conf.SDKMedia = media;
                conf.PlayerLimit = count;
            }
            else
            {
                conf.PlayerLimit = count;
            }
            conf.Save();
            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "修改服务器人数上限成功"
            };
        }

        [OperationContract]
        public PicaCommenReply GrpcConfigProto(string value)
        {

            var config = ProtoUtility.TypeGroupDataCache.ProtoData;
            var bytes = ProtoHelp.GetBytes(config, CompressType.GZip);
            var senddata = Convert.ToBase64String(bytes);
            return new PicaCommenReply() { Success = true, Data = senddata };
        }

        [OperationContract]
        public PicaCommenReply GrpcConfigVersion()
        {
            var conf = GameApplication.DataManager.FindValue<ConfigVersion>(1);
            return new PicaCommenReply() { Success = true, Data = conf == null ? "null" : conf.GetVersion(ProtoUtility.TypeGroupDataCache) };
        }


        /// <summary>
        /// 收回摆放物品
        /// </summary>
        /// <param name="rid">角色id</param>
        /// <param name="sceneId">场景id</param>
        /// <param name="itemId">物品id</param>
        /// <returns></returns>
        [OperationContract]
        public LeitingCommonReply RetractRoomOrItem(string rid, string sceneId, string itemId)
        {
            try
            {
                long _rid = 0;
                long.TryParse(rid, out _rid);

                long _sceneId = 0;
                long.TryParse(sceneId, out _sceneId);

                long _itemId = 0;
                long.TryParse(itemId, out _itemId);

                Action<IEntityOwner, SceneObject, Area> do_pick_up = (owner, so, area) =>
                {
                    if (owner is Organization org)
                    {
                        org.Recycling(so.Area, so);
                    }
                    else if (owner is Character ch)
                    {
                        if (so.OwnerID != ch.ID && ch.AdminType > 0)
                        {
                            //如果管理员回收不是自己的物品，就变成自己的
                            so.OwnerID = ch.ID;
                            so.OwnerCategory = OwnerCategory.Player;
                            so.Save();
                        }

                        //房门和小屋不回收
                        if (so.GetFunction<PicaGoToPersonalSpaceFunction>() != null)
                        {
                            return;
                        }

                        ch.Recycling(area, so, "回收房间物品");
                    }
                    area.UpdateCacheVersionToSceneServer();
                };

                var scene = GameApplication.SceneManager.GetScene(_sceneId, true);
                if (scene == null)
                {
                    return new LeitingCommonReply()
                    {
                        Success = false,
                        Msg = "未找到目标场景."
                    };
                }
                if (scene.Dimension.Type == DimensionType.PicaRoom || scene.Dimension.Type == DimensionType.PicaGarden)
                {
                    PicaRoomScene? room_scene = scene as PicaRoomScene;

                    //增加OwnerId判断:场景的Owner必须对应上 - bug fix：20230411
                    if (room_scene == null || room_scene.OwnerID != _rid)
                    {
                        return new LeitingCommonReply()
                        {
                            Success = false,
                            Msg = "操作失败，未找到对应的场景."
                        };
                    }

                    if (_itemId > 0)
                    {
                        return RetractItem(rid, itemId);
                    }

                    room_scene.Areas.ForEach(a => a.Value.Objects.ForEach(o => do_pick_up(room_scene.Owner, o.Value, a.Value)));
                    room_scene.Save();
                    room_scene.Owner.Save();
                }
                else if (scene.Dimension.Type == DimensionType.Organization)
                {
                    OrganizationScene? org_scene = scene as OrganizationScene;
                    if (org_scene == null)
                    {
                        return new LeitingCommonReply()
                        {
                            Success = false,
                            Msg = "操作失败，未找到对应的场景."
                        };
                    }
                    if (_itemId > 0)
                    {
                        if (org_scene.Areas.First().Value.Objects.TryGetValue(long.Parse(itemId), out var so))
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "操作失败，未找到对应的家具."
                            };
                        }

                        org_scene.Organization.Recycling(org_scene.Areas.First().Value, so);
                        return new LeitingCommonReply()
                        {
                            Success = true,
                            Msg = "小屋回收成功."
                        };
                    }
                    else
                    {
                        org_scene.Areas.ForEach(a => a.Value.Objects.ForEach(o => do_pick_up(org_scene.Owner, o.Value, a.Value)));
                        org_scene.Save();
                        org_scene.Organization.Save();
                    }
                }
            }
            catch { }

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "小屋回收成功."
            };
        }

        /// <summary>
        /// 限制访问小屋
        /// </summary>
        /// <param name="roomId">小屋id</param>
        /// <returns></returns>
        [OperationContract]
        public LeitingCommonReply BanVisit(string rid, string sceneId, int time, string reason)
        {
            try
            {
                long _sceneId = 0;
                long.TryParse(sceneId, out _sceneId);

                long _rid = 0;
                long.TryParse(rid, out _rid);

                var room = GameApplication.SceneManager.GetScene(_sceneId, true) as PicaRoomScene;

                if (room == null || room.OwnerID != _rid)
                {
                    return new LeitingCommonReply()
                    {
                        Success = false,
                        Msg = "操作失败，未找到对应的小屋."
                    };
                }
                if (time < 0)//永封
                {
                    room.FrozenStopTime = DateTimeExtension.Now.AddYears(99);
                }
                else
                {
                    room.FrozenStopTime = DateTimeExtension.Now.AddMinutes(time);
                }
                room.Save();
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "禁止访问")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("sceneId", sceneId)
                                .WriteEntry("frozenTime", room.FrozenStopTime.ToString("yyyy-MM-dd HH:mm:ss"))
                                .WriteStringEntry("player_id", room.OwnerID)
                                .WriteEntry("player_name", room.Owner.Name)
                            .WriteToTaLogger();
                }
            }
            catch { }

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "小屋被限制访问。"
            };
        }

        [OperationContract]
        public LeitingCommonReply UnBanVisit(string rid, string sceneId, string reason)
        {
            try
            {
                long _sceneId = 0;
                long.TryParse(sceneId, out _sceneId);

                long _rid = 0;
                long.TryParse(rid, out _rid);

                var room = GameApplication.SceneManager.GetScene(_sceneId, true) as PicaRoomScene;
                if (room == null || room.OwnerID != _rid)
                {
                    return new LeitingCommonReply()
                    {
                        Success = false,
                        Msg = "操作失败，未找到对应的小屋."
                    };
                }
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "解除禁止访问")
                            .WriteEntry("reason", reason)
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteRawEntry("sceneId", sceneId)
                                .WriteStringEntry("player_id", room.OwnerID)
                                .WriteEntry("player_name", room.Owner.Name)
                            .WriteToTaLogger();
                }

                room.FrozenStopTime = DateTimeExtension.Now;
                room.Save();
            }
            catch { }

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "小屋访问限制解除。"
            };
        }


        [OperationContract]
        public LeitingCommonReply FunctionModuleList(string featureName, string type)
        {
            SilenceCategoryVersion silence_type;
            var res = new JsonObject();
            var res_list = new JsonArray();
            if (String.IsNullOrEmpty(featureName))
            {
                res["status"] = 0;
                for (int i = 1; i <= System.Enum.GetValues(typeof(SilenceCategoryVersion)).Length; i++)
                {
                    var sdata = new JsonObject();
                    sdata["serialNum"] = i;
                    sdata["featureName"] = System.Enum.GetName(typeof(SilenceCategoryVersion), (SilenceCategoryVersion)i);
                    sdata["type"] = type;
                    sdata["closePrompt"] = "系统升级改造中";
                    sdata["state"] = GameApplication.SilenceManager.CheckSilenceSetting((SilenceCategory)i) ? 1 : 0;
                    res_list.Add(sdata);
                }
                res["data"] = res_list;

                return new LeitingCommonReply()
                {
                    Success = true,
                    Data = res_list.ToString(),
                };
            }
            else
            {
                if (!System.Enum.TryParse(featureName, out silence_type))
                {
                    res["status"] = 1;
                    res["message"] = "featureName 错误，找不到对应的功能模块";

                    return new LeitingCommonReply()
                    {
                        Success = false,
                        Msg = res.ToString()
                    };
                }

                res["status"] = 0;
                var data = new JsonObject();
                data["serialNum"] = (int)silence_type;
                data["featureName"] = featureName;
                data["type"] = type;
                data["closePrompt"] = "系统升级改造中";
                data["state"] = GameApplication.SilenceManager.CheckSilenceSetting((SilenceCategory)((int)silence_type)) ? 1 : 0;
                res_list.Add(data);
                res["data"] = res_list;
            }


            return new LeitingCommonReply()
            {
                Success = true,
                //Msg = res.ToString()
                Data = res_list.ToString(),
            };
        }

        [OperationContract]
        public LeitingCommonReply UpdatefunctionModule(int serialNum, string closePrompt, int state)
        {
            var res = new JsonObject();

            if (!System.Enum.IsDefined(typeof(SilenceCategory), serialNum))
            {
                res["status"] = 1;
                res["message"] = "serialNum 错误，找不到对应的功能模块";

                return new LeitingCommonReply()
                {
                    Success = false,
                    Msg = res.ToString()
                };
            }

            SilenceCategory silence_type = (SilenceCategory)serialNum;

            GameApplication.SilenceManager.SetSilence(silence_type, state == 1);

            if (GameApplication.SilenceManager.CheckSilenceSetting(SilenceCategory.Search))
            {
                GameApplication.UserManager.BroadCast(ClientMethod.BanSearch, null);
            }

            res["status"] = 0;
            res["message"] = "操作成功";

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = res.ToString()
            };
        }
        enum SilenceCategoryVersion
        {
            禁止昵称修改 = 1,
            禁止自定义文本 = 2,
            禁止搜索文本 = 3,
            禁止聊天 = 4,
            禁止赠送 = 5,
            禁止拍卖 = 6,
            禁止家族信息修改 = 7,
            禁止交易 = 8,
            禁止发红包 = 9,
        }

        #endregion

        [OperationContract]
        public LeitingCommonReply BanTrading(string rid, int time, string mailTitle, string mailContent)
        {
            var res = new LeitingCommonReply();
            var ch = GetCharacterByRoleId(rid);
            if (ch == null)
            {
                res.Success = false;
                res.Msg = "未找到此玩家";
                return res;
            }

            if (time <= 0 && time != -1)
            {
                res.Success = false;
                res.Msg = "时间输入错误";
                return res;
            }

            if (time == -1)
            {
                ch.BannedTradingTime = DateTimeExtension.Now.AddYears(99);
            }
            else
            {
                ch.BannedTradingTime = DateTimeExtension.Now.AddMinutes(time);
            }
            ch.Save();

            ch.AddMail(mailTitle, mailContent, null, true, DateTimeExtension.Now.AddDays(7), MailType.Common, "禁止交易");

            res.Success = true;
            res.Msg = "禁止交易成功";

            return res;
        }


        [OperationContract(Method = WebRequestMethods.Http.Get)]
        public LeitingCommonReply ServiceStatusCheck()
        {
            try
            {
                var dbIndex = 0;
                var res_json = new JsonObject();
                var schema = EntitySchemaManager.GetSchema<ValueObjectRecord>();
                if (DbConnectionManager.Gain(schema.DbGroup, true) is RedisDbManager manager)
                {
                    res_json["DBLinkStatus"] = manager.IsConnectComplete(out dbIndex) ? "Linked to Db:" + manager.Config.RedisServer : "None Link";
                }
                else
                {
                    res_json["DBLinkStatus"] = "Not A Redis Server";
                }

                var scene_servers_status = new JsonArray();
                if (GameApplication.ServiceManager == null)
                {
                    var notReadyResult = new LeitingCommonReply();
                    notReadyResult.Data = "Service Not Ready";
                    return notReadyResult;
                }
                var scene_servers = GameApplication.ServiceManager.SceneServiceUsers.Values.ToList();
                if (scene_servers == null)
                {
                    res_json["SceneServerConnetionStatus"] = "None";
                }
                else
                {
                    res_json["SceneServerConnetionStatus"] = "Linked";
                    scene_servers.ForEach(s =>
                    {
                        var server_status_json = new JsonObject();
                        server_status_json[nameof(s.ID)] = s.ID;
                        server_status_json[nameof(s.Name)] = s.Name;
                        server_status_json[nameof(s.ClientIP)] = s.ClientIP;
                        server_status_json[nameof(s.Connection.IsConnected)] = s.Connection.IsConnected;
                        server_status_json[nameof(s.Connection.ConnectTime)] = s.Connection.ConnectTime.ToString();
                        server_status_json[nameof(s.Connection.RemoteEndpointString)] = s.Connection.RemoteEndpointString;
                        scene_servers_status.Add(server_status_json);
                    });
                }
                res_json["SceneServerStatus"] = scene_servers_status;
                var res = new LeitingCommonReply();

                res.Success = true;
                res.Data = res_json.ToString();
                return res;
            }
            catch (Exception e)
            {

                var res = new LeitingCommonReply();

                res.Success = false;
                res.Data = e.Message + e.StackTrace;
                return res;
            }
        }

        [OperationContract]
        public LeitingCommonReply ReloadActivity(string key)
        {
            var rsp = new LeitingCommonReply();
            var filename = $"{key}{ActivityManager.MetadataFileExtension}";
            var path = Path.Combine(GameApplication.ActivityManager.MetadataDirectoryPath, filename);
            if (!File.Exists(path))
            {
                rsp.Msg = "file not found";
                return rsp;
            }

            var text = File.ReadAllText(path, Encoding.UTF8);
            var json = (JsonObject)JsonValue.Parse(text);
            json.Remove(nameof(ActivityFunction.StartDate));
            json.Remove(nameof(ActivityFunction.EndDate));
            var res = GameApplication.ActivityManager.SetActivity(json);
            if (res.Status == 1 && res.Activity.Function.AllwaysOpen)
            {
                //这里就不用判断id了 直接把其他的非唯一活动都关掉
                if (res.Activity.Function.UniquelyExist)
                {
                    var acts = GameApplication.ActivityManager.FindActivity(c => c.ClassName == json["ClassName"]);
                    if (acts != null)
                    {
                        acts.ForEach(act =>
                        {
                            act.StartDate = new DateTime(2000, 1, 1);
                            act.EndDate = new DateTime(2000, 1, 1);
                        });
                    }
                }
                res.Activity.Function.StartDate = new DateTime(2000, 1, 1);
                res.Activity.Function.EndDate = new DateTime(2100, 1, 1);
            }


            rsp.Success = true;
            rsp.Msg = "success";
            return rsp;
        }

        [OperationContract]
        public LeitingCommonReply ReloadAllActivities()
        {
            var count = 0;
            var baseDirectory = new DirectoryInfo(GameApplication.ActivityManager.MetadataDirectoryPath);
            var allways_open_dic = new Dictionary<string, ActivityFunction>();
            if (baseDirectory.Exists)
            {
                foreach (var file in baseDirectory.GetFiles($"*{ActivityManager.MetadataFileExtension}"))
                {
                    count++;

                    try
                    {
                        var text = File.ReadAllText(file.FullName, Encoding.UTF8);
                        var json = (JsonObject)JsonValue.Parse(text);
                        json.Remove(nameof(ActivityFunction.StartDate));
                        json.Remove(nameof(ActivityFunction.EndDate));
                        var res = GameApplication.ActivityManager.SetActivity(json);

                        if (res.Status == 1 && res.Activity.Function.AllwaysOpen)
                        {
                            if (res.Activity.Function.UniquelyExist && (!allways_open_dic.ContainsKey(json["ClassName"]) || allways_open_dic[json["ClassName"]].ID < json["ID"]))
                            {
                                allways_open_dic[json["ClassName"]] = res.Activity.Function;
                            }
                            else
                            {
                                res.Activity.Function.StartDate = new DateTime(2000, 1, 1);
                                res.Activity.Function.EndDate = new DateTime(2100, 1, 1);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException($"reload activity file:{file.Name} error.", ex);
                    }
                }
            }

            var rsp = new LeitingCommonReply();
            rsp.Success = true;
            rsp.Msg = "success";
            rsp.Data = count.ToString();

            allways_open_dic.Values.ForEach(act =>
            {
                act.StartDate = new DateTime(2000, 1, 1);
                act.EndDate = new DateTime(2100, 1, 1);
            });
            return rsp;
        }

        [OperationContract]
        public LeitingCommonReply GetActivityVersion(long key)
        {
            var rsp = new LeitingCommonReply();
            var activity = GameApplication.ActivityManager.GetActivity(key);
            if (activity == null)
            {
                rsp.Msg = "activity not found";
                return rsp;
            }

            rsp.Success = true;
            rsp.Msg = "success";
            rsp.Data = (activity.Function?.Version ?? 0).ToString();
            return rsp;
        }

        private static JsonValue ActivityConvertTo(ActivityFunction activity)
        {
            var json = new JsonObject();
            json["id"] = activity.Owner.ID;
            json["name"] = activity.Name;
            json["phase"] = (byte)activity.Owner.Phase;
            json["startTimestamp"] = activity.StartDate.ToTimestamp();
            json["endTimestamp"] = activity.EndDate.ToTimestamp();

            //增加描述和载入时间
            json["description"] = JsonSerializer.Serialize(activity);
            json["loadTime"] = activity.Owner.LoadTime.ToTimestamp();
            return json;
        }

        [OperationContract]
        public LeitingCommonReply GetActivities()
        {
            JsonArray? array = null;
            array = new JsonArray(GameApplication.ActivityManager.FindActivity(p => p != null).Select(ActivityConvertTo));
            var rsp = new LeitingCommonReply();
            rsp.Success = true;
            rsp.Msg = "success";
            rsp.Data = array.ToString();
            return rsp;
        }

        [OperationContract]
        public LeitingCommonReply SearchTargetSceneObjectLocation(string rid, string sceneObjectTypeId)
        {
            if (!long.TryParse(sceneObjectTypeId, out var typeId))
            {
                return new LeitingCommonReply() { Success = false, Data = "sceneObjectTypeId 必须为整数。传入值: " + sceneObjectTypeId };
            }
            var character = GetCharacterByRoleId(rid);
            if (character == null)
            {
                return new LeitingCommonReply() { Success = false, Data = "角色不存在: " + rid };
            }
            (var success, var message) = character.FindMySceneObject(typeId);

            var res = new LeitingCommonReply();
            res.Success = success;
            res.Data = message;
            return res;
        }

        [OperationContract]
        public LeitingCommonReply SetActivityTime(long key, long startTimestamp, long endTimestamp)
        {
            var rsp = new LeitingCommonReply();
            var startTime = DateTimeExtension.ConvertFromTimestamp(startTimestamp);
            var endTime = DateTimeExtension.ConvertFromTimestamp(endTimestamp);
            if (startTime > endTime)
            {
                rsp.Msg = "invalid param";
                return rsp;
            }
            var activity = GameApplication.ActivityManager.GetActivity(key);
            if (activity == null)
            {
                rsp.Msg = "activity not found";
                return rsp;
            }
            if (activity.Function is null)
            {
                rsp.Msg = "invalid activity";
                return rsp;
            }

            activity.Function.StartDate = startTime;
            activity.Function.EndDate = endTime;
            activity.Save();

            var data = ActivityConvertTo(activity.Function);
            GameApplication.ActivityManager.ActivityPublishBytesRefreshed = false;

            rsp.Success = true;
            rsp.Msg = "success";
            rsp.Data = data.ToString();
            return rsp;
        }

        public class SetActivitiesTimeModel
        {
            public string id { get; set; }

            public long startTimestamp { get; set; }

            public long endTimestamp { get; set; }
        }

        [OperationContract]
        public LeitingCommonReply GetAwardTest(string systemawAwardId)//根据id获取奖励并输出到LeitingCommonReply 
        {
            var rsp = new LeitingCommonReply();
            if (!long.TryParse(systemawAwardId, out var systemawarId))
            {
                rsp.Success = false;
                rsp.Msg = "输入id格式错误。";
                return rsp;
            }
            var award_config = GameApplication.DataManager.FindValue<SystemAwardType>(systemawarId);//获取奖励信息

            if (award_config == null)
            {
                rsp.Success = false;
                rsp.Msg = "未找到对应的奖励配置。";
                return rsp;
            }

            rsp.Success = true;
            rsp.Msg = "找到奖励"+systemawarId+"的配置。";
            var r_data = new JsonArray();
            foreach (var group_item in award_config.Awards.OfType<AwardGroup>())
            {
                var resJson = new JsonObject();
                var awardsjson = new JsonArray();
                resJson["ClassName"] = group_item.ClassName;
                resJson["Probability"] = group_item.Probability;
                foreach (var item in group_item.Awards.OfType<AwardBase>())
                {
                    awardsjson.Add(item.ShowGrpcJson);
                }
                resJson["Awards"] = awardsjson;
                r_data.Add(resJson);
            }
            rsp.Data = r_data.ToString();
            return rsp;
        }
      

        /// <summary>
        /// 这边activityInfos是一个json体
        /// 格式类似于：
        /// [
        ///     {
        ///         "id":"11111111",
        ///         "startTimestamp":1123123123,
        ///         "endTimestamp":123123123
        ///     },
        ///     {
        ///         "id":"2222222",
        ///         "startTimestamp":1123123123,
        ///         "endTimestamp":123123123
        ///     },
        /// ]
        /// </summary>
        /// <param name="activities"></param>
        /// <returns></returns>
        [OperationContract]
        public LeitingCommonReply SetActivitiesTime(string activityInfos)
        {
            bool doSuccess = true;
            StringBuilder err = new StringBuilder();

            Logger.GrpcAccepted.Write("SetActivitiesTime accepted, data info : " + activityInfos);

            var infos = Newtonsoft.Json.JsonConvert.DeserializeObject<List<SetActivitiesTimeModel>>(activityInfos);
            if (infos != null && infos.Count > 0)
            {
                foreach (var info in infos)
                {
                    try
                    {
                        long id = 0;
                        long.TryParse(info.id, out id);
                        var res = SetActivityTime(id, info.startTimestamp, info.endTimestamp);

                        if (!res.Success)
                        {
                            doSuccess = false;
                            string er = $"SetActivitiesTime failed,info:{Newtonsoft.Json.JsonConvert.SerializeObject(info)}, exception:{res.Msg}";
                            err.Append(er);
                        }
                    }
                    catch (Exception ex)
                    {
                        doSuccess = false;

                        string errmsg = $"SetActivitiesTime error,info:{Newtonsoft.Json.JsonConvert.SerializeObject(info)}, exception:{ex.ToString()}";
                        Logger.Error.Write(errmsg);
                        err.Append(errmsg);
                    }
                }
            }

            return new LeitingCommonReply()
            {
                Success = doSuccess,
                Msg = err.ToString()
            };
        }

        [OperationContract]
        public LeitingCommonReply DeleteText(string rid, string textType, string textId)
        {
            var character = GetCharacterByRoleId(rid);

            if (character == null)
            {
                return new LeitingCommonReply() { Success = false, Msg = "未找到目标玩家" };
            }

            if (!Enum.TryParse(typeof(ExtendType), textType, out var type) || type == null)
            {
                return new LeitingCommonReply() { Success = false, Msg = "未找到目标textType" };
            }

            var text_type = (ExtendType)type;
            long object_id = 0;
            if (text_type != ExtendType.留言条 && text_type != ExtendType.家族申请)
            {
                if (!long.TryParse(textId, out object_id))
                {
                    return new LeitingCommonReply() { Success = false, Msg = "textid格式错误" };
                }
            }

            try
            {
                switch (text_type)
                {
                    case ExtendType.庭舍昵称:
                    case ExtendType.房间昵称:
                        var scene = GameApplication.SceneManager.GetScene(object_id, true);
                        scene.Name = scene.Dimension.Type == DimensionType.PicaGarden ? scene.OwnerName + "的庭舍" : scene.Dimension.Type == DimensionType.PicaRoom ? scene.OwnerName + "的房间" : scene.OwnerName + "的驻地";
                        scene.Spec.Name = scene.Name;
                        scene.Save();
                        scene.UpdateToSceneService(p => p.Name = scene.Name);
                        scene.Save();
                        break;
                    case ExtendType.宠物昵称:
                        var pet = character.GetPet(object_id);
                        pet.NameChanged = false;
                        pet.ChangeName("" + pet.ID % 1000 + DateTimeExtension.NowTimeStamp % 1000, true);
                        break;
                    case ExtendType.传单标题:
                        var leaflet = character.Leaflets.FirstOrDefault(l => l.ID == object_id);
                        if (leaflet == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标传单或目标传单已被删除"
                            };
                        }
                        leaflet.Title = "";
                        leaflet.Save();
                        break;
                    case ExtendType.传单文本:
                        var leaflet1 = character.Leaflets.FirstOrDefault(l => l.ID == object_id);
                        if (leaflet1 == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标传单或目标传单已被删除"
                            };
                        }
                        leaflet1.Content = "";
                        leaflet1.Save();
                        break;
                    case ExtendType.一卡通挂售文本:
                    case ExtendType.一卡通挂售描述:
                        var vender_machine = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(object_id);
                        if (vender_machine == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标收银台或目标收银台已被删除"
                            };
                        }
                        var func = vender_machine.GetFunction<PicaVendingMachine>();
                        if (func == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "找不到目标收银台"
                            };
                        }

                        var record = func.GetRecord(vender_machine);

                        record.Commoditys.ForEach(c =>
                        {
                            c.Name = "我的商品";
                            c.Describe = "商品描述";
                        });
                        func.UpdateRecordToSceneService(character, vender_machine, record);
                        vender_machine.Save();
                        break;
                    case ExtendType.房间公告:
                        var notice_scene = GameApplication.DataManager.FindValue<Scene>(object_id);
                        if (notice_scene.Dimension.Type != DimensionType.PicaRoom)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "房间id错误"
                            };
                        }
                        var notice_ecord = (notice_scene as PicaRoomScene)?.GetFunctionRecord<PicaRoomExtraDatasFunctionRecord>(true);
                        if (notice_ecord == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "房间id错误"
                            };
                        }
                        notice_ecord.NoticeContent = "";
                        notice_ecord.UpdateTo();
                        notice_scene.Save();

                        break;
                    case ExtendType.留言条:
                        //如果是从举报走进来的id是直接找物件id 否则发送的是有:的后端存储id
                        SceneObject? note;
                        if (textId.Contains(":"))
                        {
                            var org_ids = textId.Split(":");
                            var note_id = long.Parse(org_ids[1]);
                            var area_id = long.Parse(org_ids[0]);
                            var note_area = GameApplication.DataManager.FindValue<Area>(area_id);
                            note = note_area.Objects.Values.FirstOrDefault(o =>
                            {
                                var func = o.GetFunction<PicaNoteFunction>();
                                if (func == null)
                                {
                                    return false;
                                }

                                if (func.GetRecord(o).NoteBroadId == note_id)
                                {
                                    return true;
                                }
                                return false;
                            });
                        }
                        else
                        {
                            if (!long.TryParse(textId, out object_id))
                            {
                                return new LeitingCommonReply() { Success = false, Msg = "textid格式错误" };
                            }
                            note = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(object_id);
                        }

                        if (note == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标留言条或目标留言条已被删除"
                            };
                        }
                        var note_func = note.GetFunction<PicaNoteFunction>();
                        if (note_func == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "找不到目标留言条"
                            };
                        }

                        var note_record = note_func.GetRecord(note);

                        note_record.Title = "";
                        note_record.Context = "";
                        //note_func.OnOwnerPlace(note);
                        note_record.UpdateTo(character, note);
                        note.Save();
                        break;
                    case ExtendType.情书称呼:
                    case ExtendType.情书内容:
                        var love_letter_item = GameApplication.DataManager.FindValue<EntityBase, Item>(object_id);
                        if (love_letter_item == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "找不到目标情书"
                            };
                        }
                        var love_letter_func_record = love_letter_item.GetFunctionRecord<RomanticLetterItemFunctionRecord>(false);
                        if (love_letter_func_record == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "目标情书id传入错误"
                            };
                        }

                        love_letter_func_record.Salutation = "[内容已删除]";
                        love_letter_func_record.Content = "[本信件内容由于包含不适宜内容已被清理]";
                        love_letter_item.UpdateToClient();
                        love_letter_item.Save();
                        break;
                    case ExtendType.请柬内容:
                        var invite_letter_item = GameApplication.DataManager.FindValue<EntityBase, Item>(object_id);
                        if (invite_letter_item == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "找不到目标请柬"
                            };
                        }
                        var invite_letter_func_record = invite_letter_item.GetFunctionRecord<InvitationCardItemFunctionRecord>(false);
                        if (invite_letter_func_record == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "目标请柬id传入错误"
                            };
                        }

                        invite_letter_func_record.Content = "[本信件内容由于包含不适宜内容已被清理]";
                        invite_letter_item.UpdateToClient();
                        invite_letter_item.Save();
                        break;
                    case ExtendType.合照:
                        var photo_so = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(object_id);
                        if (photo_so == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "找不到目标合照"
                            };
                        }
                        var photo_so_func_record = photo_so.GetFunctionRecord<GroupPhotoFunctionRecord>(false);
                        if (photo_so_func_record == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "目标合照id传入错误"
                            };
                        }
                        photo_so_func_record.GroupPhotoCharacters.Clear();
                        photo_so_func_record.GroupPhotoStickers.Clear();
                        photo_so.UpdateToClient();
                        photo_so.Save();
                        break;
                    case ExtendType.家族昵称:
                        var org_title = GameApplication.OrganizationManager.TryGetOrganization(object_id);
                        if (org_title == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标家族"
                            };
                        }
                        org_title.Name = "家族" + org_title.ID;
                        org_title.BroadCast(ClientMethod.UpdateOrganizationSpec, org_title.SpecData);

                        break;
                    case ExtendType.家族宣言:
                        var org_dec = GameApplication.OrganizationManager.TryGetOrganization(object_id);
                        if (org_dec == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标家族"
                            };
                        }
                        org_dec.Declaration = "家族" + org_dec.ID;
                        org_dec.BroadCast(ClientMethod.UpdateOrganizationSpec, org_dec.SpecData);
                        break;
                    case ExtendType.家族申请:
                        var ids = textId.Split("x");
                        var org_id = long.Parse(ids[1]);
                        var chara_id = long.Parse(ids[0]);
                        var org = GameApplication.OrganizationManager.TryGetOrganization(org_id);
                        if (org == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标家族"
                            };
                        }
                        var character_org_apply = GameApplication.DataManager.FindValue<Character>(chara_id);
                        if (character_org_apply == null)
                        {
                            return new LeitingCommonReply()
                            {
                                Success = false,
                                Msg = "未找到目标玩家"
                            };
                        }

                        org.Requests.Remove(org.Requests.First(o => o.Character.ID == character_org_apply.ID));

                        org.BroadCast(ClientMethod.UpdateOrganizationRequestData, org.GetPersonal(character_org_apply));
                        break;
                    case ExtendType.个性签名:
                        character.Signature = "";
                        character.UpdateToClient(c => c.Signature = character.Signature);
                        character.Save();
                        break;
                    default:
                        return new LeitingCommonReply()
                        {
                            Success = false,
                            Msg = "未知ExtendType"
                        };
                }
            }
            catch (Exception e)
            {
                Logger.Error.Write("DeleteText 发生错误 " + e.Message + e.StackTrace);
                return new LeitingCommonReply()
                {
                    Success = false,
                    Msg = "发生错误 " + e.Message + e.StackTrace
                };
            }

            return new LeitingCommonReply()
            {
                Success = true,
                Msg = "清除成功"
            };
        }

        [OperationContract]
        public LeitingCommonReply UnbanTrade(string rid)
        {
            return new LeitingCommonReply { Success = true, Msg = "解封成功" };
        }

        [OperationContract(SkipClientImpl = true)]
        public LeitingCommonReply ReceiveWebAward(string rid, long id)
        {
            return new LeitingCommonReply { Success = true, Msg = "ReceiveWebAward成功" };
        }

        [OperationContract]
        public LeitingCommonReply RefundByGM(string rid, int crownDays = 0, int payMoney = 0, int totalPayProgress = 0)
        {
            string msg = "";
            bool success = false;

            try
            {
                var character = GetCharacterByRoleId(rid);
                if (character == null)
                {
                    success = false;
                    msg = $"找不到对应玩家，rid : {rid}";
                    goto RefundByGMReply;
                }

                var operationDetails = new List<string>();

                // 1. 扣除金冠天数
                if (crownDays > 0)
                {
                    var originalCrownLeftDay = character.CrownLeftDay;
                    var originalCrownLevel = character.CrownLevel;

                    if (originalCrownLeftDay < crownDays)
                    {
                        msg = $"玩家当前金冠剩余天数({originalCrownLeftDay})不足以扣除{crownDays}天";
                        goto RefundByGMReply;
                    }

                    // 从最高等级开始扣除天数
                    var remainingDaysToDeduct = crownDays;
                    var keyList = character.CrownLevelLastData.Keys.ToList().OrderByDescending(a => a);

                    foreach (var level in keyList)
                    {
                        if (remainingDaysToDeduct <= 0) break;

                        var currentDays = character.CrownLevelLastData[level];
                        if (currentDays >= remainingDaysToDeduct)
                        {
                            character.CrownLevelLastData[level] -= remainingDaysToDeduct;
                            if (character.CrownLevelLastData[level] == 0)
                            {
                                character.CrownLevelLastData.Remove(level);
                                character.OnCrownRemoved(level);
                            }
                            remainingDaysToDeduct = 0;
                        }
                        else
                        {
                            remainingDaysToDeduct -= currentDays;
                            character.CrownLevelLastData.Remove(level);
                            character.OnCrownRemoved(level);
                        }
                    }

                    character.RefreshCrownLevelAndCrownLeftDay();
                    operationDetails.Add($"扣除金冠天数: {crownDays}天 (原剩余: {originalCrownLeftDay}天, 现剩余: {character.CrownLeftDay}天)");
                }

                // 2. 扣除累充金额
                if (payMoney > 0)
                {
                    var originalPayMoney = character.PayMoney;
                    if (originalPayMoney < payMoney)
                    {
                        msg = $"玩家当前累充金额({originalPayMoney / 100}元)不足以扣除{payMoney / 100}元";
                        goto RefundByGMReply;
                    }

                    character.PayMoney -= payMoney;
                    character.Save();
                    character.UpdateToClient(c => c.TotalChargeScore = character.PayMoney / 10);
                    operationDetails.Add($"扣除累充金额: {payMoney / 100}元 (原累充: {originalPayMoney / 100}元, 现累充: {character.PayMoney / 100}元)");
                }

                // 3. 扣除累充活动进度
                if (totalPayProgress > 0)
                {
                    var act = GameApplication.ActivityManager.FirstActivity<TotalPayActivity>();
                    if (act != null)
                    {
                        var ac_record = act.GetCharacterRecord(character, true, true);
                        var originalTotalValue = ac_record.TotalValue;

                        if (originalTotalValue < totalPayProgress)
                        {
                            msg = $"玩家当前累充活动进度({originalTotalValue / 100}元)不足以扣除{totalPayProgress / 100}元";
                            goto RefundByGMReply;
                        }

                        ac_record.TotalValue -= totalPayProgress;
                        ac_record.Save();
                        act.UpdateToClient(character);
                        operationDetails.Add($"扣除累充活动进度: {totalPayProgress / 100}元 (原进度: {originalTotalValue / 100}元, 现进度: {ac_record.TotalValue / 100}元)");
                    }
                    else
                    {
                        operationDetails.Add("累充活动未找到，跳过累充活动进度扣除");
                    }
                    
                    var actV2 = GameApplication.ActivityManager.FirstActivity<TotalPayV2Activity>();
                    if (actV2 != null)
                    {
                        var ac_recordV2 = actV2.GetCharacterRecord(character, true, true);
                        var currentStep = actV2.NowAtStep;

                        if (currentStep > 0)
                        {
                            var stepRecord = ac_recordV2.GetRecordByStep(currentStep, actV2);
                            var originalChargeValue = stepRecord.ChargeValue;

                            if (originalChargeValue < totalPayProgress)
                            {
                                msg = $"玩家当前累充活动V2进度({originalChargeValue / 100}元)不足以扣除{totalPayProgress / 100}元";
                                goto RefundByGMReply;
                            }

                            stepRecord.ChargeValue -= totalPayProgress;
                            ac_recordV2.Save();
                            actV2.UpdateToClient(character);
                            operationDetails.Add($"扣除累充活动V2进度(第{currentStep}期): {totalPayProgress / 100}元 (原进度: {originalChargeValue / 100}元, 现进度: {stepRecord.ChargeValue / 100}元)");
                        }
                        else
                        {
                            operationDetails.Add("累充活动V2当前无活跃期数，跳过累充活动V2进度扣除");
                        }
                    }
                    else
                    {
                        operationDetails.Add("累充活动V2未找到，跳过累充活动V2进度扣除");
                    }
                }

                // 保存数据
                character.Save();

                // 更新客户端数据
                character.UpdateToClient(c => {
                    c.FirstChargePanelData = character.GetFirstChargePanelData();
                    c.FirstChargeLevel = character.GetRookieChargePackLevel;
                    c.TotalChargeScore = character.PayMoney / 10;
                });

                // TA日志记录
                if (Constants.AllowTaLog)
                {
                    LeiTingEventWriter.Track("system", "system", GameApplication.Service.Config.IP, "gm")
                        .WriteProperties()
                            .WriteEntry("action", "GM退款操作")
                            .WriteObject("extend_info")
                                .WriteRawEntry("rid", rid)
                                .WriteEntry("crownDays", crownDays)
                                .WriteEntry("payMoney", payMoney)
                                .WriteEntry("totalPayProgress", totalPayProgress)
                                .WriteStringEntry("player_id", character.ID)
                                .WriteEntry("player_name", character.Name)
                                .WriteRawEntry("operation_details", string.Join("; ", operationDetails))
                            .WriteToTaLogger();
                }

                success = true;
                msg = $"GM退款操作成功。操作详情: {string.Join("; ", operationDetails)}";

            RefundByGMReply:
                return new LeitingCommonReply
                {
                    Success = success,
                    Msg = msg
                };
            }
            catch (Exception e)
            {
                return new LeitingCommonReply
                {
                    Success = false,
                    Msg = $"GM退款操作异常: {e.ToString()}"
                };
            }
        }


        [OperationContract]
        public LeitingCommonReply GetActivitiesWithPagination(int pageIndex, int pageSize, string searchValue)
        {
            var rsp = new LeitingCommonReply();

            var search_func = new Func<ActivityFunction, bool>((act) =>
            {
                if (string.IsNullOrEmpty(searchValue))
                {
                    return true;
                }
                if (long.TryParse(searchValue, out var act_id))
                {
                    return act.ID == act_id;
                }
                else
                {
                    return act.Name.Contains(searchValue);
                }
            });

            var acitvitys = GameApplication.ActivityManager.FindActivity(p => p != null)
                .Where(p => search_func(p))
                .OrderByDescending(p => p.Owner.Phase == ActivityPhase.开启)
                .ThenByDescending(p => p.ID)
                .ToList();
            var pageArray = acitvitys.Skip(pageIndex * pageSize).Take(pageSize).Select(ActivityConvertTo);

            if (!pageArray.Any())
            {
                rsp.Success = true;
                rsp.Msg = "success";
                rsp.Data = new JsonObject
                {
                    ["payload"] = new JsonArray(),
                    ["page"] = pageIndex,
                    ["total"] = 0
                }.ToString();
            }
            else
            {
                rsp.Success = true;
                rsp.Msg = "success";
                rsp.Data = new JsonObject
                {
                    ["payload"] = JsonSerializer.Serialize(pageArray),
                    ["page"] = pageIndex,
                    ["total"] = /*(acitvitys.Count + pageSize - 1) / pageSize*/acitvitys.Count
                }.ToString();
            }

            return rsp;
        }

        [OperationContract]
        public LeitingCommonReply FindActivityByIdOrName(string searchValue)
        {
            JsonArray? array = null;
            if (long.TryParse(searchValue, out var act_id))
            {
                //根据id搜索
                array = new JsonArray(GameApplication.ActivityManager.FindActivity(p => p.ID == act_id).ToList().Select(ActivityConvertTo));
            }
            else
            {
                //根据名字搜索
                array = new JsonArray(GameApplication.ActivityManager.FindActivity(p => p.Name.Contains(searchValue)).ToList().Select(ActivityConvertTo));
            }
            var rsp = new LeitingCommonReply();
            rsp.Success = true;
            rsp.Msg = "success";
            rsp.Data = array.ToString();
            return rsp;
        }


        [OperationContract]
        public LeitingCommonReply LiftAttach(string characterId)
        {
            return new LeitingCommonReply { Success = true, Msg = "LiftAttach成功" };
        }

        [OperationContract]
        public LeitingCommonReply SetSuit(string characterId, string suitId, int minute)
        {
            return new LeitingCommonReply { Success = true, Msg = "LiftAttach成功" };
        }


        [OperationContract]
        public LeitingCommonReply DeleteAccount(string rid)
        {
            return new LeitingCommonReply { Success = true, Msg = "DeleteAccount成功" };
        }
    }

    internal class CommonReply
    {
        public bool Status { get; set; }

        public string Data { get; set; }

        public string Message { get; set; }

        public CommonReply(bool status, string message = "", string data = "")
        {
            Status = status;
            Data = data;
            Message = message;
        }

        public LeitingCommonReply ToLeitingCommonReplay()
        {
            return new LeitingCommonReply() { Success = Status, Data = Data, Msg = Message };
        }

        public override string ToString()
        {
            var jsonObject = new JsonObject
            {
                { "status", Status ? 1 : 0 }
            };

            jsonObject["data"] = Data;
            jsonObject["message"] = Message;

            return jsonObject.ToString();
        }
    }
}