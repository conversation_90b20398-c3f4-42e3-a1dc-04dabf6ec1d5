using System;
using System.Collections.Generic;
using System.Net.Sockets;

namespace ConnectionBase
{
    public class SocketArgsPool
    {
        private readonly Queue<SocketAsyncEventArgs> asyncEventPool;
        private readonly int capacity;

        public SocketArgsPool(int capacity)
        {
            if (capacity <= 0)
                throw new ArgumentOutOfRangeException(nameof(capacity));

            this.capacity = capacity;
            this.asyncEventPool = new Queue<SocketAsyncEventArgs>(capacity);
        }

        public int Capacity => capacity;

        public int Available => asyncEventPool.Count;

        public void CheckIn(SocketAsyncEventArgs asyncEvent)
        {
            if (asyncEvent == null)
                throw new ArgumentNullException(nameof(asyncEvent));

            lock (asyncEventPool)
            {
                asyncEventPool.Enqueue(asyncEvent);
            }
        }

        public bool TryDequeue(out SocketAsyncEventArgs asyncEvent)
        {
            lock (asyncEventPool)
            {
                if (asyncEventPool.Count > 0)
                {
                    asyncEvent = asyncEventPool.Dequeue();
                    return true;
                }

                asyncEvent = null;
                return false;
            }
        }
    }
}