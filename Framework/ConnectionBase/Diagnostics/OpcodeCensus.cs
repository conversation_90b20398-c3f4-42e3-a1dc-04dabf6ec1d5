using System.Collections.Concurrent;

namespace ConnectionBase.Diagnostics
{
    public class OpcodeCensus
    {
        private readonly ConcurrentDictionary<short, OpcodeInfo> opcodes = new ConcurrentDictionary<short, OpcodeInfo>();

        public IReadOnlyDictionary<short, OpcodeInfo> Opcodes => opcodes;

        public OpcodeAggregation Total => OpcodeAggregation.Merge(opcodes.Select(p => p.Value));

        public OpcodeInfo Gain(short opcode)
        {
            return opcodes.GetOrAdd(opcode, key => new OpcodeInfo(key));
        }
    }
}