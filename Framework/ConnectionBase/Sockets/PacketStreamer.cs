using System.Collections.Generic;

namespace ConnectionBase.Sockets
{
    public class PacketStreamer
    {
        private Queue<byte[]> dataQueue = new Queue<byte[]>();

        public int Count => dataQueue.Count;

        public void Enqueue(byte[] packet)
        {
            dataQueue.Enqueue(packet);
        }

        public byte[] Dequeue()
        {
            return dataQueue.Dequeue();
        }

        public bool TryDequeue(out byte[] packet)
        {
            if (dataQueue.Count == 0)
            {
                packet = null;
                return false;
            }

            packet = dataQueue.Dequeue();
            return true;
        }

        public void Clear()
        {
            dataQueue.Clear();
        }
    }
}
