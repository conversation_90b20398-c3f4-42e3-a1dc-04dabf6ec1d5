namespace DataBase.SqlExpressions
{
    public class SqlParameterExpression : SqlExpression
    {
        private string _name;

        internal SqlParameterExpression(string name)
        {
            _name = name;
        }

        public sealed override SqlExpressionType NodeType => SqlExpressionType.Parameter;
        public string Name => _name;

        protected internal override void Accept(SqlExpressionVisitor visitor)
        {
            visitor.Parameter(this);
        }
    }
}
