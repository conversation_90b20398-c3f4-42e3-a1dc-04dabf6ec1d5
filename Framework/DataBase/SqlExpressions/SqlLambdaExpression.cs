using System;

namespace DataBase.SqlExpressions
{
    class SqlLambdaExpression : SqlExpression
    {
        internal SqlLambdaExpression(string name)
        {
            Name = name;
        }

        public string Name { get; }

        public override SqlExpressionType NodeType => SqlExpressionType.Lambda;

        protected internal override void Accept(SqlExpressionVisitor visitor)
        {
            throw new NotImplementedException();
        }
    }
}
