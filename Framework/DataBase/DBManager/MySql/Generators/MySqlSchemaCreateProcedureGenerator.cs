using System;
using System.Linq;
using DataBase.Entity;
using DataBase.Entity.Generators;
using HelpBase.Reflection;

namespace DataBase.DBManager.MySql.Generators
{
    internal sealed class MySqlSchemaCreateProcedureGenerator : SchemaCreateProcedureGenerator
    {
        public MySqlSchemaCreateProcedureGenerator(SchemaEntity entity, ProcedureUsage usage)
            : base(entity, usage)
        { }

        public override void Generate(SourceWriter writer)
        {
            ArgumentNullException.ThrowIfNull(writer, nameof(writer));

            switch (Usage)
            {
                case ProcedureUsage.TVPInsert:
                case ProcedureUsage.TVPUpdate:
                    GenerateTvpProcedureCommandText(writer, Entity);
                    return;
                case ProcedureUsage.InsertAndUpdate:
                case ProcedureUsage.Insert:
                case ProcedureUsage.Update:
                    GenerateMvpProcedureCommandText(writer, Entity);
                    break;
                default:
                    throw new InvalidOperationException($"unknown schema:{Entity.Name} usage:{Usage}");
            }

            void GenerateTvpProcedureCommandText(SourceWriter writer, SchemaEntity entity)
            {
                writer.Write("Create Procedure `").Write(Name).WriteLine("` (");
                writer.Embed(writer =>
                {
                    switch (entity)
                    {
                        case SchemaTable table:
                            GenerateParameter(writer, table);
                            break;
                        case SchemaView view:
                            GenerateParameter(writer, view.Tables[0]);
                            for (var i = 1; i < view.Tables.Count; i++)
                            {
                                writer.WriteLine(',');
                                GenerateParameter(writer, view.Tables[i]);
                            }
                            break;
                    }

                    writer.WriteLine();
                });
                writer.WriteLine(')');
                writer.WriteLine("Begin");
                writer.Embed(writer =>
                {
                    switch (entity)
                    {
                        case SchemaTable table:
                            GenerateTable(writer, table);
                            break;
                        case SchemaView view:
                            GenerateTable(writer, view.Tables[0]);
                            for (var i = 1; i < view.Tables.Count; i++)
                            {
                                writer.WriteLine();
                                GenerateTable(writer, view.Tables[i]);
                            }
                            break;
                    }
                });
                writer.WriteLine("End;");
                void GenerateParameter(SourceWriter writer, SchemaTable table)
                {
                    writer.Write("In ").Write(table.Name).Write("_COLUMNS LONGTEXT,").WriteLine();
                    writer.Write("In ").Write(table.Name).Write("_VALUES LONGTEXT");
                }
                void GenerateTable(SourceWriter writer, SchemaTable table)
                {
                    var name = table.Name;
                    writer.Write("Set @EXEC_").Write(name).Write("_COMMAND_TEXT = Concat(\"Insert Into `").Write(name)
                        .Write("` (\", ").Write(name).Write("_COLUMNS, \") Values \", ").Write(name)
                        .Write("_VALUES, \" On Duplicate Key Update ");
                    using (var e = table.GetMappingColumns().Where(p => !p.IsPrimaryKey).GetEnumerator())
                    {
                        if (e.MoveNext())
                        {
                            writer.Write('`').Write(e.Current.Name).Write("` = Values(`").Write(e.Current.Name).Write("`)");
                            while (e.MoveNext())
                            {
                                writer.Write(", `").Write(e.Current.Name).Write("` = Values(`").Write(e.Current.Name).Write("`)");
                            }
                        }
                    }

                    writer.WriteLine(";\");");
                    writer.Write("Prepare STMT_").Write(name).Write(" From @EXEC_").Write(name).WriteLine("_COMMAND_TEXT;");
                    writer.Write("Execute STMT_").Write(name).WriteLine(';');
                    writer.Write("Deallocate Prepare STMT_").Write(name).WriteLine(';');
                }
            }

            void GenerateMvpProcedureCommandText(SourceWriter writer, SchemaEntity entity)
            {
                var columns = entity.GetMappingColumns();
                writer.Write("Create Procedure `").Write(Name).WriteLine("` (");
                writer.Embed(writer =>
                {
                    GenerateParameter(writer, columns[0]);
                    for (var i = 1; i < columns.Count; i++)
                    {
                        writer.WriteLine(',');
                        GenerateParameter(writer, columns[i]);
                    }

                    writer.WriteLine();
                });
                writer.WriteLine(')');
                writer.WriteLine("Begin");
                writer.Embed(writer =>
                {
                    switch (Usage)
                    {
                        case ProcedureUsage.InsertAndUpdate:
                            writer.Write("If Exists (Select * From `").Write(entity.Name).WriteLine("`) Then");
                            writer.Embed(writer => GenerateUpdateCommandText(writer, entity));
                            writer.WriteLine("Else");
                            writer.Embed(writer => GenerateInsertCommandText(writer, entity));
                            writer.WriteLine("End If;");
                            break;
                        case ProcedureUsage.Insert:
                            GenerateInsertCommandText(writer, entity);
                            break;
                        case ProcedureUsage.Update:
                            GenerateUpdateCommandText(writer, entity);
                            break;
                    }
                });
                writer.WriteLine("End;");
                void GenerateParameter(SourceWriter writer, TableColumnAttribute column)
                {
                    var dbType = MySqlParamHelper.GetDbType(column);
                    writer.Write("In _").Write(column.Name).Write(' ').Write(dbType);
                }

                void GenerateInsertCommandText(SourceWriter writer, SchemaEntity entity)
                {
                    switch (entity)
                    {
                        case SchemaTable table:
                            GenerateTable(writer, table);
                            break;
                        case SchemaView view:
                            GenerateTable(writer, view.Tables[0]);
                            for (var i = 1; i < view.Tables.Count; i++)
                            {
                                writer.WriteLine();
                                GenerateTable(writer, view.Tables[i]);
                            }
                            break;
                    }

                    void GenerateTable(SourceWriter writer, SchemaTable table)
                    {
                        var columns = table.GetMappingColumns();
                        var column = columns[0];
                        writer.Write("Insert Into `").Write(table.Name).Write("` (`").Write(column.Name).Write('`');
                        for (var i = 1; i < columns.Count; i++)
                        {
                            column = columns[i];
                            writer.Write(", `").Write(column.Name).Write('`');
                        }

                        writer.WriteLine(')');
                        column = columns[0];
                        writer.Write("Values (_").Write(column.Name);
                        for (var i = 1; i < columns.Count; i++)
                        {
                            column = columns[i];
                            writer.Write(", _").Write(column.Name);
                        }

                        writer.WriteLine(");");
                    }
                }

                void GenerateUpdateCommandText(SourceWriter writer, SchemaEntity entity)
                {
                    switch (entity)
                    {
                        case SchemaTable table:
                            GenerateTable(writer, table);
                            break;
                        case SchemaView view:
                            GenerateTable(writer, view.Tables[0]);
                            for (var i = 1; i < view.Tables.Count; i++)
                            {
                                writer.WriteLine();
                                GenerateTable(writer, view.Tables[i]);
                            }
                            break;
                    }

                    void GenerateTable(SourceWriter writer, SchemaTable table)
                    {
                        using (var e = table.GetMappingColumns().Where(p => !p.IsPrimaryKey).GetEnumerator())
                        {
                            if (!e.MoveNext())
                            {
                                return;
                            }

                            writer.Write("Update `").Write(table.Name).WriteLine('`');
                            writer.Write("Set `").Write(e.Current.Name).Write("` = _").WriteLine(e.Current.Name);
                            while (e.MoveNext())
                            {
                                writer.Write(", `").Write(e.Current.Name).Write("` = _").WriteLine(e.Current.Name);
                            }

                            var primary = table.MappingPrimary;
                            writer.Write("Where `").Write(primary.Name).Write("` = _").Write(primary.Name).WriteLine(';');
                        }
                    }
                }
            }
        }
    }
}
