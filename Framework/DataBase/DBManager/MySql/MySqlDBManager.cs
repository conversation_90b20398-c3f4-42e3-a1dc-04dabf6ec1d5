using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using DataBase.Data;
using DataBase.Data.Impl.MySql;
using DataBase.DBManager.MySql;
using DataBase.DBManager.MySql.Generators;
using DataBase.Entity;
using DataBase.Entity.Generators;
using DataBase.SqlExpressions;
using HelpBase;
using HelpBase.Reflection;
using MySqlConnector;

namespace DataBase
{
    public class MySqlDBManager : DBManagerBase<MySqlDBConnection>
    {
        public MySqlDBManager(MySqlDbConfiguration config)
            : base(config)
        { }

        public sealed override string PreParam => MySqlParamHelper.PreParamChar;

        public new MySqlDbConfiguration Config => (MySqlDbConfiguration)base.Config;

        public override string ToSqlString<T>(Expression<Func<T, bool>> cond)
        {
            return cond.ToSqlString(MySqlParamHelper.FormatName);
        }

        #region 创建数据库表、存储过程等

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        /// <param name="connectionString"></param>
        protected override void CheckDBExists(string connectionString)
        {
            string newConnectionString = connectionString.Replace(Config.Database, "information_schema");
            using (MySqlConnection connection = new MySqlConnection(newConnectionString))
            {
                connection.Open();
                Dictionary<string, object> input = new Dictionary<string, object>();

                string s = $"CREATE DATABASE IF NOT EXISTS `{Config.Database}` character set utf8mb4 collate utf8mb4_unicode_ci;";
                var sqc = new MySqlCommand(s, connection);

                try
                {
                    sqc.CommandText = s;
                    sqc.ExecuteNonQuery();
                }
                catch (Exception e)
                {
                    Logger.Error.Write(e.Message + e.StackTrace);
                }
            }
        }

        private string GetCreateTableIndexCommand(SchemaIndex index, string tablename)
        {
            var sb = new StringBuilder();
            sb.Append("CREATE ");
            if (index.IsUnique)
            {
                sb.Append("UNIQUE ");
            }
            string name;
            switch (index.Category)
            {
                case IndexCategory.Clustered:
                    name = "ClusteredIndex-";
                    break;
                case IndexCategory.NonClustered:
                    name = "NonClusteredIndex-";
                    break;
                default:
                    throw new InvalidOperationException($"table:{tablename} unknown index category:{index.Category}");
            }
            foreach (var column in index.Columns)
            {
                name += column.Name + '-';
            }
            if (name.Length > 47)
            {
                //对于InnoDB存储引擎，索引名称的最大长度为64个字符
                name = name[46] == '-' ? name.Substring(0, 47) : (name.Substring(0, 46) + '-');
            }

            sb.Append("INDEX `").Append(name);
            sb.Append(DateTime.Now.ToString("yyyyMMdd-HHmmssff")).Append("` ON `").Append(tablename).Append("` ");
            sb.Append('(');
            using (var e = index.Columns.GetEnumerator())
            {
                e.MoveNext();
                sb.Append("    ").Append(MySqlParamHelper.FormatName(e.Current.Name)).Append(" ASC");
                while (e.MoveNext())
                {
                    sb.AppendLine(",");
                    sb.Append("    ").Append(MySqlParamHelper.FormatName(e.Current.Name)).Append(" ASC");
                }
            }

            sb.AppendLine(");");
            // WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
            return sb.ToString();
        }


        public string GenerateCreateTableCommand(SchemaTable table)
        {
            return GetCreateTableCommand(table.Name, table.EntityType.GetTableColumnDictionary(out _)[table.Name], table.Indices);
        }

        /// <summary>
        /// 创建或者更新表
        /// </summary>
        /// <param name="tablename"></param>
        /// <param name="DataPropertyAttributes"></param>
        /// <param name="connectionString"></param>
        protected override void CreateOrRefreshTable(string tablename, List<TableColumnAttribute> DataPropertyAttributes, IReadOnlyList<SchemaIndex> indices, string connectionString)
        {
            CheckDBExists(connectionString);
            string newConnectionString = connectionString.Replace("information_schema", Config.Database);
            using (var connection = new MySqlConnection(newConnectionString))
            {
                connection.Open();
                Dictionary<string, object> input = new Dictionary<string, object>();

                var s = $"select * from `information_schema`.`tables` where `table_schema` = '{Config.Database}' and `table_name` = '{tablename}'";
                var sqc = new MySqlCommand(s, connection);
                try
                {
                    sqc.CommandText = s;
                    sqc.CommandTimeout = Config.CommandTimeout;
                    var reader = sqc.ExecuteReader();
                    if (reader.HasRows)
                    {
                        reader.Close();
                        CheckTableChange(tablename, DataPropertyAttributes, connection);
                        return;
                    }
                    else
                    {
                        reader.Close();
                        sqc.CommandText = GetCreateTableCommand(tablename, DataPropertyAttributes, indices);
                        sqc.ExecuteNonQuery();
                    }
                }
                catch (Exception e)
                {
                    Logger.Fatel.Write("error while creating table " + tablename + " " + s + " " + e.Message + e.StackTrace);
                }
            }
        }

        /// <summary>
        /// 获取命令字符串
        /// </summary>
        private string GetCreateTableCommand(string tablename, IReadOnlyList<TableColumnAttribute> DataPropertyAttributes, IReadOnlyList<SchemaIndex> indices)
        {
            List<TableColumnAttribute> haveDefaultvalue = new List<TableColumnAttribute>();
            var ss = new StringBuilder();
            ss.AppendFormat("create table `{0}`(", tablename).AppendLine();
            string promarykey = "";
            List<TableColumnAttribute> indexDataes = new List<TableColumnAttribute>();
            foreach (var property in DataPropertyAttributes)
            {
                ss.AppendFormat("`{0}` ", property.Name);
                ss.AppendFormat("{0} ", MySqlParamHelper.GetDbType(property));
                if (property.IsPrimaryKey)
                {
                    promarykey = property.Name;
                    ss.Append("not null");
                }
                else
                {
                    if (property.NotNUll || property.IsPrimaryKey)
                    {
                        ss.Append("not null");
                    }
                    else
                    {
                        ss.Append("null");
                    }
                }

                if (property.DataIndex != DataIndex.None)
                {
                    indexDataes.Add(property);
                }

                if (!property.IsPrimaryKey)
                {
                    var df = GetDefaultValue(property);
                    if (df != null)
                    {
                        ss.Append(" default " + df);
                    }
                }

                ss.Append(",\r\n");
            }

            ss.AppendFormat(" PRIMARY KEY (`{0}`)", promarykey);
            ss.Append(")ENGINE =InnoDB DEFAULT CHARSET=utf8mb4;").AppendLine();
            ss.AppendLine();

            foreach (var i in indexDataes)
            {
                var dbType = MySqlParamHelper.GetDbType(i);
                if (dbType.StartsWith("Text"))
                {
                    ss.AppendLine(
                        $"CREATE INDEX `{i.Name}-{DateTime.Now.ToString("yyyyMMdd-HHmmss")}` ON `{tablename}` ( `{i.Name}` (100));");
                }
                else
                {
                    ss.AppendLine(
                        $"CREATE INDEX `{i.Name}-{DateTime.Now.ToString("yyyyMMdd-HHmmss")}` ON `{tablename}` ( `{i.Name}` );");
                }
            }

            if (indices?.Count > 0)
            {
                foreach (var index in indices)
                {
                    ss.AppendLine(GetCreateTableIndexCommand(index, tablename));
                }
            }

            Logger.Debug.Write("创建db指令:" + ss.ToString());
            return ss.ToString();
        }

        /// <summary>
        /// 检测类型是否增减字段，如果增加则更新到数据库
        /// </summary>
        /// <param name="tablename"></param>
        /// <param name="DataPropertyAttributes"></param>
        /// <param name="connect"></param>
        protected override void CheckTableChange(string tablename, List<TableColumnAttribute> DataPropertyAttributes, DbConnection connect)
        {
            List<string> tablecolumns = new List<string>();
            var sqc = new MySqlCommand();
            sqc.Connection = (MySqlConnection)connect;
            sqc.CommandType = CommandType.Text;
            string s =
                $"select `column_name` from `information_schema`.`columns` where `table_schema` = '{connect.Database}' and `table_name` = '{tablename}';";
            try
            {
                sqc.CommandText = s;
                sqc.CommandTimeout = Config.CommandTimeout;
                var reader = sqc.ExecuteReader();
                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        object cname = reader.GetValue(reader.GetOrdinal("column_name"));
                        tablecolumns.Add(cname.ToString());
                    }

                    reader.Close();
                }

                foreach (var property in DataPropertyAttributes)
                {
                    if (!tablecolumns.Contains(property.Name))
                    {
                        var lowerMatchIndex = tablecolumns.FindIndex(t => t.ToLower() == property.Name.ToLower());


                        string defaultValue = GetDefaultValue(property)?.ToString();
                        // s = "ALTER TABLE `" + tablename + "` ADD COLUMN `" + property.Name + "` " + MySqlParamHelper.GetDbType(property) ;
                        if (lowerMatchIndex >= 0)
                        {
                            s =
                                $"ALTER TABLE `{tablename}` change `{tablecolumns[lowerMatchIndex]}` `{property.Name}` {MySqlParamHelper.GetDbType(property)}";
                            Logger.Warning.Write(
                                $"Try to change table name, change {tablecolumns[lowerMatchIndex]} to {property.Name}");
                        }
                        else
                        {
                            s = "ALTER TABLE `" + tablename + "` ADD COLUMN `" + property.Name + "` " +
                                MySqlParamHelper.GetDbType(property);
                        }

                        if (!string.IsNullOrEmpty(defaultValue))
                        {
                            s += (" DEFAULT " + defaultValue);
                        }

                        sqc.CommandText = s;
                        sqc.CommandTimeout = Config.CommandTimeout;
                        int tt = sqc.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception e)
            {
                Logger.Fatel.Write("执行 SQL 错误: " + s + " Error: " + e.Message + e.StackTrace);
            }
        }

        /// <summary>
        /// 创建或者更新视图
        /// </summary>
        /// <param name="datainfo"></param>
        /// <param name="connectionString"></param>
        protected override void CreateOrAlterView(Type type, string connectionString)
        {
            var newConnectionString = connectionString.Replace("information_schema", Config.Database);
            using (var dbConnection = new MySqlConnection(newConnectionString))
            {
                dbConnection.Open();
                var commandText = $"select * from `information_schema`.`views` where `table_schema` = '{dbConnection.Database}' and `table_name` = 'V_{type.Name}'";
                using var dbCommand = new MySqlCommand(commandText, dbConnection);

                try
                {
                    string opcode;
                    using (var reader = dbCommand.ExecuteReader())
                    {
                        opcode = reader.HasRows ? "alter" : "create";
                    }

                    dbCommand.CommandText = opcode + GetViewCommand(type, dbConnection);
                    dbCommand.CommandTimeout = Config.CommandTimeout;
                    dbCommand.ExecuteNonQuery();
                }
                catch (Exception e)
                {
                    Logger.Error.Write($"执行 SQL 错误: {dbCommand.CommandText} Error: {e.ToString()}");
                }
            }
        }

        /// <summary>
        /// 获取创建或者修改视图的命令
        /// </summary>
        /// <param name="viewname"></param>
        /// <param name="createoralter"></param>
        /// <param name="datainfo"></param>
        /// <param name="connect"></param>
        /// <returns></returns>
        private string GetViewCommand(Type type, DbConnection connect)
        {
            TableColumnAttribute primarykey;
            var datainfo = type.GetTableColumnDictionary(out primarykey);
            System.Text.StringBuilder insertstring = new System.Text.StringBuilder();
            insertstring.AppendFormat(" view `{0}` \r\n as \n select  ", "V_" + type.Name);
            insertstring.AppendFormat("`{0}`.`{1}`,\n", type.Name, primarykey.Name);
            foreach (var key in datainfo.Keys)
            {
                var ts = datainfo[key];
                ts.Reverse();
                foreach (var value in ts)
                {
                    if (value.IsDelayLoad)
                    {
                        continue;
                    }

                    if (value.IsPrimaryKey)
                    {
                        continue;
                    }

                    if (value.Name == primarykey.Name && key != type.Name)
                    {
                        continue;
                    }

                    insertstring.AppendFormat("`{0}`.`{1}`,\n", key, value.Name);
                }
            }

            insertstring.Remove(insertstring.Length - 2, 1);

            insertstring.AppendFormat("\r\n from \r\n `{0}` ", type.Name);
            List<string> keys = datainfo.Keys.ToList();
            for (int i = 0; i < keys.Count; i++)
            {
                string key = keys[i];
                if (key == type.Name)
                {
                    continue;
                }

                //if (i != keys.Count - 1)
                {
                    insertstring.Append(" INNER JOIN \r\n");
                }
                insertstring.AppendFormat("`{0}` on `{1}`.`{2}` = `{0}`.`{2}` \n", key, type.Name, primarykey.Name);
            }

            return insertstring.ToString();
        }

        /// <summary>
        /// 检测是否存在存储过程
        /// </summary>
        /// <param name="type"></param>
        /// <param name="connectionString"></param>
        /// <returns></returns>
        protected override bool CheckProcedureExists(Type type, string connectionString = @"Server=.\SQLEXPRESS;Database=NewGameDB;integrated security=SSPI")
        {
            CheckTableExist(type, connectionString);
            string queryString = "SELECT * FROM sysobjects where xtype='p' and name='Update" + type.Name + "'";
            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                var command = new MySqlCommand(queryString, connection);
                command.Connection.Open();
                var reader = command.ExecuteReader();
                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        object cname = reader.GetString("name");
                    }

                    reader.Close();
                    CreateOrAlterProcedure(type, connectionString, ProcedureOperate.Alter,
                        ProcedureUsage.InsertAndUpdate);
                    return true;
                }
                else
                {
                    CreateOrAlterProcedure(type, connectionString, ProcedureOperate.Create,
                        ProcedureUsage.InsertAndUpdate);
                    return false;
                }
            }
        }

        /// <summary>
        /// 创建数据表类型
        /// </summary>
        protected void CreateTableType(Type type, string connectionString)
        {
            List<TableColumnAttribute> dataPropertyAttributes = type.GetJsonMembers()
                .Where(p => p is TableColumnAttribute).Cast<TableColumnAttribute>().ToList();
            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                connection.Open();
                Dictionary<string, object> input = new Dictionary<string, object>();

                string s = "SELECT   name   FROM  sys.types where name='" + type.Name + "TVP'";
                // SqlCommand sqc = new SqlCommand(s, connection);
                MySqlCommand sqc = new MySqlCommand(s, connection);
                try
                {
                    sqc.CommandText = s;
                    var reader = sqc.ExecuteReader();
                    if (reader.HasRows)
                    {
                        reader.Close();
                        string dropString = "drop type " + type.Name + "TVP";
                        sqc.CommandText = dropString;
                        var dr = sqc.ExecuteNonQuery();
                    }

                    reader.Close();
                    sqc.CommandText = GetCreateTableTypeCommand(type.Name, dataPropertyAttributes);
                    int r = sqc.ExecuteNonQuery();
                }
                catch (Exception e)
                {
                    Logger.Fatel.Write(e.Message + e.StackTrace);
                }
            }
        }

        protected string GetCreateTableTypeCommand(string tablename, IReadOnlyList<TableColumnAttribute> DataPropertyAttributes)
        {
            StringBuilder sb = new StringBuilder(48);
            sb.Append("create type ");
            sb.Append(tablename);
            sb.Append("TVP as Table(");
            string primaryKey = "";
            foreach (var property in DataPropertyAttributes)
            {
                sb.Append("[");
                sb.Append(property.Name);
                sb.Append("] ");
                sb.Append(property.DBType);
                sb.Append(" ");

                if (property.IsPrimaryKey)
                {
                    primaryKey = property.Name;
                }

                if (property.NotNUll || property.IsPrimaryKey)
                {
                    sb.Append("not null");
                }
                else
                {
                    sb.Append("null");
                }

                sb.Append(",\r\n");
            }

            sb.Append("PRIMARY KEY (");
            sb.Append(primaryKey);
            sb.Append("))");
            return sb.ToString();
        }

        protected override bool InitializeTable(Type type, string connectionString = @"Server=.\SQLEXPRESS;Database=NewGameDB;integrated security=SSPI")
        {
            CheckTableExist(type, connectionString);
            CheckProcedure(type, connectionString, ProcedureUsage.Update);
            CheckProcedure(type, connectionString, ProcedureUsage.Insert);
            CheckProcedure(type, connectionString, ProcedureUsage.InsertAndUpdate);

            DeleteOldProcedure(type, connectionString, ProcedureUsage.TVPUpdate);
            DeleteOldProcedure(type, connectionString, ProcedureUsage.TVPInsert);

            // CreateTableType(type, connectionString);

            CreateOrAlterProcedure(type, connectionString, ProcedureOperate.Create, ProcedureUsage.TVPUpdate);
            CreateOrAlterProcedure(type, connectionString, ProcedureOperate.Create, ProcedureUsage.TVPInsert);


            return true;
        }

        protected override void DeleteOldProcedure(Type type, string connectionString, ProcedureUsage insertOrUpdate)
        {
            string procedurename = insertOrUpdate + type.Name;
            using (MySqlConnection connection = new MySqlConnection(connectionString))
            {
                var s = $"drop procedure if exists  `{procedurename}`;";
                MySqlCommand command = new MySqlCommand(s, connection);
                try
                {
                    command.Connection.Open();
                    command.ExecuteNonQuery();
                }
                catch (Exception e)
                {
                    Logger.Fatel.Write($"执行 SQL 错误: {s}, {e.ToString()}");
                }
            }
        }

        private string GenerateTvpInsertProcedureCommandText(Type type, ProcedureOperate createOrAlter, ProcedureUsage insertOrUpdate)
        {
            var sb = new StringBuilder();
            var writer = new SourceWriter(sb);
            var procedurename = insertOrUpdate + type.Name;
            var tables = type.GetTableColumnDictionary(out var primary);
            writer.Write(createOrAlter.ToString()).Write(" PROCEDURE `").Write(procedurename).WriteLine("`(");
            writer.Embed(writer =>
            {
                using (var e = tables.Keys.GetEnumerator())
                {
                    if (e.MoveNext())
                    {
                        PrintArgument(writer, e.Current);
                        while (e.MoveNext())
                        {
                            writer.WriteLine(',');
                            PrintArgument(writer, e.Current);
                        }

                        sb.AppendLine();
                        static void PrintArgument(SourceWriter writer, string name)
                        {
                            writer.Write("IN ").Write(name).WriteLine("_COLUMNS LONGTEXT,");
                            writer.Write("IN ").Write(name).Write("_VALUES LONGTEXT");
                        }
                    }
                }
            });

            writer.WriteLine(")");
            writer.WriteLine("BEGIN");
            writer.Embed(writer =>
            {
                using (var e = tables.GetEnumerator())
                {
                    if (e.MoveNext())
                    {
                        PrintTable(writer, e.Current.Key, e.Current.Value);
                        while (e.MoveNext())
                        {
                            writer.WriteLine();
                            PrintTable(writer, e.Current.Key, e.Current.Value);
                        }
                    }
                }

                static void PrintTable(SourceWriter writer, string name, IReadOnlyList<TableColumnAttribute> columns)
                {
                    writer.Write("SET @EXEC_").Write(name).Write("_COMMAND_TEXT = CONCAT(\"INSERT INTO `").Write(name)
                        .Write("` (\", ").Write(name).Write("_COLUMNS, \") VALUES \", ").Write(name)
                        .Write("_VALUES, \" ON DUPLICATE KEY UPDATE ");
                    using (var e = columns.Where(p => !p.IsPrimaryKey).GetEnumerator())
                    {
                        if (e.MoveNext())
                        {
                            writer.Write('`').Write(e.Current.Name).Write("` = VALUES(`").Write(e.Current.Name)
                                .Write("`)");
                            while (e.MoveNext())
                            {
                                writer.Write(", `").Write(e.Current.Name).Write("` = VALUES(`").Write(e.Current.Name)
                                    .Write("`)");
                            }
                        }
                    }

                    writer.WriteLine(";\");");
                    writer.Write("PREPARE STMT_").Write(name).Write(" FROM @EXEC_").Write(name)
                        .WriteLine("_COMMAND_TEXT;");
                    writer.Write("EXECUTE STMT_").Write(name).WriteLine(';');
                    writer.Write("DEALLOCATE PREPARE STMT_").Write(name).WriteLine(';');
                }
            });
            writer.WriteLine("END");
            return sb.ToString();
        }

        private string GenerateProcedureCommandText(Type type, ProcedureOperate createOrAlter, ProcedureUsage insertOrUpdate)
        {
            switch (insertOrUpdate)
            {
                case ProcedureUsage.TVPInsert:
                case ProcedureUsage.TVPUpdate:
                    return GenerateTvpInsertProcedureCommandText(type, createOrAlter, insertOrUpdate);
                //break;
            }

            var sb = new StringBuilder();
            var writer = new SourceWriter(sb);
            var procedurename = insertOrUpdate + type.Name;
            var tPropertyDictionary = type.GetTableColumnDictionary(out var primarykey);
            writer.Write(createOrAlter.ToString()).Write(" Procedure `").Write(procedurename).WriteLine("` (");
            writer.Embed(writer => GetInputString(writer, type, insertOrUpdate));
            writer.WriteLine(')');
            writer.WriteLine("Begin");
            writer.Embed(writer => 
            {
                switch (insertOrUpdate)
                {
                    case ProcedureUsage.InsertAndUpdate:
                        writer.Write("If Exists (Select * From `").Write(type.Name).Write("` Where `").Write(primarykey.Name).Write("` = _").Write(primarykey.Name).WriteLine(") Then");
                        writer.Embed(writer => 
                        {
                            foreach (var (key, value) in tPropertyDictionary)
                            {
                                GetUpdateString(writer, key, value, primarykey);
                            }
                        });
                        writer.WriteLine("Else");
                        writer.Embed(writer => 
                        {
                            foreach (var (key, value) in tPropertyDictionary)
                            {
                                GetInsertString(writer, type.Name, key, value, insertOrUpdate);
                            }
                        });
                        writer.WriteLine("End If;");
                        break;
                    case ProcedureUsage.Insert:
                        foreach (var (key, value) in tPropertyDictionary)
                        {
                            GetInsertString(writer, type.Name, key, value, insertOrUpdate);
                        }
                        break;
                    case ProcedureUsage.Update:
                        foreach (var (key, value) in tPropertyDictionary)
                        {
                            GetUpdateString(writer, key, value, primarykey);
                        }
                        break;
                    default:
                        throw new InvalidOperationException($"unknown schema:{type.Name} procedure type:{insertOrUpdate}");
                }
            });
            writer.WriteLine("End;");
            return sb.ToString();
        }

        /// <summary>
        /// 创建或者修改存储过程
        /// </summary>
        protected override void CreateOrAlterProcedure(Type type, string connectionString, ProcedureOperate createOrAlter, ProcedureUsage insertOrUpdate = ProcedureUsage.Update)
        {
            var procedurename = insertOrUpdate + type.Name;
            var newConnectionString = connectionString.Replace("information_schema", Config.Database);
            using (var connection = new MySqlConnection(newConnectionString))
            {
                connection.Open();
                using var cmd = connection.CreateCommand();
                if (createOrAlter == ProcedureOperate.Create)
                {
                    cmd.CommandText = $"DROP PROCEDURE IF EXISTS `{procedurename}`;";
                    try
                    {
                        cmd.ExecuteNonQuery();
                    }
                    catch (Exception e)
                    {
                        Logger.Fatel.Write($"执行 SQL 错误: {cmd.CommandText}, {e.ToString()}");
                    }
                }

                cmd.CommandText = GenerateProcedureCommandText(type, createOrAlter, insertOrUpdate);

                // MySqlScript script = new MySqlScript(connection);
                // script.Query = qs;
                // script.Delimiter = MySqlParamHelper.ProcedureDelimiter;

                try
                {
                    cmd.ExecuteNonQuery();
                    // script.Execute();
                }
                catch (Exception e)
                {
                    Logger.Fatel.Write($"执行 SQL 错误: {cmd.CommandText}, {e.ToString()}");
                }
                // script.Delimiter = ";";
            }
        }

        private void GetInputString(SourceWriter writer, Type type, ProcedureUsage insertOrUpdate, char padChar = '\t', int padCharCount = 0)
        {
            var dpas = type.GetJsonMembers().OfType<TableColumnAttribute>();
            using (var e = dpas.GetEnumerator())
            {
                if (e.MoveNext())
                {
                    writer.Write("IN _").Write(e.Current.Name).Write(' ').Write(MySqlParamHelper.GetDbType(e.Current));
                    while (e.MoveNext())
                    {
                        writer.WriteLine(',');
                        writer.Write("IN _").Write(e.Current.Name).Write(' ').Write(MySqlParamHelper.GetDbType(e.Current));
                    }

                    writer.WriteLine();
                }
            }
        }

        private void GetUpdateString(SourceWriter writer, string tablename, List<TableColumnAttribute> dpas, TableColumnAttribute primarykey)
        {
            using (var e = dpas.Where(p => !p.IsPrimaryKey).GetEnumerator())
            {
                if (!e.MoveNext())
                {
                    return;
                }

                writer.Write("Update `").Write(tablename).WriteLine('`');
                writer.Write("Set `").Write(e.Current.Name).Write("` = _").WriteLine(e.Current.Name);
                while (e.MoveNext())
                {
                    writer.Write(", `").Write(e.Current.Name).Write("` = _").WriteLine(e.Current.Name);
                }

                writer.Write("Where `").Write(primarykey.Name).Write("` = _").Write(primarykey.Name).WriteLine(';');
            }
        }

        private void GetInsertString(SourceWriter writer, string typename, string tablename, List<TableColumnAttribute> dpas, ProcedureUsage procedureType)
        {
            var column = dpas[0];
            writer.Write("Insert Into `").Write(tablename).Write("` (`").Write(column.Name).Write('`');
            for (var i = 1; i < dpas.Count; i++)
            {
                column = dpas[i];
                writer.Write(", `").Write(column.Name).Write('`');
            }

            writer.WriteLine(')');
            column = dpas[0];
            writer.Write("Values (_").Write(column.Name);
            for (var i = 1; i < dpas.Count; i++)
            {
                column = dpas[i];
                writer.Write(", _").Write(column.Name);
            }

            writer.WriteLine(");");
        }

        #endregion

        public override object GetDefaultValue(TableColumnAttribute column)
        {
            return MySqlParamHelper.GetDefaultValue(column);
        }

        public override string FormatName(string name)
        {
            return MySqlParamHelper.FormatName(name);
        }

        public override IDbCommandStruct CreateCommand(string tableName, DbCommandMode mode, IEnumerable<SqlExpression> columns = null)
        {
            return new MySqlDbCommandStruct(tableName, mode, columns);
        }

        public override IDbCommandStruct<T> CreateCommand<T>(string tableName, DbCommandMode mode, IEnumerable<SqlExpression> columns = null)
        {
            return new MySqlDbCommandStruct<T>(tableName, mode, columns);
        }

        public override IDataParameter CreateParameter(string name, object value)
        {
            return new MySqlParameter(FormatParamName(name), value);
        }

        public override async ValueTask CheckSchemaChangeRecordAsync(SchemaUnitGenerator unit, SchemaEntity entity)
        {
            if (unit == null) throw new ArgumentNullException(nameof(unit));
            if (entity == null) throw new ArgumentNullException(nameof(entity));

            bool hasChanged;
            switch (entity)
            {
                case SchemaTable table:
                    hasChanged = await CheckTableAsync(unit, table);
                    break;
                case SchemaView view:
                    hasChanged = false;
                    for (var i = 0; i < view.Tables.Count; i++)
                    {
                        var table = view.Tables[i];
                        if (await CheckTableAsync(unit, table))
                        {
                            hasChanged = true;
                        }
                    }
                    if (!await ViewExistsAsync(view))
                    {
                        hasChanged = true;
                        unit.Add(new MySqlSchemaCreateViewGenerator(view));
                    }
                    else if (hasChanged)
                    {
                        unit.Add(new MySqlSchemaAlterViewGenerator(view));
                    }
                    break;
                default:
                    throw new InvalidOperationException($"unknown schema:{entity.Name}");
            }
            if (hasChanged)
            {
                CreateProcedureGenerator(unit, entity, ProcedureUsage.Insert);
                CreateProcedureGenerator(unit, entity, ProcedureUsage.Update);
                CreateProcedureGenerator(unit, entity, ProcedureUsage.TVPInsert);
                CreateProcedureGenerator(unit, entity, ProcedureUsage.TVPUpdate);
                CreateProcedureGenerator(unit, entity, ProcedureUsage.InsertAndUpdate);
            }

            void CreateProcedureGenerator(SchemaUnitGenerator unit, SchemaEntity entity, ProcedureUsage usage)
            {
                unit.Add(new MySqlSchemaDropProcedureGenerator(entity, usage));
                unit.Add(new MySqlSchemaCreateProcedureGenerator(entity, usage));
            }

            async ValueTask<IReadOnlyDictionary<string, Entity.Generators.DbColumn>> GetDbColumnsAsync(SchemaTable table)
            {
                var commandText = @$"Select `COLUMN_NAME`
, (`COLUMN_KEY` = 'PRI') As `IS_PRIMARY`
, (`IS_NULLABLE` = 'YES') As `IS_NULLABLE`
, `DATA_TYPE`
, IfNull(`CHARACTER_MAXIMUM_LENGTH`, 0) As `CHARACTER_MAXIMUM_LENGTH`
, IfNull(`NUMERIC_PRECISION`, 0) As `NUMERIC_PRECISION`
, IfNull(`NUMERIC_SCALE`, 0) As `NUMERIC_SCALE`
# , `COLUMN_TYPE`
From `information_schema`.`COLUMNS`
Where `TABLE_SCHEMA` = '{Config.Database}'
And `TABLE_NAME` = '{table.Name}'
Order By `ORDINAL_POSITION`";
                var columns = new Dictionary<string, Entity.Generators.DbColumn>();
                await ConnectionPool.ExecuteReaderAsync(commandText, async reader =>
                {
                    while (await reader.ReadAsync())
                    {
                        var name = reader.GetString(0);
                        var isPrimaryKey = reader.GetBoolean(1);
                        var isNullable = reader.GetBoolean(2);
                        var dataType = reader.GetString(3);
                        var maxLength = reader.GetInt64(4);
                        var precision = reader.GetInt64(5);
                        var scale = reader.GetInt64(6);
                        //var columnType = reader.GetString(7);
                        var dbType = DataBase.DBManager.MySql.Generators.MySqlDbColumn.GetDbType(dataType);
                        columns.Add(name, new DataBase.DBManager.MySql.Generators.MySqlDbColumn(name, isPrimaryKey, isNullable, dbType, maxLength, precision, scale));
                    }
                });

                return columns;
            }

            async ValueTask<bool> CheckTableAsync(SchemaUnitGenerator unit, SchemaTable table)
            {
                if (await TableExistsAsync(table))
                {
                    if (unit.Members.Any(p => p is MySqlSchemaAlterTableGenerator t && t.Name == table.Name))
                    {
                        return true;
                    }

                    List<SchemaColumnGenerator> alterColumns = null;
                    var dbColumns = await GetDbColumnsAsync(table);
                    var entityColumns = table.GetMappingColumns();
                    for (var i = 0; i < entityColumns.Count; i++)
                    {
                        var entityColumn = entityColumns[i];
                        if (dbColumns.TryGetValue(entityColumn.Name, out var dbColumn))
                        {
                            var dbType = MySqlParamHelper.GetDbType(entityColumn);
                            if (!dbColumn.ColumnType.Equals(dbType, StringComparison.OrdinalIgnoreCase))
                            {
                                alterColumns ??= new List<SchemaColumnGenerator>();
                                alterColumns.Add(new MySqlSchemaModifyColumnGenerator(entityColumn, dbColumn));
                            }
                        }
                        else
                        {
                            alterColumns ??= new List<SchemaColumnGenerator>();
                            alterColumns.Add(new MySqlSchemaAddColumnGenerator(entityColumn));
                        }
                    }
                    if (alterColumns == null)
                    {
                        return false;
                    }

                    unit.Add(new MySqlSchemaAlterTableGenerator(table, alterColumns));
                }
                else if (!unit.Members.Any(p => p is MySqlSchemaCreateTableGenerator t && t.Name == table.Name))
                {
                    unit.Add(new MySqlSchemaCreateTableGenerator(table));
                }

                return true;
            }

            async ValueTask<bool> TableExistsAsync(SchemaTable table)
            {
                var commandText = @$"Select Count(*)
From `information_schema`.`TABLES` 
Where `TABLE_TYPE` = 'BASE TABLE' 
And `TABLE_SCHEMA` = '{Config.Database}' 
And `TABLE_NAME` = '{table.Name}';";

                var r = await ConnectionPool.ExecuteScalarAsync(commandText);
                return Convert.ToInt32(r) > 0;
            }

            async ValueTask<bool> ViewExistsAsync(SchemaView view)
            {
                var commandText = $@"Select Count(*)
From `information_schema`.`TABLES` 
Where `TABLE_TYPE` = 'VIEW' 
And `TABLE_SCHEMA` = '{Config.Database}' 
And `TABLE_NAME` = '{view.Name}';";

                var r = await ConnectionPool.ExecuteScalarAsync(commandText);
                return Convert.ToInt32(r) > 0;
            }
        }
    }
}