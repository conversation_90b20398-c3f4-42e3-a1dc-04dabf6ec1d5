using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using DataBase.Data;
using DataBase.Entity;
using DataBase.Entity.Generators;
using DataBase.SqlExpressions;
using HelpBase;
using HelpBase.Diagnostics;
using HelpBase.Json;
using HelpBase.Threading;

namespace DataBase
{
    [Serializable]
    public abstract class DBManagerBase<TConnection> : JsonFormatObject, IDBManager
        where TConnection : DBConnectionBase, new()
    {
        private const int NoneSentinel = 0;
        private const int ActiveSentinel = 1;

        private readonly DBConnectionPool<TConnection> connectionPool;
        private readonly SaveStatus status = new SaveStatus();
        private readonly DBConfig config;
        private Dictionary<Type, UnsavedTable> tables = new Dictionary<Type, UnsavedTable>();
        private int isInSaving;

        public DBManagerBase(DBConfig config)
        {
            if (config == null) throw new ArgumentNullException(nameof(config));

            this.config = config;
            this.connectionPool = new DBConnectionPool<TConnection>(config);

            Logger.Data.Write($"Connect to db {connectionPool.ConnectionString}");
            if (config.IsCheckStructure)
            {
                CheckDBExists(connectionPool.ConnectionString);
            }
            if (config.WriteCheckPeriodInSeconds > 0)
            {
                TimerManager.AddToTimer(new TimeAction(config.WriteCheckPeriodInSeconds, WriteDatabaseCheckingTimerCallbacked));
            }
        }

        public abstract string ToSqlString<T>(Expression<Func<T, bool>> cond);
        
        public abstract string PreParam { get; }

        public string Name => config.ShowName;

        public DBConfig Config => config;

        public IDbManagerStatus Status => status;

        public IReadOnlyDictionary<Type, UnsavedTable> Tables => tables;

        public IDBConnectionPool ConnectionPool => connectionPool;

        public bool IsDisallowSaveRow { get; set; }

        public event SavingEventHandler Saving;

        public event SavedEventHandler Saved;

        public event StatusChangedEventHandler StatusChanged;

        private void TryWriteDatabase(string category)
        {
            if (Interlocked.CompareExchange(ref isInSaving, ActiveSentinel, NoneSentinel) == NoneSentinel)
            {
                Logger.Data.Write("{0}:{1},线程[{2}]", nameof(TryWriteDatabase), category, Thread.CurrentThread.GetHashCode());
                Task.Factory.StartNew(() =>
                {
                    try
                    {
                        SaveToDb(onfailed: NotifySavedEvent);
                    }
                    catch (Exception ex)
                    {
                        Logger.SaveError.Write("{0} error:{1}", nameof(SaveToDb), ex);
                    }
                    finally
                    {
                        Interlocked.Exchange(ref isInSaving, NoneSentinel);
                    }
                });
            }
        }

        private void WriteDatabaseCheckingTimerCallbacked()
        {
            if (status.LastSuccessTime.AddSeconds(config.WriteIntervalInSeconds) < DateTimeExtension.Now)
            {
                TryWriteDatabase("定时保存数据");
            }
            else if (tables.Values.Any(p => p.InsertRowCount > config.AutoSaveThreshold || p.UpdateRowCount > config.AutoSaveThreshold))
            {
                TryWriteDatabase("定量保存数据");
            }
        }

        private void WriteCompleted()
        {
            Logger.Data.Write("写入完成，开始切换待保存列表,总线程[{0}]", Process.GetCurrentProcess().Threads.Count);

            try
            {
                foreach (var table in tables.Values)
                {
                    table.WriteCompleted();
                }

                Logger.Data.Write("切换待保存列表成功，总线程[{0}]", Process.GetCurrentProcess().Threads.Count);
            }
            catch (Exception e)
            {
                Logger.Error.Write(e.Message + e.StackTrace);
            }
        }

        protected virtual UnsavedTable CreateUnsavedTable(Type rowEntityType, ProcedureUsage insertUsage, ProcedureUsage updateUsage)
        {
            var objParams = new object[] { this, insertUsage, updateUsage };
            var unsavedTableType = typeof(DoubleBufferUnsavedTable<>).MakeGenericType(rowEntityType);
            return (UnsavedTable)Activator.CreateInstance(unsavedTableType, objParams);
        }

        protected virtual bool WriteDatabase(ExecutionSuccessEventHandler executeSuccess, ExecutionFailedEventHandler executeFailed, out Exception exception)
        {
            DBConnectionBase connection = null;

            try
            {
                if (!Config.IsSaveData)
                {
                    exception = null;
                    NotifySavedEvent("no save");
                    return true;
                }

                connection = connectionPool.CheckOut();
                if (connection.Connection.State != ConnectionState.Open)
                {
                    connection.Connection.Open();
                }
            }
            catch (DbException ex)
            {
                Logger.Fatel.Write("打开DbConnection出错:" + ex.Message + "\r\n" + ex.StackTrace);
                if (connection != null)
                {
                    connectionPool.CheckIn(connection);
                }

                executeFailed?.Invoke(new Exception("打开DbConnection连接出错!"));
                exception = ex;
                return false;
            }

            try
            {
                DateTime begin = DateTimeExtension.Now;
                Logger.Data.Write("开始保存数据,总线程数[{0}]", Process.GetCurrentProcess().Threads.Count);
                foreach (var unsavedTable in tables.Values)
                {
                    status.Description = $"正在写入：{unsavedTable.EntityType.Name}";
                    connection.SaveDatas(unsavedTable, executeFailed, NotifyStatusChangedEvent);
                }

                var costSecends = (DateTimeExtension.Now - begin).TotalSeconds;
                Logger.Data.Write("写入[" + this.Name + "]总耗时[{0}]秒, 线程[{1}],总线程数[{2}]", costSecends, Thread.CurrentThread.GetHashCode(), Process.GetCurrentProcess().Threads.Count);

                executeSuccess?.Invoke(new ExcuteResult(this) { ReturnObject = "保存操作总耗时[" + costSecends + "]" });
                exception = null;
                NotifySavedEvent("上次保存[" + Name + "]结束于[" + DateTimeExtension.Now + "]总耗时[" + costSecends + "]秒");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Fatel.Write("数据保存出错:" + ex.Message + "\r\n" + ex.StackTrace);
                executeFailed?.Invoke(ex);

                NotifySavedEvent(ex);
                exception = ex;
                return false;
            }
            finally
            {
                status.Description = null;
                connectionPool.CheckIn(connection);
            }
        }

        protected void WriteStatus(string value)
        {
            status.Description = value;
        }

        public virtual void Initialize()
        { }

        public void AddUnsavedTable(Type entityType, Type baseType = null, ProcedureUsage insertUsage = ProcedureUsage.Insert, ProcedureUsage updateUsage = ProcedureUsage.Update)
        {
            if (entityType == null) throw new ArgumentNullException(nameof(entityType));
            if (!typeof(RowAdapter).IsAssignableFrom(entityType)) throw new ArgumentException($"Invalid entity type:{entityType.FullName}", nameof(entityType));

            var stopwatch = ValueStopwatch.StartNew();
            var unsavedTable = CreateUnsavedTable(entityType, insertUsage, updateUsage);
            if (config.IsCheckStructure)
            {
                InitializeTable(entityType, connectionPool.ConnectionString);
            }

            LockHelper.LockFreeUpdate(ref tables, p => 
            {
                var dictionary = new Dictionary<Type, UnsavedTable>(p);
                dictionary[entityType] = unsavedTable;
                return dictionary;
            });

            stopwatch.Stop();
            Logger.Data.Write("初始化[{0}]耗时[{1}]毫秒", entityType.Name, stopwatch.ElapsedMilliseconds);
        }

        public virtual void Save(long id, RowAdapter row)
        {
            if (IsDisallowSaveRow) return;
            if (row.IsTemped || id == 0 || !Config.IsSaveData)
            {
                return;
            }
            var type = row.GetType();
            if (tables.TryGetValue(type, out var table))
            {
                table.Save(id, row);
            }
        }

        public bool SaveToDb(ExecutionSuccessEventHandler onSuc = null, ExecutionFailedEventHandler onfailed = null)
        {
            if (!status.TryEnterSaving())
            {
                NotifySavedEvent(new Exception("上次保存还未完成!"));
                onfailed?.Invoke(new Exception("上次保存还未完成,请等待上次保存完成!"));
                return true;
            }

            try
            {
                Logger.Data.Write("开始保存内存[{0}]数据……", Name);
                NotifySavingEvent(DateTimeExtension.Now);
                if (!WriteDatabase(onSuc, onfailed, out var exception))
                {
                    status.ExitSaving(exception);
                    Logger.DataError.Write("保存[{0}]数据失败！", Name);
                    return false;
                }

                WriteCompleted();
                Logger.Data.Write("保存[{0}]数据成功!", Name);
                status.ExitSaving();
                return true;
            }
            catch (Exception ex)
            {
                status.ExitSaving(ex);
                Logger.Fatel.Write("save data error:{0}", ex);
                return false;
            }
        }

        public bool SaveToDb(RowAdapter data, bool cacheWhenError = true)
        {
            DBConnectionBase database = null;
            try
            {
                database = connectionPool.CheckOut();
                if (database.Connection.State != ConnectionState.Open)
                    database.Connection.Open();
            }
            catch (System.Data.SqlClient.SqlException e)
            {
                Logger.Fatel.Write("打开DBConnection出错:" + e.Message + "\r\n" + e.StackTrace);
                if (database != null)
                {
                    connectionPool.CheckIn(database);
                }
                return false;
            }

            try
            {
                database.UpdateData(data);
                data.IsLastSaveSuccess = true;
                data.SaveUsage = SaveUsage.Update;
                data.IsTemped = false;
                return true;
            }
            catch
            {
                if (cacheWhenError)
                {
                    data.IsTemped = false;
                    data.Save();
                    return true;
                }
                else
                {
                    Logger.Fatel.Write($"{nameof(SaveToDb)} fail while update data! " + Environment.StackTrace);
                    return false;
                }
            }
            finally
            {
                connectionPool.CheckIn(database);
            }
        }

        public void SaveToDb(SchemaEntity schema, IEnumerable<RowAdapter> rows)
        {
            if (schema == null) throw new ArgumentNullException(nameof(schema));
            if (rows == null) throw new ArgumentNullException(nameof(rows));

            var dbConnection = connectionPool.CheckOut();

            try
            {
                if (dbConnection.Connection.State != ConnectionState.Open)
                {
                    dbConnection.Connection.Open();
                }

                dbConnection.SaveToDb(schema, rows);
            }
            finally
            {
                connectionPool.CheckIn(dbConnection);
            }
        }

        public async ValueTask SaveToDbAsync(SchemaEntity schema, IEnumerable<RowAdapter> rows)
        {
            if (schema == null) throw new ArgumentNullException(nameof(schema));
            if (rows == null) throw new ArgumentNullException(nameof(rows));

            var dbConnection = connectionPool.CheckOut();

            try
            {
                if (dbConnection.Connection.State != ConnectionState.Open)
                {
                    await dbConnection.Connection.OpenAsync();
                }

                await dbConnection.SaveToDbAsync(schema, rows);
            }
            finally
            {
                connectionPool.CheckIn(dbConnection);
            }
        }

        #region 创建数据库表、存储过程等

        /// <summary>
        /// 检查数据库是否存在
        /// </summary>
        protected abstract void CheckDBExists(string connectionString);
        
        /// <summary>
        /// 检测数据库的表是否存在
        /// </summary>
        protected void CheckTableExist(Type type, string connectionString)
        {
            var td = type.GetTableColumnDictionary(out _);
            var schema = EntitySchemaManager.GetSchema(type);
            switch (schema)
            {
                case SchemaTable table:
                    CreateOrRefreshTable(table.Name, td[table.Name], table.Indices, connectionString);
                    break;
                case SchemaView view:
                    foreach (var table in view.Tables)
                    {
                        CreateOrRefreshTable(table.Name, td[table.Name], table.Indices, connectionString);
                    }

                    CreateOrAlterView(type, connectionString);
                    break;
                default:
                    throw new InvalidOperationException($"unknown schema:{schema.GetType().FullName}. type:{type.FullName}");
            }
        }

        /// <summary>
        /// 创建或者更新表
        /// </summary>
        protected abstract void CreateOrRefreshTable(string tablename, List<TableColumnAttribute> DataPropertyAttributes, IReadOnlyList<SchemaIndex> indices, string connectionString);
 
        /// <summary>
        /// 检测类型是否增减字段，如果增加则更新到数据库
        /// </summary>
        protected abstract void CheckTableChange(string tablename, List<TableColumnAttribute> DataPropertyAttributes, DbConnection connect);

        /// <summary>
        /// 创建或者更新视图
        /// </summary>
        protected abstract void CreateOrAlterView(Type type, string connectionString);

        /// <summary>
        /// 检测是否存在存储过程
        /// </summary>
        protected abstract bool CheckProcedureExists(Type type, string connectionString = @"Server=.\SQLEXPRESS;Database=NewGameDB;integrated security=SSPI");

        /// <summary>
        ///  根据类型创建或者刷新表和存储过程
        /// </summary>
        protected abstract bool InitializeTable(Type type, string connectionString = @"Server=.\SQLEXPRESS;Database=NewGameDB;integrated security=SSPI");

        protected void CheckProcedure(Type type, string connectionString, ProcedureUsage insertOrUpdate)
        {
            DeleteOldProcedure(type, connectionString, insertOrUpdate);
            CreateOrAlterProcedure(type, connectionString, ProcedureOperate.Create, insertOrUpdate);
        }

        protected abstract void DeleteOldProcedure(Type type, string connectionString, ProcedureUsage insertOrUpdate);

        /// <summary>
        /// 创建或者修改存储过程
        /// </summary>
        protected abstract void CreateOrAlterProcedure(Type type, string connectionString, ProcedureOperate createOrAlter, ProcedureUsage insertOrUpdate = ProcedureUsage.Update);
        #endregion

        public abstract object GetDefaultValue(TableColumnAttribute column);

        public abstract string FormatName(string name);

        public abstract IDbCommandStruct CreateCommand(string tableName, DbCommandMode mode, IEnumerable<SqlExpression> columns = null);

        public abstract IDbCommandStruct<T> CreateCommand<T>(string tableName, DbCommandMode mode, IEnumerable<SqlExpression> columns = null);

        public abstract IDataParameter CreateParameter(string name, object value);

        public string FormatParamName(string name)
        {
            if (name == null)
                throw new ArgumentNullException(nameof(name));

            return name.StartsWith(PreParam) ? name : PreParam + name;
        }

        private void NotifySavingEvent(DateTime node)
        {
            Saving?.Invoke(this, new SavingEventArgs(node));
        }

        private void NotifySavedEvent(string statusText)
        {
            Saved?.Invoke(this, new SavedEventArgs(statusText));
        }

        private void NotifySavedEvent(Exception exception)
        {
            Saved?.Invoke(this, new SavedEventArgs(exception));
        }

        private void NotifyStatusChangedEvent(string statusText)
        {
            StatusChanged?.Invoke(this, new StatusChangedEventArgs(statusText));
        }

        protected override void Dispose(bool disposing)
        {
            if (!IsDisposed)
            {
                try
                {
                    connectionPool.Dispose();
                }
                finally
                {
                    base.Dispose(disposing);
                }
            }
        }

        public abstract ValueTask CheckSchemaChangeRecordAsync(SchemaUnitGenerator unit, SchemaEntity entity);
    }
}