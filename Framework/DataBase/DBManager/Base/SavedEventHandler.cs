using System;

namespace DataBase
{
    public delegate void SavedEventHandler(object sender, SavedEventArgs e);

    public sealed class SavedEventArgs : EventArgs
    {
        internal SavedEventArgs(string statusText)
        {
            StatusText = statusText;
        }

        internal SavedEventArgs(Exception exception)
        {
            Exception = exception;
        }

        public string StatusText { get; }

        public Exception Exception { get; }
    }
}
