using System;
using System.Collections.Generic;
using System.Linq;
using GameModel.CooperateGame;
using GameModel.DataModel;
using GameModel.DataModel.State;
using GameModel.Dimensions;
using GameModel.Functions.Items;
using GameModel.Managers;
using Google.Protobuf.WellKnownTypes;
using Proto.GameModel;

namespace GameModel.Command
{
    public partial class CmdHandler
    {
        /// <summary>
        /// 获取矿工背包数据
        /// </summary>
        /// <returns>PicaMineRequestInfo <see cref="Proto.GameModel.PicaMineData"/></returns>
        public void PicaMineRequestInfo()
        {
            Character.SendMineData();
        }

        /// <summary>
        /// 装备新矿镐
        /// </summary>
        /// <param name="IM"></param>
        /// <returns>PicaMineEquipPickaxe <see cref="Proto.GameModel.IDMessage"/></returns>
        public void PicaMineEquipPickaxe(IDMessage IM)
        {
            if (IM.Id <= 0) return;
            if (Character.PicaMineData.CurrentPickaxeId != IM.Id)
            {
                Character.PicaMineData.CurrentPickaxeId = IM.Id;
            }
            Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000093);
            Character.SendGoogleProto(ClientMethod.PicaMineEquipPickaxe, new IDMessage() { Id = 1 });
        }

        /// <summary>
        /// 修复 升级矿镐等
        /// </summary>
        /// <param name="PMPU"></param>
        public void PicaMinePickaxeUpgrage(PicaMinePickaxeUpgrage PMPU)
        {
            switch (PMPU.Type)
            {
                case 1: //修复
                    if (Character.PicaMineData.DiggingForce.Durability >= Character.PicaMineData.DiggingForce.MaxDurability)
                    {
                        Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000084);
                        return;
                    }
                    if (Character.CheckVirtualCurrencyCountEnough(2, 20)) // 矿镐修复价格
                    {
                        Character.Pay(2, 20, "修复矿镐", "", 0);
                        Character.PicaMineRecover();
                        Character.SendMineData();
                        return;
                    }
                    else
                    {
                        Character.ShowLackOfCoin();
                    }
                    break;
                case 2: //升级
                    var minePickaxeLevelData = GameApplication.DataManager.FindValue<PicaMinePickaxeLevelType>(Character.PicaMineData.DiggingForce.Level + 1);
                    if (minePickaxeLevelData == null)
                    {
                        Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000086);
                        //ShowMessage();
                        return;
                    }
                    minePickaxeLevelData = GameApplication.DataManager.FindValue<PicaMinePickaxeLevelType>(Character.PicaMineData.DiggingForce.Level);
                    foreach (var material in minePickaxeLevelData.StrengthenMaterial)
                    {
                        Item item = Character.FindEntities<Item>(t => t.Type == material.Type).FirstOrDefault();
                        if (item == null || item.Count < material.Count)
                        {
                            Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, PicaLocalityStrings.ItemName(material.Type));
                            return;
                        }
                    }
                    foreach (var material in minePickaxeLevelData.StrengthenMaterial)
                    {
                        Item item = Character.FindEntities<Item>(t => t.Type == material.Type).FirstOrDefault();
                        item.Delete(material.Count, "Mine.UpgradeWeapon");
                    }
                    var level_before = Character.PicaMineData.DiggingForce.Level;
                    var count = minePickaxeLevelData.StrengthenResult.GetRandomItem().UpCount;
                    Character.PicaMineUpgradeLevel(count);
                    var level_after = Character.PicaMineData.DiggingForce.Level;

                    var suc = count > 0 ? "成功" : "失败";
                    Logger.Supplement.Write($"{Character.ID} 防爆服 升级矿镐。{level_before} -> {level_after} {suc}");

                    //Character.GetLeiTingTrackWriter("mining_pick_level_up")
                    //    .WriteEntry("level_before", level_before)
                    //    .WriteEntry("level_after", level_after)
                    //    .WriteEntry("status", suc)
                    //    .WriteToTaLogger();

                    Character.SendGoogleProto(ClientMethod.PicaMinePickaxeUpgrageResult, new IDMessage() { Id = count });
                    Character.SendMineData();
                    break;
                case 3: //锻造
                    switch (PMPU.Forging)
                    {
                        case 1: //锻造挖矿数量
                            if (Character.PicaMineData.DiggingForce.GetCount() >= 3)
                            {
                                Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000087);
                                return;
                            }
                            var countLevel = Character.PicaMineData.DiggingForce.UpCount + 1;
                            if (Character.CheckVirtualCurrencyCountEnough(3, countLevel * countLevel * 5000)) // 矿镐锻造数量价格
                            {
                                Character.AddVirtualCurrency(3, -countLevel * countLevel * 5000, "Mine.ForgeWeapon.Count", "", 0);
                                Character.PicaMineForgeCount();
                                //Character.SendGoogleProto(ClientMethod.PicaMinePickaxeUpgrageResult, new IDMessage() { Id = 1 });
                                Character.SendMineData();
                                return;
                            }
                            else
                            {
                                Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000088);
                            }
                            //Character.SendGoogleProto(ClientMethod.PicaMinePickaxeUpgrageResult, new IDMessage() { Id = 1 });
                            break;
                        case 2: //耐久度消耗减少
                            if (Character.PicaMineData.DiggingForce.UpReduceDurability >= 10)
                            {
                                Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000087);
                                return;
                            }
                            var reduceDurabilityLevel = Character.PicaMineData.DiggingForce.UpReduceDurability + 1;
                            if (Character.CheckVirtualCurrencyCountEnough(3, reduceDurabilityLevel * reduceDurabilityLevel * 4000)) // 矿镐锻造减少消耗价格
                            {
                                Character.AddVirtualCurrency(3, -reduceDurabilityLevel * reduceDurabilityLevel * 4000, "Mine.ForgeWeapon.ReduceDurability", "", 0);
                                Character.PicaMineForgeReduceDurability();
                                //Character.SendGoogleProto(ClientMethod.PicaMinePickaxeUpgrageResult, new IDMessage() { Id = 1 });
                                Character.SendMineData();
                                return;
                            }
                            else
                            {
                                Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000088);
                            }
                            //Character.SendGoogleProto(ClientMethod.PicaMinePickaxeUpgrageResult, new IDMessage() { Id = 1 });
                            break;
                    }
                    break;
            }
        }

        private PicaMineRoom createMineRoom(string name, long level, int playerCount, bool isOpen, int stage)
        {
            var room = new PicaMineRoom()
            {
                Name = name,
                Level = level,
                PlayerCount = playerCount,
                IsOpen = isOpen,
                Stage = stage
            };
            return room;
        }

        private int GetRoomsPlayerCount(int levelMin, int levelMax)
        {
            var playerCount = 0;
            var dimension = GameApplication.SceneManager.GetDimensionByType(DimensionType.PicaMine);
            var sceneList = dimension.Levels.Values.Where(p =>
            {
                return p.SceneType.DimensionLevel >= levelMin && p.SceneType.DimensionLevel < levelMax;
            }
            );
            foreach (var scene in sceneList)
            {
                scene.Scenes.Values.ToList().ForEach(p => playerCount += p.PlayerCount);
            }
            return playerCount;
        }

        /// <summary>
        /// 获取矿洞列表
        /// </summary>
        /// <returns>PicaMineGetRoomList <see cref="Proto.GameModel.PicaMineRoomListData"/></returns>
        public void PicaMineGetRoomList()
        {
            var PMRLD = new PicaMineRoomListData();
            var roomList = GetRoomList();
            foreach (var room in roomList)
            {
                for (int i = 1; i <= room.RoomCount; i++)
                {
                    var dimensionLevel = room.ID * 100000 + i * 10000;
                    var roomConfig = new PicaMineRoom()
                    {
                        Name = I18nManager.I18n(room.Name, room.MinLevel.ToString()),
                        Level = dimensionLevel + room.MinLevel,
                        //PlayerCount = GetRoomsPlayerCount((int)(dimensionLevel + room.MinLevel), (int)(dimensionLevel + room.MaxLevel)),
                        IsOpen = Character.PicaMineData.CompleteMaxFloor >= room.NeedMaximumLevel,
                        Stage = (int)room.ID,
                    };
                    roomConfig.ExplosionProofs.AddRange(room.ExplosionProofList);
                    PMRLD.RoomList.Add(roomConfig);
                }
            }
            Character.SendGoogleProto(ClientMethod.PicaMineGetRoomList, PMRLD);
        }

        private List<PicaMineRoomListType> GetRoomList()
        {
            return GameApplication.DataManager.FindValue<PicaMineRoomListType>(p => { return true; });
        }

        private Dictionary<long, bool> LockEnterEvent = new Dictionary<long, bool>();
        public void PicaMineEnter(IDListMessage iDList)
        {
            if (!LockEnterEvent.ContainsKey(Character.ID)) LockEnterEvent.Add(Character.ID, false);
            if (LockEnterEvent[Character.ID]) return;
            LockEnterEvent[Character.ID] = true;
            var roomFloor = (int)(iDList.Items[0] % 10000);
            var RoomListConfig = GameApplication.DataManager.FindValue<PicaMineRoomListType>(p => roomFloor >= p.MinLevel && roomFloor <= p.MaxLevel).FirstOrDefault();
            var skill_to_learn = Character.FindSkillsToLearn(RoomListConfig.NecessarySkill);
            if (skill_to_learn != 0)
            {
                Character.SendGoogleProto(ClientMethod.ShowRequireSkill, new IDMessage() { Id = skill_to_learn });
                LockEnterEvent[Character.ID] = false;
                return;
            }
            Character.MineArrowNext = true;
            if (!CanEnterMine(DimensionType.PicaMine, iDList.Items[0]))
            {
                LockEnterEvent[Character.ID] = false;
                Character.MineArrowNext = false;
                return;
            }
            Character.MineArrowNext = false;
            if (iDList.Items[1] != 0)
            {
                var et = GameApplication.DataManager.FindValueNoConstraint<EntityType>(iDList.Items[1]);
                var item = et as ItemType;
                if (item == null)
                {
                    Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000015);
                    LockEnterEvent[Character.ID] = false;
                    return;
                }
                var func = item.GetFunction<PicaMineExplosionProofFunction>();
                if (func == null)
                {
                    Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000088);
                    LockEnterEvent[Character.ID] = false;
                    return;
                }
                
                var entity = Character.GetVirtualCurrency(2, false);
                var count = entity == null ? 0 : entity.Value;
                if (count < func.Sell)
                {
                    Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, I18nManager.VirtualCurrencyName(2));
                    LockEnterEvent[Character.ID] = false;
                    return;
                }

                Character.SelectExplosionProof = iDList.Items[1];
                //Character.Pay(2, func.Sell, "进入矿洞");
                //// 添加防弹衣次数
                //Character.PicaMineSetExplosionProofCount(func.Times);
                //Character.CheckUpdateAction(Character, UserAction.购买防爆服, value: func.Times);
            }
            else
            {
                // 没选择防弹衣
                Character.SelectExplosionProof = -1;
                //Character.PicaMineSetExplosionProofCount(0);
            }
            // 每次进入矿洞重置各种状态
            Character.PicaMineFirstEnter();

            LockEnterEvent[Character.ID] = false;
            //Character.EnterPicaPublicScene(DimensionType.PicaMine, iDList.Items[0]);
            Character.TryEnterMineRoom(iDList.Items[0], true);
        }

        private bool CanEnterMine(DimensionType sceneType, long sceneLevel)
        {
            var dimension = GameApplication.SceneManager.GetDimensionByType(sceneType);
            if (dimension == null)
            {
                return false;
            }
            var level = sceneLevel == -1 ? dimension.Main : dimension.GetLevel(sceneLevel);
            if (level == null)
            {
                return false;
            }
            var scene = dimension.GetAvailableScene(Character.SceneCharacter, level);
            if (scene != null)
            {
                return scene.CanEnter(Character);
            }
            return false;
        }

        /// <summary>
        /// 进入新一层的矿洞 ,处理一些计时类型和状态类型的东西
        /// </summary>
        public void PicaMineEnteredLevel()
        {
            Character.PicaMineEnteredLevel();
        }

        /// <summary>
        /// 传送到异世界
        /// </summary>
        public void PicaMineEnterMagicWorld(ActionBytesMessage data)
        {
            MineActionResult action = (MineActionResult)data.Action;
            switch (action)
            {
                case MineActionResult.异世界传送:
                    if (Character.MagicWordSceneType != 0)
                    {
                        var typeId = Character.MagicWordSceneType;
                        Character.MagicWordSceneType = 0;
                        //Character.LastMineLevel = Character.SceneCharacter.CurrentScene.DimensionLevel.Level;
                        //Character.Save();
                        var sceneType = GameApplication.DataManager.FindValue<SceneType>(typeId);
                        Character.EnterPicaPublicScene(sceneType.DimensionType, typeId);
                    }
                    break;
                case MineActionResult.特产矿洞:
                    if (Character.SpecialLevel != 0)
                    {
                        var level = Character.SpecialLevel;
                        Character.SpecialLevel = 0;
                        //Character.Save();
                        //Character.TryEnterMineRoom(level, true);
                        //特产矿洞每次进都要新建一个场景
                        var dimension = GameApplication.SceneManager.GetDimensionByType(DimensionType.PicaMine);
                        if (dimension == null)
                        {
                            ShowError(LocalityStrings.argumentError);
                            return;
                        }
                        var dimensionLevel = dimension.GetLevel(level);
                        if (dimensionLevel == null)
                        {
                            ShowError(LocalityStrings.argumentError);
                            return;
                        }

                        Character.CheckUpdateAction(Character, UserAction.特定产出物矿洞, level, mode : MissionValueTriggerCategory.Assignment);
                        if (!dimension.Enter(Character.SceneCharacter, dimensionLevel))
                        {
                            Logger.GameError.Write($"{nameof(PicaMineEnterMagicWorld)} Dimension: [{dimension.ID}][{dimension.Type}], Level: [{dimensionLevel.Level}] Character.ID:{Character.ID} enter scene fail. 3");
                            return;
                        }
                    }
                    break;
                case MineActionResult.特产矿石全部挖完:
                    if (Character.SceneCharacter.CurrentScene is PicaMineScene mineScene && mineScene.RoomConfig.SpecialLevel)
                    {
                        Character.TryEnterMineRoom(Character.SceneCharacter.CurrentMineLevel, true);
                    }
                    break;
                default:
                    break;
            }
        }

        public void PicaMineEnterNextLevel(ActionBytesMessage data)
        {
            if (Character.MineArrowNext && Character.SceneCharacter.CurrentScene is PicaMineScene scene)
            {
                switch (data.Action)
                {
                    case 0:
                        var newLevel = scene.DimensionLevel.Level + 1;
                        Character.EnterPicaPublicScene(DimensionType.PicaMine, newLevel);
                        break;
                    case 1:
                        long level;
                        if (scene.RoomFloor == 1)
                        {
                            level = scene.DimensionLevel.Level + 1;
                        }
                        else if (GameApplication.DataManager.FindValue<PicaMineRoomType>(p => scene.RoomFloor + 1 >= p.MinIndex && scene.RoomFloor + 1 <= p.MaxIndex).FirstOrDefault() == null)
                        {
                            level = scene.DimensionLevel.Level - 1;
                        }
                        else
                        {
                            level = scene.DimensionLevel.Level + (RandomEvent.RandomBoolean() ? 1 : -1);
                        }
                        Character.EnterPicaPublicScene(DimensionType.PicaMine, level);
                        break;
                }
            }
        }

        public void PicaMineBombDisposal(IDMessage data)
        {
            var targetActor = Character.SceneCharacter.CurrentArea.FindPlayer(data.Id);
            if (targetActor == null)
            {
                Character.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00070"));
                return;
            }
            var targetBuff = targetActor.Owner.GetBuff(12);
            if (targetBuff == null || targetBuff.Deleted)
            {
                Character.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00071"));
                return;
            }
            var disposaled = RandomEvent.RandomHit(3333);//三分之一概率
            targetBuff.Delete(!disposaled);
            if (disposaled)
            {
                Character.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00072", targetActor.Owner.Name));
                targetActor.Owner.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00073", Character.Name));
            }
            else
            {
                Character.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00074", targetActor.Owner.Name));
                targetActor.Owner.ShowTipsMessage(I18nManager.I18n("PKT_NEWMINE_SYS_00075", Character.Name));
            }
            var state = new MineTimeBombState() { EndTime = DateTimeExtension.Now.AddSeconds(-10) };
            targetActor.AddOrUpdateState(state, true);
        }

        #region 合作矿洞
        public void CooperateMineRequestInfo()
        {
            Character.SendCooperateMineData();
        }

        public void CooperateMineNextRoom()
        {
            if (Character.TryGetCooperateGame<CooperateMineTeam>(out var data))
            {
                if (data.OwnerID == Character.ID)
                {
                    data.CheckEnterNextRoom(Character);
                }
            }
        }

        /// <summary>
        /// 修复合作矿洞矿镐耐久度
        /// </summary>
        public void CooperateMineRestoreDurability()
        {
            if (Character.TryGetCooperateGame<CooperateMineTeam>(out var team))
            {
                if (Character.CooperateMineDurability > 0)
                {
                    Character.ShowTipsMessage("耐久度还未消耗完");
                    return;
                }
                var rd = team.Config.GetRestoreDurabilityConfig(Character.CooperateMineRestoreTimes + 1);

                foreach (var ma in rd.Requirement)
                {
                    var num = ma.Count;
                    if (ma.Type <= 100)
                    {
                        if (!Character.CheckVirtualCurrencyCountEnough(ma.Type, num))
                        {
                            var name = GameApplication.DataManager.FindValue<VirtualCurrencyType>(ma.Type).Name;
                            Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, name);
                            return;
                        }
                    }
                    else
                    {
                        if (!Character.CheckEntityCountEnough(ma.Type, num))
                        {
                            var name = GameApplication.DataManager.FindValue<EntityType, ItemType>(ma.Type).Name;
                            Character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, name);
                            return;
                        };
                    }
                }
                foreach (var ma in rd.Requirement)
                {
                    var num = ma.Count;
                    if (ma.Type <= 100)
                    {
                        Character.Pay(ma.Type, num, "合作矿洞矿镐修复");
                    }
                    else
                    {
                        var item = Character.FindEntities<Item>(t => t.Type == ma.Type).FirstOrDefault();
                        item.Delete(num, "合作矿洞矿镐修复");
                    }
                }

                Character.CooperateMineRestoreTimes++;
                Character.CooperateMineDurability = rd.RestoreValue;
                Character.Save();
                Character.SendCooperateMineData();

                Character.ShowTipsMessage("矿镐修复成功");
            }
        }

        #endregion
    }
}
