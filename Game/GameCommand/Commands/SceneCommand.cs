using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Numerics;
using GameCommand.Utility;
using GameModel.Dimensions;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Linq;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Command
{
    public partial class CmdHandler
    {
        #region Scene

        private int verifyEnterSceneTimes;

        public void VerifyEnterScene(VerifyEnterSceneRequest req)
        {
            if (verifyEnterSceneTimes >= 100000)
            {
                ShowError(LocalityStrings.operationError);
                return;
            }

            verifyEnterSceneTimes++;
            var rsp = new VerifyEnterSceneResponse();
            rsp.SceneID = req.SceneID;
            var scene = GameApplication.SceneManager.GetScene(req.SceneID, true);
            if (scene == null)
            {
                rsp.Success = false;
                rsp.Message = LocalityStrings.targetSceneNotFound.DefaultString;
                SendGoogleProto(ClientMethod.VerifyEnterSceneResult, rsp);
                return;
            }
            if (!scene.CanEnter(Character, req.Password))
            {
                rsp.Success = false;
                //rsp.Message = error.DefaultString;
                SendGoogleProto(ClientMethod.VerifyEnterSceneResult, rsp);
                return;
            }

            rsp.Success = true;
            rsp.Password = req.Password;
            SendGoogleProto(ClientMethod.VerifyEnterSceneResult, rsp);
        }

        private bool IsAvailable(SceneSpec scene)
        {
            return scene.OwnerID != 0 &&
                scene.EnterRule < SceneEnterRule.Lock;
        }

        /// <summary>
        /// 房间点赞
        /// </summary>
        /// <returns>ShowSceneDetail <see cref="Proto.GameModel.SceneDetailResponse"/></returns>
        public void PraiseRoom()
        {

            if (!Character.LearntTargetSkill(610))
            {
                Character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000040, Managers.I18nManager.SkillName(610));
                Character.SendGoogleProto(ClientMethod.ShowRequireSkill, new IDMessage() { Id = 610 });
                return;
            }
            if (Character.RoomData.PraisedRoomIds.Contains(Character.SceneCharacter.CurrentSceneID))
            {
                //pkttodo 给提示
                Character.ShowError(LocalityStrings.GetUndefinedString("已经点过赞了"));
                return;
            }
            //基本上点赞一定要在这个房间才能点
            var room = GameApplication.SceneManager.GetScene<PicaRoomScene>(Character.SceneCharacter.CurrentSceneID);

            if (room == null)
            {
                //pkttodo errorLog
                Character.ShowError(LocalityStrings.GetUndefinedString("这里不是房间"));
                return;
            }

            Character.RoomData.PraisedRoomIds.Add(Character.SceneCharacter.CurrentSceneID);
            Character.RoomData.Save();
            Character.Save();
            room.PraiseCount++;
            //var record = room.GetFunctionRecord<PicaRoomExtraDatasFunctionRecord>(true);
            //record.TotalPraiseCount++;
            ////record.UpdateTo();
            room.CheckUpdatePopularity(RoomPopularityValueChangeAction.点赞);
            Character.RecordRoomOperation(RecordOperation.Praise, room, Character.GetXmlTag(), room.ShowName, "2");
            Character.CheckUpdateAction(Character, UserAction.点赞);
            if (Character.IsInLove(room.OwnerID))
            {
                Character.CheckUpdateAction(Character, UserAction.羁绊对象点赞);
            }
            if (room.PraiseCount > room.Owner.ActionTime.MaxRoomPraiseCount)
            {
                room.Owner.ActionTime.MaxRoomPraiseCount = room.PraiseCount;
                room.Owner.Save();
                room.Owner.CheckUpdateAction(room.Owner, UserAction.点赞数门槛, value: room.Owner.ActionTime.MaxRoomPraiseCount, mode: MissionValueTriggerCategory.Assignment, sceneID: room.ID);
            }
            room.Save();

            var rsp = new SceneDetailResponse();
            rsp.Data = room.DetailData;
            rsp.Data.CanPraise = false;
            User.SendGoogleProto(ClientMethod.ShowSceneDetail, rsp);

            var nd2 = new SceneAreaIDInt64StringMessage();
            nd2.Msg = "thumbsup";
            nd2.ID = Character.ID;
            nd2.SceneID = Character.SceneCharacter.CurrentScene.ID;
            nd2.AreaID = Character.SceneCharacter.CurrentAreaID;
            nd2.Value = 1;
            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayActorAnim, nd2);

            var nd = new SceneAreaIDInt64StringMessage();
            nd.Msg = "likeEffect1";
            nd2.ID = Character.ID;
            nd2.SceneID = Character.SceneCharacter.CurrentScene.ID;
            nd2.AreaID = Character.SceneCharacter.CurrentAreaID;
            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayActorEff, nd);


            //var g = Character.SceneCharacter.Area.Objects.Where(p => p.Value.Type == 402072).FirstOrDefault();
            //string w ="状态:"+ g.Value.GetFunctionRecord<PicaCookFoodFunctionRecord>(false).Type + ",准备：" + g.Value.GetFunctionRecord<PicaCookFoodFunctionRecord>(false).Ready + ",";
            //string w2 ="挂在物"+ String.Join(",", g.Value.InteractionSceneObjects.ToList());
            //    Logger.Warning.Write(w+w2);
        }

        /// <summary>
        /// 获得导航栏场景列表
        /// </summary>
        /// <param name="sceneTypeList"></param>
        /// <returns>ShowNavigationSceneList <see cref="Proto.GameModel.SceneListData"/></returns>
        public void GetNavigationSceneList(IDListMessage sceneTypeList)
        {

            var rsp = new SceneListData();

            foreach (var sceneTypeId in sceneTypeList.Items)
            {
                var sceneSpec = GameApplication.SceneManager.FindSpec<SceneSpec>(spec => spec.Type == sceneTypeId);
                if (sceneSpec == null)
                {
                    var sceneType = GameApplication.DataManager.FindValue<SceneType>(sceneTypeId);
                    if (sceneType == null)
                    {
                        // error
                        continue;
                    }
                    var dimension = GameApplication.SceneManager.GetDimensionByType(sceneType.DimensionType);
                    if (dimension == null)
                    {
                        continue;
                    }
                    var level = dimension.GetLevel(sceneTypeId) ?? dimension.Main;
                    var scene = dimension.GetAvailableScene(Character.SceneCharacter, level);
                    sceneSpec = scene.Spec;
                }
                rsp.Items.Add(sceneSpec.GlobalPlayerCountProtoData); ;
            }

            rsp.Total = rsp.Items.Count();

            SendGoogleProto(ClientMethod.ShowNavigationSceneList, rsp);
        }

        /// <summary>
        /// 获取玩家房间列表
        /// </summary>
        /// <param name="req"></param>
        /// <returns>ShowRoomList <see cref="Proto.GameModel.PicaRoomSceneListData"/></returns>
        public void GetRoomList(SceneListRequestData req)
        {
            var index = req.StartIndex;
            var count = req.Count;
            if (index < 0 || count <= 0)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            if (req.Conditions == null || req.Conditions.Count == 0)
            {
                //scenes = GameApplication.SceneManager.RoomCache.Skip(index == 0 ? 0 : index - 1).Take(count).ToList();

                var s_list = GameApplication.SceneManager.GetRoomSpecList(index, count);
                var rsp = new PicaRoomSceneListData
                {
                    StartIndex = index,
                    Total = s_list.Count()
                };
                //s_list.ForEach(s => rsp.Items.Add(s));
                rsp.Items.AddRange(s_list);
                SendGoogleProto(ClientMethod.ShowRoomList, rsp, rsp.Items.Count > 2 ? CompressType.GZip : CompressType.None);
            }
            else
            {
                var scenes = new List<PicaRoomSceneSpec>();
                bool inited = false;
                var player_count = count / 3;
                //这里没想好要怎么能分层，先这样写吧
                if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.好友) != null)
                {
                    Character.RelationAgent.Relations.Where(r => r.Group == RelationGroup.Friend).Skip(index == 0 ? 0 : index - 1).Take(player_count).ForEach(r =>
                    {
                        scenes.AddRange(r.TargetRoomSpecs());
                    });
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.关注) != null)
                {
                    Character.RelationAgent.Relations.Where(r => r.Group == RelationGroup.Attention || r.Group == RelationGroup.Friend).Skip(index == 0 ? 0 : index - 1).Take(player_count).ForEach(r =>
                    {
                        scenes.AddRange(r.TargetRoomSpecs());
                    });
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.收藏) != null)
                {
                    foreach (var room in Character.ListDatas.FavouriteRooms)
                    {
                        if (room is PicaRoomSceneSpec prs)
                        {
                            scenes.Add(prs);
                        }
                    }
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.最近) != null)
                {
                    foreach (var room in Character.ListDatas.RecentEnterRooms)
                    {
                        scenes.Add(room);
                    }
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.特殊) != null)
                {
                    foreach (var room in GameApplication.SceneManager.TagedRoomCache)
                    {
                        if (room is PicaRoomSceneSpec rs) scenes.Add(rs);
                    }
                    inited = true;
                }


                if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称) != null)
                {
                    if (GameApplication.SilenceManager.CheckSilenceSetting(SilenceCategory.Search))
                    {
                        ShowErrorTipsMessage(PicaLocalityStrings.search_name_close);
                        return;
                    }
                    var name = req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称).Value;
                    //输入的名字如果大于玩家取名长度上限 判定为输入的是id
                    if (name.Length > GameConfig.BaseConfig.MaxCharacterNameLength && long.TryParse(name, out var owner_id))
                    {
                        scenes = (inited ? scenes.Where(s => s.OwnerID == owner_id) : GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(spec => spec.OwnerID == owner_id)).ToList();
                    }
                    else
                    {
                        scenes = (inited ? scenes.Where(s => s.Name.Contains(name)) : GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(spec => (spec.Name == "" ? spec.OwnerName + "的房间" : spec.Name).Contains(name))).ToList();
                    }
                }
                var rsp = new PicaRoomSceneListData
                {
                    StartIndex = index,
                    Total = scenes.Count
                };
                scenes.ForEach(s => rsp.Items.Add(s.ProtoData));
                SendGoogleProto(ClientMethod.ShowRoomList, rsp, rsp.Items.Count > 2 ? CompressType.GZip : CompressType.None);
            }

        }

        /// <summary>
        /// 获取garden的列表
        /// </summary>
        /// <param name="req"></param>
        public void GetGardenList(SceneListRequestData req)
        {
            var index = req.StartIndex;
            var count = req.Count;
            if (index < 0 || count <= 0)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            if (req.Conditions == null || req.Conditions.Count == 0)
            {
                var s_list = GameApplication.SceneManager.GetGardenSpecList(index, count);
                var rsp = new PicaRoomSceneListData
                {
                    StartIndex = index,
                    Total = s_list.Count()
                };
                //s_list.ForEach(s => rsp.Items.Add(s));
                rsp.Items.AddRange(s_list);
                SendGoogleProto(ClientMethod.ShowGardenList, rsp);
            }
            else
            {
                var scenes = new List<PicaGardenSceneSpec>();
                bool inited = false;
                var player_count = count / 3;
                //这里没想好要怎么能分层，先这样写吧
                if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.好友) != null)
                {
                    Character.RelationAgent.Relations.Where(r => r.Group == RelationGroup.Friend).Skip(index == 0 ? 0 : index - 1).Take(player_count).ForEach(r =>
                    {
                        scenes.AddRange(r.TargetGardenSpecs());
                    });
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.关注) != null)
                {
                    Character.RelationAgent.Relations.Where(r => r.Group == RelationGroup.Attention || r.Group == RelationGroup.Friend).Skip(index == 0 ? 0 : index - 1).Take(player_count).ForEach(r =>
                    {
                        scenes.AddRange(r.TargetGardenSpecs());
                    });
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.收藏) != null)
                {
                    foreach (var room in Character.ListDatas.FavouriteRooms)
                    {
                        if (room is PicaGardenSceneSpec pgs)
                        {
                            scenes.Add(pgs);
                        }
                    }
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.最近) != null)
                {
                    foreach (var garden in Character.ListDatas.RecentEnterGardens)
                    {
                        scenes.Add(garden);
                    }
                    inited = true;
                }
                else if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.特殊) != null)
                {
                    foreach (var room in GameApplication.SceneManager.TagedRoomCache)
                    {
                        if(room is PicaGardenSceneSpec gs) scenes.Add(gs);
                    }
                    inited = true;
                }

                if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称) != null)
                {
                    if (GameApplication.SilenceManager.CheckSilenceSetting(SilenceCategory.Search))
                    {
                        ShowErrorTipsMessage(PicaLocalityStrings.search_name_close);
                        return;
                    }
                    var name = req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称).Value;
                    //scenes = (inited ? scenes : GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(spec => (spec.Name == "" ? spec.OwnerName + "的房间" : spec.Name).Contains(name)).ToList());
                    //输入的名字如果大于玩家取名长度上限 判定为输入的是id
                    if (name.Length > GameConfig.BaseConfig.MaxCharacterNameLength && long.TryParse(name, out var owner_id))
                    {
                        scenes = (inited ? scenes.Where(s => s.OwnerID == owner_id) : GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(spec => spec.OwnerID == owner_id)).ToList();
                    }
                    else
                    {
                        scenes = (inited ? scenes.Where(s => s.Name.Contains(name)) : GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(spec => (spec.Name == "" ? spec.OwnerName + "的庭舍" : spec.Name).Contains(name))).ToList();
                    }
                }
                var rsp = new PicaRoomSceneListData
                {
                    StartIndex = index,
                    Total = scenes.Count
                };
                scenes.ForEach(s => rsp.Items.Add(s.ProtoData));
                SendGoogleProto(ClientMethod.ShowGardenList, rsp);
            }

        }

        public void GetOrganizationSceneList(SceneListRequestData req)
        {
            var index = req.StartIndex;
            var count = req.Count;
            if (index < 0 || count <= 0)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            var rsp = new PicaOrganizationSceneListData
            {
                StartIndex = index,
            };
            if (req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称) != null)
            {
                var name = req.Conditions.FirstOrDefault(c => c.ConditionName == RoomSpecCondition.名称).Value;
                var scenes = GameApplication.SceneManager.FindAllSpec<OrganizationSceneSpec>(spec => spec.Name.Contains(name)).ToList();
                rsp.Total = scenes.Count();
                rsp.Items.AddRange(scenes.Select(s => s.ProtoData));
            }
            else
            {
                var s_list = GameApplication.SceneManager.GetOrganizationSpecList(index, count);
                //s_list.ForEach(s => rsp.Items.Add(s));
                rsp.Total = s_list.Count();
                rsp.Items.AddRange(s_list);
            }
            SendGoogleProto(ClientMethod.ShowOrganizationSceneList, rsp);
        }

        /// <summary>
        /// 进入玩家最多的房间
        /// </summary>
        /// <returns>RedirectScene <see cref="Proto.GameModel.SceneRedirectData"/></returns>
        public void EnterMostManRoom()
        {
            var scenes = GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(spec => IsAvailable(spec) && spec.RoomLevel > 0 && !spec.isGarden)
               .OrderByDescending(spec => spec.PlayerCount).FirstOrDefault();
            if (scenes == null)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            TryEnterSceneRequest ter = new TryEnterSceneRequest();
            ter.SceneID = scenes.Scene.ID;
            ter.AreaID = 0;
            TryEnterScene(ter);
        }

        /// <summary>
        /// 获取玩家自己拥有的房间列表
        /// </summary>
        /// <returns>ShowRoomList <see cref="Proto.GameModel.PicaRoomSceneListData"/></returns>
        public void GetMyRoomList()
        {
            var rsp = new PicaRoomSceneListData();
            lock (Character.MyRoomSpecs)
            {
                var scenes = Character.MyRoomSpecs;
                for (int i = 0; i < scenes.Count; i++)
                {
                    rsp.Items.Add(scenes[i].ProtoData);
                    if (Character.DefaultSceneID == scenes[i].ID)
                    {
                        rsp.Items[i].IsDefault = true;
                    }
                    //if (scenes[i].ProtoData.BaseSceneData.Id == Character.DefaultGardenSceneID)
                    //{
                    //    rsp.Items[0].Type = DimensionType.PicaGarden;
                    //}
                    //else
                    //{
                    //    rsp.Items[0].Type = DimensionType.PicaRoom;
                    //}
                }
                rsp.Total = scenes.Count;
            }

            SendGoogleProto(ClientMethod.ShowMyRoomList, rsp);
        }

        public void FindMySceneObject(IDMessage idm)
        {
            if (!Constants.EnableSceneObjectSearch)
            {
                Character.ShowMessage("服务暂时不可用");
                return;
            }

            var lastTime = Character.LastSearchTime;
            if (lastTime.AddSeconds(5) > DateTimeExtension.Now)
            {
                Character.ShowMessage("请不要操作太频繁...");
                return;
            }

            Character.LastSearchTime = DateTimeExtension.Now;

            (var suc, var msg) = Character.FindMySceneObject(idm.Id);
            if (!suc)
            {
                Character.ShowError(new PicaLocalityString() { DefaultString = msg });
                return;
            }

            Character.ShowMessage(msg);
        }

        public void FindMySceneObjectV2(IDMessage idm)
        {
            if (!Constants.EnableSceneObjectSearch)
            {
                Character.ShowMessage("服务暂时不可用");
                return;
            }

            var lastTime = Character.LastSearchTime;
            if (lastTime.AddSeconds(5) > DateTimeExtension.Now)
            {
                Character.ShowMessage("请不要操作太频繁...");
                return;
            }

            Character.LastSearchTime = DateTimeExtension.Now;

            (var suc, var msg) = Character.FindMySceneObjectV2(idm.Id);
            if (!suc)
            {
                Character.ShowError(new PicaLocalityString() { DefaultString = msg });
                return;
            }
        }

        /// <summary>
        /// 获取玩家自己的庭舍列表
        /// </summary>
        /// <returns>ShowMyGardenList <see cref="Proto.GameModel.PicaRoomSceneListData"/></returns>
        public void GetMyGardenList()
        {
            var rsp = new PicaRoomSceneListData();
            lock (Character.MyGardenSpecs)
            {
                var scenes = Character.MyGardenSpecs;
                rsp.Total = scenes.Count;

                for (int i = 0; i < scenes.Count; i++)
                {
                    rsp.Items.Add(scenes[i].ProtoData);
                    if (Character.DefaultGardenSceneID == scenes[i].ID)
                    {
                        rsp.Items[i].IsDefault = true;
                    }
                }
            }
            SendGoogleProto(ClientMethod.ShowMyGardenList, rsp);
        }

        /// <summary>
        /// 获取指定玩家的房间列表
        /// </summary>
        /// <param name="message"></param>
        /// <returns>ShowRoomList <see cref="Proto.GameModel.PicaRoomSceneListData"/></returns>
        public void GetTargetCharacterRoomList(IDMessage message)
        {
            var scenes = GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(
                spec => IsAvailable(spec) && spec.OwnerID == message.Id
                ).ToList();

            var res = new TargetCharacterRoomList();
            res.OwnerId = message.Id;
            var rsp = new PicaRoomSceneListData
            {
                Total = scenes.Count
            };
            for (int i = 0; i < scenes.Count; i++)
            {
                rsp.Items.Insert(0, scenes[i].ProtoData);
                if (Character.DefaultSceneID == scenes[i].ID)
                {
                    rsp.Items[0].IsDefault = true;
                }
            }
            res.RoomList = rsp;
            SendGoogleProto(ClientMethod.ShowTargetCharacterRoomList, res);
        }

        /// <summary>
        /// 获取指定玩家的庭舍列表
        /// </summary>
        /// <param name="message"></param>
        /// <returns>ShowGardenList <see cref="Proto.GameModel.PicaRoomSceneListData"/></returns>
        public void GetTargetCharacterGardenList(IDMessage message)
        {
            var scenes = GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(
                spec => IsAvailable(spec) && spec.OwnerID == message.Id
                ).ToList();

            var res = new TargetCharacterRoomList();
            res.OwnerId = message.Id;
            var rsp = new PicaRoomSceneListData
            {
                Total = scenes.Count
            };
            for (int i = 0; i < scenes.Count; i++)
            {
                rsp.Items.Insert(0, scenes[i].ProtoData);
                if (Character.DefaultSceneID == scenes[i].ID)
                {
                    rsp.Items[0].IsDefault = true;
                }
            }
            res.RoomList = rsp;
            SendGoogleProto(ClientMethod.ShowTargetCharacterRoomList, res);
        }

        /// <summary>
        /// 回家
        /// </summary>
        /// <returns>RedirectScene <see cref="Proto.GameModel.SceneRedirectData"/></returns>
        public void GoHome()
        {
            Character.GoHome();
        }

        public void BackToGarden()
        {
            Character.GoGarden();
        }

        /// <summary>
        /// 设置出生点偏好
        /// </summary>
        /// <param name="msg">出生点类型消息</param>
        public void SetSpawnPointPreference(IntMessage msg)
        {
            // 验证枚举值有效性
            if (!Enum.IsDefined(typeof(SpawnPointSetting), msg.Value))
            {
                return;
            }
            var value = (SpawnPointSetting)msg.Value;

            Character.Setting.SpawnPointPreference = value;
            Character.Save();

            SendSpawnPointPreference(true);
        }

        /// <summary>
        /// 获取当前出生点偏好设置
        /// </summary>
        public void GetSpawnPointPreference()
        {
            SendSpawnPointPreference(false);
        }

        private void SendSpawnPointPreference(bool update)
        {
            SendGoogleProto(ClientMethod.ShowSpawnPointPreference, new Int32BoolMessage() { Item1 = (int)Character.Setting.SpawnPointPreference, Item2 = update });
        }

        static bool IsInherit(object obj, Type t)
        {
            var type = obj.GetType();
            var baseType = type.BaseType;
            while (baseType != null && baseType.IsGenericType)
            {
                var definitionType = baseType.GetGenericTypeDefinition();
                if (definitionType == t) return true;

                baseType = baseType.BaseType;
            }

            return false;
        }

        /// <summary>
        /// 进入特定的矿场(已经废弃？）
        /// </summary>
        /// <param name="req"></param>
        /// <returns>RedirectScene <see cref="Proto.GameModel.SceneRedirectData"/></returns>
        public void EnterPicaMineScene(IDStringsMessage req)
        {
            if (req.Values.Count < 2)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            // get from sceneType or get from req?
            // dimension.
            string gameId = null;
            string mask = req.Values[0];
            string password = req.Values[1];
            var character = Character.SceneCharacter;

            var dimension = GameApplication.SceneManager.FirstDimension<PicaMineDimension>(p => p.Type == DimensionType.PicaMine);

            // p.Value.SceneType.Level == mask ???
            var level = dimension.Main; // dimension.Levels.FirstOrDefault(p => p.Value.SceneType.Sn == gameId).Value;

            if (level == null)
            {
                User.SendGoogleProto(ClientMethod.EnterSceneFail);
                Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID}, GameId {gameId}, enter scene fail. level is null.");
                return;
            }
            gameId = level.SceneType.Sn;

            var scene = dimension.GetAvailableScene(character, level);

            if (scene == null)
            {
                User.SendGoogleProto(ClientMethod.EnterSceneFail);
                Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID} enter scene fail. scene is null. 4");
                return;
            }


            if (!scene.CanEnter(Character, password))
            {
                return;
            }


            if (!scene.Enter(character))
            {
                User.SendGoogleProto(ClientMethod.EnterSceneFail);
                Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID} enter scene fail. 3");
                return;
            }

        }

        /// <summary>
        /// 进入指定ID的公共场景
        /// </summary>
        /// <param name="req"></param>
        /// <returns>RedirectScene <see cref="Proto.GameModel.SceneRedirectData"/></returns>
        public void EnterPicaPublicScene(IDListMessage req)
        {
            // 0: SceneTypeId
            //1: DimensionLevel, 比如矿洞的层数,或者农场分场景的
            if (req.Items.Count < 2)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }

            Character.EnterPicaPublicScene((DimensionType)req.Items[0], req.Items[1]);

            //var character = Character.SceneCharacter;

            //var dimension = GameApplication.SceneManager.GetDimensionByType((DimensionType)req.Items[0]);
            //if (dimension == null)
            //{
            //    ShowError(LocalityStrings.argumentError);
            //    return;
            //}

            //var level = req.Items[1] == -1 ? dimension.Main : dimension.GetLevel(req.Items[1]);

            //if (level == null)
            //{
            //    User.SendGoogleProto(ClientMethod.EnterSceneFail);
            //    Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID}, enter scene fail. level is null.");
            //    return;
            //}


            //var scene = dimension.GetAvailableScene(character, level);

            //if (scene == null)
            //{
            //    User.SendGoogleProto(ClientMethod.EnterSceneFail);
            //    Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID} enter scene fail. scene is null. 4");
            //    return;
            //}

            //if (!scene.CanEnter(Character, null, out LocalityString error))
            //{
            //    ShowError(error);
            //    return;
            //}

            //if (!scene.Enter(character))
            //{
            //    User.SendGoogleProto(ClientMethod.EnterSceneFail);
            //    Logger.GameError.Write($"{nameof(EnterPicaMineScene)} Character.ID:{Character.ID} enter scene fail. 3");
            //    return;
            //}

        }

        /// <summary>
        /// 进入场景
        /// </summary>
        /// <param name="req"></param>
        /// <returns>RedirectScene <see cref="Proto.GameModel.SceneRedirectData"/></returns>
        public void TryEnterScene(TryEnterSceneRequest req)
        {
            long areaID = 0;
            Scene scene = null;
            Transform transform = null;
            var character = Character.SceneCharacter;

            if (req.SceneID == 0)
            {
                var guide = Character.GuideStep(GuideID.新手引导);
                var petGuide = Character.GuideStep(GuideID.宠物引导);
                var NoviceMineGuide = Character.GuideStep(GuideID.矿洞引导);
                var NovicePastureGuide = Character.GuideStep(GuideID.牧场引导);
                var FishingGuide = Character.GuideStep(GuideID.钓鱼引导);
                var ServantGuide = Character.GuideStep(GuideID.服务生引导);
                var CookGuide = Character.GuideStep(GuideID.厨师引导);
                var EngineerGuide = Character.GuideStep(GuideID.建筑师引导);
                var StylistGuide = Character.GuideStep(GuideID.造型师引导);
                var mapGuide = Character.GuideStep(GuideID.地图引导);

                // 注意这个要跟 Scene.CanEnter 里面的检查顺序一致，否则会出现无法登录的情况
                if (NoviceMineGuide > 1 && NoviceMineGuide < Constants.GotoNoviceMineLevel3)
                {
                    // 回到新手矿洞
                    if (NoviceMineGuide >= 2 && NoviceMineGuide < Constants.GotoNoviceMineLevel1)
                    {
                        var room = Character.PicaMineGetNoviceRoom(2007);
                        room.Enter(character);
                    }
                    else if (NoviceMineGuide >= Constants.GotoNoviceMineLevel1 && NoviceMineGuide < Constants.GotoNoviceMineLevel2)
                    {
                        var room = Character.PicaMineGetNoviceRoom(2008);
                        room.Enter(character);
                    }
                    else if (NoviceMineGuide >= Constants.GotoNoviceMineLevel2 && NoviceMineGuide < Constants.GotoNoviceMineLevel3)
                    {
                        var room = Character.PicaMineGetNoviceRoom(2009);
                        room.Enter(character);
                    }
                    return;
                }

                else if (petGuide > Constants.MaxPetGuideSteps && NovicePastureGuide == 0)
                {
                    // 宠物引导结束，牧场引导未开始
                    // 此时是在未进入牧场的时候中断了
                    ToNovicePasture(Character.SceneCharacter);
                    return;
                }

                else if (FishingGuide > 0 && FishingGuide < Constants.MaxFishGuideSteps)
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaFishing, 2015);
                    return;
                }

                else if (Character.ForceGoHome()) // 继续引导
                {
                    GoHome();
                    return;
                }
                //else if (guide < Constants.PlaneGuideOver)//第一次进游戏
                //{
                //    var planeDimension = GameApplication.SceneManager.FirstDimension(p => p.Type == DimensionType.PicaAirplane);
                //    scene = planeDimension.GetAvailableScene(character, planeDimension.Main);
                //}
                //else if (Character.ForceGoSquare())
                //{
                //    Character.EnterPicaPublicScene(DimensionType.PicaPublic, 2001);
                //    return;
                //}
                //else if (guide == Constants.GuideEnterLobbyStep)
                //{
                //    Character.EnterPicaPublicScene(DimensionType.PicaLobby, 2002);
                //    return;
                //}
                else if (Character.ForceToIWSGarden())
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaIWSGarden, 3001);
                    return;
                }
                else if (Character.ForceToNoviceFarm())
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaNoviceFarm, 3002);
                    return;
                }
                //else if (Character.ForceToFarm())
                //{
                //    Character.EnterPicaPublicScene(DimensionType.PicaFarm, 1002);
                //    return;
                //}
                else if (Character.ForceToGarden())
                {
                    // 去庭舍
                    BackToGarden();
                    return;
                }
                //else if (gardenMissionGuide >= Constants.GoToIWSgarden && gardenMissionGuide < Constants.LeaveIWSgarden)
                //{
                //    Character.EnterPicaPublicScene(DimensionType.PicaIWSGarden, 22002);
                //    return;
                //}

                else if (Character.ForceToNovicePasture())
                {
                    ToNovicePasture(Character.SceneCharacter);
                    return;
                }
                else if (ServantGuide > 0 && ServantGuide <= Constants.ServantGuideDone)
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaWorkGuide, 210);
                    return;
                }
                else if (StylistGuide > 0 && StylistGuide <= Constants.StylistGuideDone)
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaWorkGuide, 220);
                    return;
                }
                else if (EngineerGuide > 0 && EngineerGuide <= Constants.EngineerGuideDone)
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaWorkGuide, 230);
                    return;
                }
                else if (CookGuide > 0 && CookGuide <= Constants.CookGuideDone)
                {
                    Character.EnterPicaPublicScene(DimensionType.PicaWorkGuide, 240);
                    return;
                }
                else if (character.CurrentMineLevel > 0)
                {
                    // 传送到上次的矿洞
                    //Character.MineArrowNext = true;
                    //var mineDimension = GameApplication.SceneManager.FirstDimension(p => p.Type == DimensionType.PicaMine);
                    //Character.EnterPicaPublicScene(DimensionType.PicaMine, character.CurrentMineLevel);
                    Character.TryEnterMineRoom(character.CurrentMineLevel, true);
                    return;
                }
                else if (Character.TryReconnectCooperateGame())
                {
                    return;
                }
                //else if (Character.LastMineLevel > 0)
                //{
                //    // 上次在矿洞异世界内, 传回上次的矿洞
                //    var mineLevel = Character.LastMineLevel;
                //    Character.LastMineLevel = 0;
                //    Character.Save();
                //    var mineDimension = GameApplication.SceneManager.FirstDimension(p => p.Type == DimensionType.PicaMine);
                //    Character.EnterPicaPublicScene(DimensionType.PicaMine, mineLevel);
                //    return;
                //}
                else if (!character.ReloginDenied && character.CurrentDimension is not null && character.CurrentDimension.TryEnterAfterRelogin(character))
                {
                    //次元重连
                    return;
                }
                else if (Scene.ShouldGoToSnowScene(Character))
                {
                    var snowDimension = GameApplication.SceneManager.GetDimensionByType(DimensionType.PicaSnow);
                    scene = snowDimension.GetAvailableScene(User.Character.SceneCharacter, snowDimension.Main);
                }
                else if (Character.IsAttach())
                {
                    // 附身状态下回到自己小屋
                    GoHome();
                    return;
                }
                else
                {
                    // 根据玩家出生点偏好设置进入游戏
                    switch (Character.Setting.SpawnPointPreference)
                    {
                        case SpawnPointSetting.Home:
                            // 小屋（玩家主屋）
                            GoHome();
                            return;
                        
                        case SpawnPointSetting.Garden:
                            // 庭舍（主庭舍）
                            BackToGarden();
                            return;
                        
                        case SpawnPointSetting.TownHall:
                        default:
                            // 小镇政务厅（默认出生点）
                            Character.EnterPicaPublicScene(DimensionType.PicaLobby, -1);
                            return;
                    }
                }

                ////后端引导进入场景
                //if (character.CurrentSceneID == 0)
                //{
                //    //首次进入游戏
                //    var dimension = GameApplication.SceneManager.FirstDimension(p => p.Type == DimensionType.PicaLobby);
                //    scene = dimension.GetAvailableScene(character, dimension.Main);
                //}
                //else
                //{
                //    var current = character.CurrentArea;
                //    if (current != null && current.Scene.CanEnter(Character, req.Password, out _))
                //    {
                //        //还原到下线前的位置
                //        scene = current.Scene;
                //        areaID = current.ID;
                //        transform = character.Transform;
                //    }
                //    else if (Character.TownshipSceneID != 0)
                //    {
                //        //到小镇
                //        areaID = Character.TownshipAreaID;
                //        scene = GameApplication.SceneManager.GetScene(Character.TownshipSceneID, true);
                //    }
                //    else if (Character.DefaultSceneID != 0)
                //    {
                //        //到小屋
                //        scene = GameApplication.SceneManager.GetScene(Character.DefaultSceneID, true);
                //    }
                //    else if (Character.BeginnerSceneID != 0)
                //    {
                //        //到新手土地
                //        areaID = Character.BeginnerAreaID;
                //        scene = GameApplication.SceneManager.GetScene(Character.BeginnerSceneID, true);
                //    }
                //    else
                //    {
                //        User.SendGoogleProto(ClientMethod.EnterSceneFail);
                //        Logger.GameError.Write($"{nameof(TryEnterScene)} Character.ID:{Character.ID} enter scene fail. scene is null. 1");
                //        return;
                //    }
                //}
                if (scene == null)
                {
                    User.SendGoogleProto(ClientMethod.EnterSceneFail);
                    Logger.GameError.Write($"{nameof(TryEnterScene)} Character.ID:{Character.ID} enter scene fail. scene is null. 2");
                    return;
                }
            }
            else
            {
                scene = GameApplication.SceneManager.GetScene(req.SceneID, true);
                if (scene is ICanBeObservedScene && req.Observe || character.TestNextObserverMode)
                {
                    character.Observer = true;

                    Character.UpdateToSceneService(p => p.Observer = character.Observer);
                }
                if (scene == null)
                {
                    ShowError(LocalityStrings.targetSceneNotFound);
                    return;
                }
                if (!scene.CanEnter(Character, req.Password, true))
                {
                    //ShowError(error);
                    return;
                }

                areaID = req.AreaID;
            }
            if (!scene.Enter(character, areaID, transform?.ProtoData, attemptPrivileged: true))
            {
                User.SendGoogleProto(ClientMethod.EnterSceneFail);
                Logger.GameError.Write($"{nameof(TryEnterScene)} Character.ID:{Character.ID} enter scene fail. 3");
                return;
            }
        }

        private void ToNovicePasture(SceneCharacter character)
        {
            var dimension = GameApplication.SceneManager.FirstDimension<PicaNovicePastureDimension>();
            var noviceScene = dimension.GetAvailableScene(character, dimension.GetLevel(2031));
            //var noviceScene = dimension.GetLevel(2031).CreateScene(Character.System);
            noviceScene.Enter(Character.SceneCharacter);
        }

        /// <summary>
        /// 获取场景详细信息
        /// </summary>
        /// <param name="idm"></param>
        /// <returns>ShowSceneDetail <see cref="Proto.GameModel.SceneDetailResponse"/></returns>
        public void GetSceneDetail(IDInt32Message idm)
        {
            var scene = GameApplication.DataManager.FindValue<Scene>(idm.ID);
            if (scene == null)
            {
                return;
            }

            var rsp = new SceneDetailResponse();
            rsp.Group = idm.Value;
            rsp.Data = scene.DetailData;
            if (!Character.RoomData.PraisedRoomIds.Contains(idm.ID))
            {
                rsp.Data.CanPraise = true;
            }
            if (Character.DefaultSceneID == scene.ID)
            {
                rsp.Data.IsDefault = true;
            }

            if (scene.Dimension.Type == DimensionType.PicaRoom || scene.Dimension.Type == DimensionType.PicaGarden)
            {
                ((PicaRoomScene)scene).OnGetSceneDetail(Character.SceneCharacter);
            }

            User.SendGoogleProto(ClientMethod.ShowSceneDetail, rsp);

        }
        public void GetOrganizationSceneDetail(IDInt32Message idm)
        {
            Organization organization = null;
            if (idm.ID == 0)
            {
                organization = Character.OrganizationAgent.Organization;
            }
            else
            {
                organization = GameApplication.OrganizationManager.TryGetOrganization(idm.ID);
            }

            if (organization == null || organization.BuildStatus == OrganizationBuildStatus.创建中)
            {
                Character.ShowErrorTipsMessage("PKT_Organization0004");
                //未找到目标家族
                return;
            }

            Character.SendGoogleProto(ClientMethod.ShowOrganizationSceneDetail, new OrganizationSceneDetailResponse() { Data = organization.FindScene(OrganizationSceneCategory.HolyLand).DetailData, Group = idm.Value });
        }

        public void ChangeOrganizationSceneName(StringMessage message)
        {
            if (Character.OrganizationAgent.OrganizationID == 0)
            {
                //玩家未加入家族
                Character.ShowErrorTipsMessage(I18nManager.I18n("PKT_Organization0008"));
                return;
            }
            if (Character.SceneCharacter.CurrentScene.TypeData.DimensionType != DimensionType.Organization)
            {
                return;
            }
            var scene = (OrganizationScene)Character.SceneCharacter.CurrentScene;
            if (!Character.OrganizationAgent.CheckPosition())
            {
                return;
            }
            var oldname = scene.Name;
            var result = "";
            if (!GameApplication.SceneManager.CheckRoomName(Character, message.Msg, scene.ID, out result))
            {
                return;
            }

            scene.Name = result;
            scene.Spec.Name = result;
            scene.Save();

            Character.GetLeiTingTrackWriter("change_name")
                .WriteEntry("before_name", oldname)
                .WriteEntry("now_name", scene.Name)
                .WriteStringEntry("clean_id", scene.ID)
                .WriteEntry("action_type", "家族场景昵称修改")
                .WriteStringEntry("room_id", scene.ID)
                .WriteToTaLogger();

            Character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000084, I18nManager.I18n("PKT_NKW0000030"));
            scene.UpdateToSceneService(p => p.Name = scene.Name);
            Character.SendGoogleProto(ClientMethod.ShowOrganizationSceneDetail, new OrganizationSceneDetailResponse() { Data = scene.DetailData });
        }

        public void ChangeOrganizationScenePower(Int32x2Message message)
        {
            if (Character.OrganizationAgent.OrganizationID == 0)
            {
                //玩家未加入家族
                Character.ShowErrorTipsMessage(I18nManager.I18n("PKT_Organization0008"));
                return;
            }
            if (Character.SceneCharacter.CurrentScene.TypeData.DimensionType != DimensionType.Organization)
            {
                return;
            }
            var scene = (OrganizationScene)Character.SceneCharacter.CurrentScene;
            if (!Character.OrganizationAgent.CheckPosition())
            {
                return;
            }
            scene.EnterPower = (OrganizationScenePower)message.Item1;
            scene.Spec.EnterPower = scene.EnterPower;
            scene.FlashPower = (OrganizationScenePower)message.Item2;
            scene.Save();
            Character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000084, "设置权限");
            Character.SendGoogleProto(ClientMethod.ShowOrganizationSceneDetail, new OrganizationSceneDetailResponse() { Data = scene.DetailData });

            var ranking = GameApplication.RankingManager.GetRanking<OrganizationSceneSpecRanking>();
            using (ranking.EnterWriterLock())
            {
                ranking.Update(scene.Spec);
            }
        }

        public void SceneNavigationBack()
        {
            var character = Character.SceneCharacter;
            character.SceneNavigation?.Enter(character);
        }

        /// <summary>
        /// 查找场景
        /// </summary>
        /// <param name="req"></param>
        /// <returns>ShowSceneList <see cref="Proto.GameModel.SceneRankingResponse"/></returns>
        public void SearchScene(SearchSceneRequestData req)
        {
            if (req.Count > 200)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }

            var rsp = new SceneRankingResponse();
            rsp.Index = req.StartIndex;
            switch (req.Type)
            {
                case SearchSceneType.房间ID:
                    if (!long.TryParse(req.KeyWords, out long id))
                    {
                        ShowError(LocalityStrings.GetUndefinedString("无效的搜索请求！"));
                        return;
                    }
                    var s = GameApplication.SceneManager.FindSpec<PersonalSceneSpec>(id);
                    if (s != null)
                    {
                        rsp.Items.Add(s.ProtoData);
                    }
                    break;

                case SearchSceneType.房主名:
                    GameApplication.SceneManager.ForEachSpec<PersonalSceneSpec>(p => p.OwnerName.Contains(req.KeyWords), req.Count, (p) => rsp.Items.Add(p.ProtoData));
                    break;

                case SearchSceneType.房间名:
                    GameApplication.SceneManager.ForEachSpec<PersonalSceneSpec>(p => p.Name.Contains(req.KeyWords), req.Count, (p) => rsp.Items.Add(p.ProtoData));
                    break;

                case SearchSceneType.房主ID:
                    if (!long.TryParse(req.KeyWords, out long ownerID))
                    {
                        ShowError(LocalityStrings.GetUndefinedString("无效的搜索请求！"));
                        return;
                    }

                    GameApplication.SceneManager.ForEachSpec<PersonalSceneSpec>(p => p.OwnerID == ownerID, req.Count, (p) => rsp.Items.Add(p.ProtoData));
                    break;

                default:
                    return;
            }

            rsp.Total = (rsp.Items == null) ? 0 : rsp.Items.Count;
            rsp.Total = Math.Min(rsp.Total, 20);
            User.SendGoogleProto(ClientMethod.ShowSceneList, rsp);
        }

        /// <summary>
        /// 进入某个 dimension （没有分线，已经废弃）
        /// </summary>
        /// <param name="req"></param>
        public void EnterDimension(EnterDimensionRequest req)
        {
            var predicate = new Func<Dimension, bool>(p => p.Type == req.Type);
            var dimensions = GameApplication.SceneManager.FindDimension(predicate);
            if (dimensions.Count != 1)
            {
                ShowError(LocalityStrings.operationError);
                return;
            }
            var dimension = dimensions[0];
            var scene = dimension.GetAvailableScene(Character.SceneCharacter, dimension.Main);
            if (!scene.CanEnter(Character, null))
            {
                //ShowError(error);
                return;
            }

            scene.Enter(Character.SceneCharacter);
        }

        private IReadOnlyGameRanking<SceneRankingItem> GetSceneRanking(Scene scene, int type)
        {
            switch (type)
            {
                case 1://热门
                    return GameApplication.RankingManager.GetRanking<HotSceneRanking>(scene);
                case 2://精选
                    return GameApplication.RankingManager.GetRanking<ChoicenessSceneRanking>(scene);
                case 3://收藏
                    return GameApplication.RankingManager.GetRanking<CollectionSceneRanking>(scene);
                default:
                    return null;
            }
        }

        public void GetSceneRanking(SceneRankingRequest req)
        {
            if (req.Type == 0)
            {
                //我的房间
                var scenes = Character.Scenes.Values.OfType<PersonalSceneSpec>().ToList();
                var rsp = new SceneRankingResponse();
                rsp.Index = req.Index;
                rsp.Group = req.Group;
                rsp.Total = scenes.Count;
                foreach (var link in scenes)
                {
                    rsp.Items.Add(link.ProtoData);
                }

                User.SendGoogleProto(ClientMethod.ShowSceneRanking, rsp);
            }
            else
            {
                //排行榜
                var scene = Character.SceneCharacter.CurrentScene;
                var ranking = GetSceneRanking(scene, req.Type);
                if (ranking == null)
                {
                    ShowError(LocalityStrings.argumentError);
                    return;
                }

                var rsp = new SceneRankingResponse();
                rsp.Index = req.Index;
                rsp.Group = req.Group;
                using (ranking.EnterReaderLock())
                {
                    rsp.Total = ranking.Count;
                    foreach (var item in ranking.GetRange(req.Index, req.Count))
                    {
                        rsp.Items.Add(item.ProtoData);
                    }
                }

                User.SendGoogleProto(ClientMethod.ShowSceneRanking, rsp);
            }
        }

        public void GetAreaSceneRanking(AreaSceneRankingRequest req)
        {
            if (req.Type == 0)
            {
                //自己房间
                var rsp = new AreaSceneRankingResponse();
                rsp.SceneID = req.SceneID;
                rsp.AreaX = req.AreaX;
                rsp.AreaY = req.AreaY;
                var location = new Location(req.SceneID, req.AreaX, req.AreaY);
                foreach (var link in Character.Scenes.Values.OfType<PersonalSceneSpec>().Where(p => p.EntranceLocation == location))
                {
                    var item = new AreaSceneData();
                    item.SceneID = link.ID;
                    item.SceneName = link.Name;
                    item.PlayerCount = link.PlayerCount;
                    rsp.Items.Add(item);
                }

                User.SendGoogleProto(ClientMethod.ShowAreaSceneRanking, rsp);
            }
            else
            {
                //排行榜
                var scene = Character.SceneCharacter.CurrentScene;
                var ranking = GetSceneRanking(scene, req.Type);
                if (ranking == null)
                {
                    ShowError(LocalityStrings.argumentError);
                    return;
                }

                var rsp = new AreaSceneRankingResponse();
                rsp.SceneID = req.SceneID;
                rsp.AreaX = req.AreaX;
                rsp.AreaY = req.AreaY;
                var location = new Location(req.SceneID, req.AreaX, req.AreaY);
                using (ranking.EnterReaderLock())
                {
                    foreach (var item in ranking.FindAll(p => p.Scene.EntranceLocation == location))
                    {
                        rsp.Items.Add(item.SceneData);
                    }
                }

                User.SendGoogleProto(ClientMethod.ShowAreaSceneRanking, rsp);
            }
        }

        public void GetFavoriteSceneList(SceneListRequestData req)
        {
            if (req.Count < 1 || req.StartIndex < 0)
            {
                return;
            }
            var scenes = Character.FavoriteScenes
                .Find(p => p.SceneSpec != null)
                .OrderByDescending(p => p.SceneSpec.PlayerCount)
                .ThenByDescending(p => p.SceneSpec.VisitTimes)
                .ToList();
            if (req.StartIndex >= scenes.Count)
            {
                req.StartIndex = 0;
            }

            var rsp = new SceneRankingResponse();
            int endIndex = Math.Min(req.StartIndex + req.Count, scenes.Count);
            for (int i = req.StartIndex; i < endIndex; i++)
            {
                rsp.Items.Add(scenes[i].SceneSpec.ProtoData);
            }

            rsp.Index = req.StartIndex;
            rsp.Total = scenes.Count;
            rsp.Group = req.Group;
            User.SendGoogleProto(ClientMethod.ShowSceneList, rsp);
        }

        public void GetRecentSceneList(SceneListRequestData req)
        {
            if (req.Count < 1 || req.StartIndex < 0)
            {
                return;
            }
            var scenes = Character.RecentScenes;
            if (req.StartIndex >= scenes.Count)
            {
                req.StartIndex = 0;
            }

            var rsp = new SceneRankingResponse();
            int endIndex = Math.Min(req.StartIndex + req.Count, scenes.Count);
            for (int i = req.StartIndex; i < endIndex; i++)
            {
                var s = scenes.ElementAt(i);
                if (s.SceneSpec != null)
                {
                    rsp.Items.Add(s.SceneSpec.ProtoData);
                }
            }
            rsp.Index = req.StartIndex;
            rsp.Total = scenes.Count;
            rsp.Group = req.Group;
            User.SendGoogleProto(ClientMethod.ShowSceneList, rsp);
        }

        public void AddFavoriteScene(IDMessage req)
        {
            if (Character.FavoriteScenes.Count >= Character.MemberSet.MaxFavoriteSceneCount)
            {
                User.ShowError(LocalityStrings.maximumAmountLimit3);
                return;
            }
            var ss = GameApplication.SceneManager.FindSpec<PersonalSceneSpec>(req.Id);
            if (ss == null)
            {
                ShowIDNotFoundError(req.Id);
                return;
            }
            var os = Character.FavoriteScenes.First(p => p.SceneID == req.Id);
            if (os != null)
            {
                ShowInvalidActionError("该房间已经收藏过了");
                return;
            }

            Character.AddFavoriteScene(ss);

            var rsp = new IDMessage();
            rsp.Id = ss.ID;
            User.SendGoogleProto(ClientMethod.AddFavoriteScene, rsp);
        }

        public void DeleteFavoriteScene(IDMessage im)
        {
            var fs = Character.FavoriteScenes.First(p => p.SceneID == im.Id);
            if (fs == null)
            {
                ShowIDNotFoundError(im.Id);
                return;
            }

            fs.Delete();
            Character.FavoriteScenes.Remove(fs.ID);

            var rsp = new IDMessage();
            rsp.Id = fs.ID;
            User.SendGoogleProto(ClientMethod.DeleteFavoriteScene, rsp);
        }

        /// <summary>
        /// （已经废弃）
        /// </summary>
        /// <param name="str"></param>
        public void SceneTransport(SceneTransportRequest str)
        {
            if (Character.SceneCharacter.RoleChanged)
            {
                ShowError(LocalityStrings.limitedTransport);
                return;
            }
            var sot = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(str.TransportObjectType);
            if (sot == null)
            {
                ShowIDNotFoundError(str.TransportObjectType);
                return;
            }
            var transport = sot.GetFunction<SceneTransport>();
            if (transport == null)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            var scene = GameApplication.DataManager.FindValue<Scene>(transport.SceneID);
            if (scene == null)
            {
                ShowError(LocalityStrings.unknownError);
                return;
            }
            var oc = Character.SceneCharacter.CurrentArea.Coordinate;
            var nc = str.TargetCoordinate.ToIntVector2();
            if (!oc.IsNearBy(nc))
            {
                ShowError(LocalityStrings.unknownError);
                return;
            }

            var area = scene.FindArea(nc, true);
            if (area == null)
            {
                ShowError(LocalityStrings.unknownError);
                return;
            }

            //var ear = new EnterAreaRequest();
            //ear.NewArea = area.Coordinate.ToProto();

            //ear.Mode = EnterAreaMode.CommonMode;
            //ear.Transform = str.Transform;
            //var around = scene.GetAroundAreas(area, 2, true);
            //ear.AroundAreas.AddRange(around.Values.Select(p => p.Coordinate.ToProto()));

            //var saear = new SceneActorEnterAreaRequest();
            //saear.SceneID = scene.ID;
            //saear.ActorID = Character.SceneCharacter.ID;
            //saear.EnterAreaRequest = ear;

            //scene.SendToSceneService(SceneManagerClientMethod.TransportActorToArea, saear);

            scene.Enter(Character.SceneCharacter, area.ID, str.Transform, null);
        }

        public void DoSceneInteraction(StringBytesMessage req)
        {
            var scene = Character.SceneCharacter.CurrentScene;
            var function = scene.TypeData.GetFunction(req.Value);
            if (function is not ISceneInteraction interaction)
            {
                ShowError(LocalityStrings.targetFunctionNotFound);
                return;
            }

            interaction.DoInteraction(Character, scene, req.Bytes);
        }

        public void EditScene(EditSceneRequest req)
        {
            var scene = Character.SceneCharacter.CurrentScene;
            if (req.ID != scene.ID)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (scene.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            if (req.HasName)
            {
                if (!GameApplication.UserManager.CheckNicknameAvailable(req.Name))
                {
                    ShowError(LocalityStrings.nameIllegal);
                    return;
                }

                scene.Name = req.Name;
                scene.Save();
                scene.UpdateNameToSceneService();
                ShowTipsMessage(LocalityStrings.changeSuccess);
                return;
            }
            if (req.HasDescription)
            {
                scene.Description = req.Description;
                scene.Save();
                scene.UpdateToSceneService(p => p.Description = scene.Description);
            }
            if (req.HasPartyTitle)
            {
                if (scene is not PersonalScene other)
                {
                    ShowError(LocalityStrings.targetTypeError);
                    return;
                }

                other.PartyTitle = req.PartyTitle;
                other.Save();
                other.UpdateToSceneService(p => p.PartyTitle = other.PartyTitle);
            }

            User.SendGoogleProto(ClientMethod.EditScene, req);
        }

        //private bool IsAvailable(LandSceneSpec scene)
        //{
        //    return scene.OwnerID != 0 &&
        //        (scene.OwnerID == Character.ID || scene.EnterRule < SceneEnterRule.Lock) &&
        //        (scene is HappySceneSpec || (scene is MiniGameSceneSpec ms && ms.GameID != 0));
        //}

        //private static LandSceneRankingItemData SceneConvertToLandRankingItem(LandSceneSpec scene)
        //{
        //    var data = new LandSceneRankingItemData();
        //    data.ID = scene.ID;
        //    data.TargetID = scene.ID;
        //    data.TargetName = scene.Name;
        //    data.Description = scene.Description;
        //    data.EnterRule = scene.EnterRule;
        //    data.OwnerID = scene.OwnerID;
        //    data.OwnerName = scene.OwnerName;
        //    data.PlayerCount = scene.PlayerCount;
        //    data.Avatar = scene.Owner?.SceneCharacter.AvatarData;
        //    data.Url = scene.Url;
        //    data.UrlVersion = scene.UrlVersion;
        //    switch (scene)
        //    {
        //        case HappySceneSpec hss:
        //            data.Praise = hss.Praise;
        //            data.Collection = hss.Collection;
        //            data.SevenDaysVisitCount = hss.SevenDaysVisitCount;
        //            break;
        //        case MiniGameSceneSpec mgss:
        //            data.Praise = mgss.MiniGame.Praise;
        //            data.Collection = mgss.MiniGame.Collection;
        //            data.SevenDaysVisitCount = mgss.MiniGame.SevenDaysVisitCount;
        //            break;
        //    }

        //    return data;
        //}

        //private LandSceneRankingItemData MiniGameConvertToLandRankingItem(MiniGame miniGame)
        //{
        //    var data = new LandSceneRankingItemData();
        //    data.ID = miniGame.ID;
        //    data.TargetID = miniGame.ID;
        //    data.TargetName = miniGame.Name;
        //    data.Description = miniGame.Description;
        //    if (miniGame.OwnerID != 0)
        //    {
        //        data.OwnerID = miniGame.OwnerID;
        //        data.OwnerName = miniGame.Owner.Name;
        //        data.Avatar = miniGame.Owner.SceneCharacter.AvatarData;
        //    }
        //    else
        //    {
        //        data.OwnerName = miniGame.Source.Author;
        //    }

        //    data.Icon = miniGame.Icon;
        //    data.IconBundle = miniGame.IconBundle;
        //    data.PlayerCount = GameApplication.MiniGameManger.GetDimension(miniGame.ID)?.PlayerCount ?? 0;
        //    data.Praise = miniGame.Praise;
        //    data.Collection = miniGame.Collection;
        //    data.SevenDaysVisitCount = miniGame.SevenDaysVisitCount;
        //    return data;
        //}

        //public void GetLandSceneRanking(LandSceneRankingRequest req)
        //{
        //    if (req.Index < 0)
        //    {
        //        ShowError(LocalityStrings.argumentOutOfRangeError);
        //        return;
        //    }
        //    if (req.Count <= 0 || req.Count > 100)
        //    {
        //        ShowError(LocalityStrings.argumentOutOfRangeError);
        //        return;
        //    }

        //    var rsp = new LandSceneRankingResponse();
        //    rsp.Category = req.Category;
        //    rsp.Index = req.Index;
        //    rsp.Group = req.Group;
        //    switch (req.Category)
        //    {
        //        case LandSceneRankingCategory.Hot:
        //            {
        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p)).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p => p.OwnerID == 0 && p.Version != 0 && p.Source.Available);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                var scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //                var ranking = scenes.OrderByDescending(p => p.PlayerCount).ThenBy(p => p.ID);
        //                foreach (var scene in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    switch (scene)
        //                    {
        //                        case LandSceneSpec ss:
        //                            {
        //                                var data = SceneConvertToLandRankingItem(ss);
        //                                data.Category = LandSceneRankingCategory.Space;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                        case MiniGame ms:
        //                            {
        //                                var data = MiniGameConvertToLandRankingItem(ms);
        //                                data.Category = LandSceneRankingCategory.MiniGame;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                    }
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.Praise:
        //            {
        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p)).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p => p.OwnerID == 0 && p.Version != 0 && p.Source.Available);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                var scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //                var ranking = scenes.OrderByDescending(p => p.RankingPraise).ThenBy(p => p.ID);
        //                foreach (var scene in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    switch (scene)
        //                    {
        //                        case LandSceneSpec ss:
        //                            {
        //                                var data = SceneConvertToLandRankingItem(ss);
        //                                data.Category = LandSceneRankingCategory.Space;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                        case MiniGame ms:
        //                            {
        //                                var data = MiniGameConvertToLandRankingItem(ms);
        //                                data.Category = LandSceneRankingCategory.MiniGame;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                    }
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.Collection:
        //            {
        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p)).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p => p.OwnerID == 0 && p.Version != 0 && p.Source.Available);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                var scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //                var ranking = scenes.OrderByDescending(p => p.RankingCollection).ThenBy(p => p.ID);
        //                foreach (var scene in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    switch (scene)
        //                    {
        //                        case LandSceneSpec ss:
        //                            {
        //                                var data = SceneConvertToLandRankingItem(ss);
        //                                data.Category = LandSceneRankingCategory.Space;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                        case MiniGame ms:
        //                            {
        //                                var data = MiniGameConvertToLandRankingItem(ms);
        //                                data.Category = LandSceneRankingCategory.MiniGame;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                    }
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.Space:
        //            {
        //                var scenes = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p)).ToList();
        //                rsp.Total = scenes.Count;
        //                var ranking = scenes
        //                    .OrderByDescending(p => p.PlayerCount)
        //                    .ThenByDescending(p => p.RankingPraise)
        //                    .ThenByDescending(p => p.RankingCollection)
        //                    .ThenBy(p => p.CreateDate);
        //                foreach (LandSceneSpec scene in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    var data = SceneConvertToLandRankingItem(scene);
        //                    data.Category = LandSceneRankingCategory.Space;
        //                    rsp.Items.Add(data);
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.MiniGame:
        //            {
        //                var miniGames = GameApplication.DataManager.FindValue<MiniGame>(p =>
        //                    p.Version != 0 &&
        //                    p.OwnerID == 0 &&
        //                    p.Source.SpaceType == MiniGameSpaceType.Space &&
        //                    p.Source.Available);
        //                rsp.Total = miniGames.Count;
        //                var ranking = miniGames
        //                    .OrderByDescending(p => p.PlayerCount)
        //                    .ThenByDescending(p => p.Praise)
        //                    .ThenByDescending(p => p.Collection)
        //                    .ThenBy(p => p.PublishTime);
        //                foreach (MiniGame miniGame in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    var data = MiniGameConvertToLandRankingItem(miniGame);
        //                    data.Category = LandSceneRankingCategory.MiniGame;
        //                    rsp.Items.Add(data);
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.SystemSpace:
        //            {
        //                var miniGames = GameApplication.DataManager.FindValue<MiniGame>(p =>
        //                    p.OwnerID == 0 &&
        //                    p.Version != 0 &&
        //                    p.Source.SpaceType == MiniGameSpaceType.Space &&
        //                    p.Source.Available);
        //                rsp.Total = miniGames.Count;
        //                var ranking = miniGames
        //                    .OrderByDescending(p => p.PlayerCount)
        //                    .ThenByDescending(p => p.Praise)
        //                    .ThenByDescending(p => p.Collection)
        //                    .ThenBy(p => p.PublishTime);
        //                foreach (MiniGame miniGame in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    var data = MiniGameConvertToLandRankingItem(miniGame);
        //                    data.Category = LandSceneRankingCategory.MiniGame;
        //                    rsp.Items.Add(data);
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.SystemIsland:
        //            {
        //                var miniGames = GameApplication.DataManager.FindValue<MiniGame>(p =>
        //                    p.OwnerID == 0 &&
        //                    p.Version != 0 &&
        //                    p.Source.SpaceType == MiniGameSpaceType.Island &&
        //                    p.Source.Available);
        //                rsp.Total = miniGames.Count;
        //                var ranking = miniGames
        //                    .OrderByDescending(p => p.PlayerCount)
        //                    .ThenByDescending(p => p.Praise)
        //                    .ThenByDescending(p => p.Collection)
        //                    .ThenBy(p => p.PublishTime);
        //                foreach (MiniGame miniGame in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    var data = MiniGameConvertToLandRankingItem(miniGame);
        //                    data.Category = LandSceneRankingCategory.MiniGame;
        //                    rsp.Items.Add(data);
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.Choiceness:
        //            {
        //                //推荐分=(7日访问量+点赞数*10+收藏数*10+当前人数*100)*场景系数
        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p)).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p => p.OwnerID == 0 && p.Version != 0 && p.Source.Available);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                var scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //                var ranking = scenes
        //                    .OrderByDescending(p => p.SevenDaysVisitCount + p.RankingPraise * 10 + p.RankingCollection * 10 + p.PlayerCount * 100)
        //                    .ThenBy(p => p.ID);
        //                foreach (var scene in ranking.Skip(req.Index).Take(req.Count))
        //                {
        //                    switch (scene)
        //                    {
        //                        case LandSceneSpec ss:
        //                            {
        //                                var data = SceneConvertToLandRankingItem(ss);
        //                                data.Category = LandSceneRankingCategory.Space;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                        case MiniGame ms:
        //                            {
        //                                var data = MiniGameConvertToLandRankingItem(ms);
        //                                data.Category = LandSceneRankingCategory.MiniGame;
        //                                rsp.Items.Add(data);
        //                            }
        //                            break;
        //                    }
        //                }
        //            }
        //            break;
        //        case LandSceneRankingCategory.Township:
        //            {
        //                var scenes1 = GameApplication.DataManager.FindValue<Township>(p => true);
        //                var scenes2 = GameApplication.SceneManager.FindAllSpec<BeginnerSceneSpec>(p => p.TypeData.DimensionType == DimensionType.BeginnerLand).ToList();
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                if (req.Index < scenes2.Count)
        //                {
        //                    foreach (var township in scenes1.OrderByDescending(p => p.PlayerCount).ThenBy(p => p.ID).Skip(req.Index).Take(req.Count))
        //                    {
        //                        var item = new LandSceneRankingItemData();
        //                        item.ID = township.ID;
        //                        item.Category = LandSceneRankingCategory.Township;
        //                        item.TargetID = township.SceneID;
        //                        item.TargetName = township.Name;
        //                        item.OwnerID = township.AlcaldeID;
        //                        item.OwnerName = township.Alcalde.Name;

        //                        rsp.Items.Add(item);
        //                    }

        //                    req.Count -= rsp.Items.Count;
        //                    req.Index = Math.Min(0, req.Index - scenes1.Count);
        //                }
        //                if (req.Count > 0)
        //                {
        //                    foreach (var scene in scenes2.OrderByDescending(p => p.PlayerCount).ThenBy(p => p.ID).Skip(req.Index).Take(req.Count))
        //                    {
        //                        var item = new LandSceneRankingItemData();
        //                        item.ID = scene.ID;
        //                        item.Category = LandSceneRankingCategory.Township;
        //                        item.TargetID = scene.ID;
        //                        item.TargetName = scene.Name;

        //                        item.OwnerName = Character.GetLanguageMessage(LanguageStrings.TownDefaultOwner);

        //                        item.Url = scene.Url;
        //                        item.UrlVersion = scene.UrlVersion;
        //                        rsp.Items.Add(item);
        //                    }
        //                }
        //            }
        //            break;
        //        default:
        //            ShowError(LocalityStrings.argumentError);
        //            return;
        //    }

        //    SendGoogleProto(ClientMethod.ShowLandSceneRanking, rsp);
        //}

        //public void SearchLandScene(SearchLandSceneRequest req)
        //{
        //    if (req.Index < 0)
        //    {
        //        ShowError(LocalityStrings.argumentOutOfRangeError);
        //        return;
        //    }
        //    if (req.Count <= 0 || req.Count > 100)
        //    {
        //        ShowError(LocalityStrings.argumentOutOfRangeError);
        //        return;
        //    }

        //    var rsp = new LandSceneRankingResponse();
        //    rsp.Index = req.Index;
        //    IEnumerable<ILandSceneSpec> scenes;
        //    switch (req.Category)
        //    {
        //        case SearchLandSceneCategory.SceneName:
        //            {
        //                var name = ProtoSerializer.Deserialize<StringMessage>(req.Bytes).Msg;
        //                if (string.IsNullOrEmpty(name))
        //                {
        //                    ShowError(LocalityStrings.argumentError);
        //                    return;
        //                }

        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p => IsAvailable(p) && p.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p => p.OwnerID == 0 && p.Version != 0 && p.Source.Available && p.Name.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //            }
        //            break;
        //        case SearchLandSceneCategory.OwnerName:
        //            {
        //                var name = ProtoSerializer.Deserialize<StringMessage>(req.Bytes).Msg;
        //                if (string.IsNullOrEmpty(name))
        //                {
        //                    ShowError(LocalityStrings.argumentError);
        //                    return;
        //                }

        //                var scenes1 = GameApplication.SceneManager.FindAllSpec<MiniGameSceneSpec>(p =>
        //                    IsAvailable(p) &&
        //                    p.OwnerName != null &&
        //                    p.OwnerName.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0).ToList();
        //                var scenes2 = GameApplication.DataManager.FindValue<MiniGame>(p =>
        //                    p.Version != 0 &&
        //                    (p.OwnerID == 0 && p.Source.Available) &&
        //                    p.OwnerName != null &&
        //                    p.OwnerName.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0);
        //                rsp.Total = scenes1.Count + scenes2.Count;
        //                scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Concat(scenes2);
        //            }
        //            break;
        //        default:
        //            ShowError(LocalityStrings.argumentError);
        //            return;
        //    }
        //    foreach (var item in scenes.OrderByDescending(p => p.PlayerCount).Skip(req.Index).Take(req.Count))
        //    {
        //        switch (item)
        //        {
        //            case LandSceneSpec ss:
        //                {
        //                    var data = SceneConvertToLandRankingItem(ss);
        //                    data.Category = LandSceneRankingCategory.Space;
        //                    rsp.Items.Add(data);
        //                }
        //                break;
        //            case MiniGame ms:
        //                {
        //                    var data = MiniGameConvertToLandRankingItem(ms);
        //                    data.Category = LandSceneRankingCategory.MiniGame;
        //                    rsp.Items.Add(data);
        //                }
        //                break;
        //        }
        //    }

        //    SendGoogleProto(ClientMethod.SearchLandSceneResult, rsp);
        //}

        //public void GetMyLandScene(MyLandSceneRequest req)
        //{
        //    if (req.Index < 0 || req.Count <= 0 || req.Count > 100)
        //    {
        //        ShowError(LocalityStrings.argumentOutOfRangeError);
        //        return;
        //    }

        //    var township = GameApplication.SceneManager.GetScene(Character.TownshipSceneID, true);
        //    var area = township.FindArea(Character.TownshipAreaID);

        //    var scenes1 = GameApplication.SceneManager.FindAllSpec<LandSceneSpec>(p => IsAvailable(p) && p.OwnerID == Character.ID).ToList();
        //    var scenes = ((IEnumerable<ILandSceneSpec>)scenes1).Append(new AreaLandSceneSpec(Character, area));
        //    var rsp = new LandSceneRankingResponse();
        //    rsp.Index = req.Index;
        //    rsp.Total = scenes1.Count + 1;
        //    foreach (var scene in scenes.OrderByDescending(p => p.PlayerCount).Skip(req.Index).Take(req.Count - 1))
        //    {
        //        switch (scene)
        //        {
        //            case LandSceneSpec land:
        //                {
        //                    var data = SceneConvertToLandRankingItem(land);
        //                    data.Category = LandSceneRankingCategory.Space;
        //                    //data.TargetName = string.Empty;
        //                    rsp.Items.Add(data);
        //                }
        //                break;
        //            case AreaLandSceneSpec als:
        //                {
        //                    var data = TownshipConvertToLandRankingItem(Character, als.Area);
        //                    data.Category = LandSceneRankingCategory.Township;
        //                    //data.TargetName = string.Empty;
        //                    rsp.Items.Add(data);
        //                }
        //                break;
        //        }
        //    }

        //    SendGoogleProto(ClientMethod.MyLandSceneResult, rsp);
        //}


        //public void GetFavoriteSpace(Int32Message req)
        //{
        //    var rsp = new LandSceneRankingResponse();
        //    rsp.Total = Character.FavoriteSpaces.Count;
        //    var items = new List<(LandSceneRankingItemData, int)>();
        //    foreach (var favorite in Character.FavoriteSpaces)
        //    {
        //        var value = 0;
        //        switch (favorite.Category)
        //        {
        //            case FavoriteSpaceCategory.Scene:
        //                {
        //                    LandSceneRankingItemData item;
        //                    var target = GameApplication.SceneManager.FindSpec<HappySceneSpec>(favorite.TargetID);
        //                    if (target != null)
        //                    {
        //                        value = GetRankingValue(req.Value, target);
        //                        item = SceneConvertToLandRankingItem(target);
        //                        item.ID = favorite.ID;
        //                        if (favorite.TargetName != target.Name)
        //                        {
        //                            favorite.TargetName = target.Name;
        //                            favorite.Save();
        //                        }
        //                    }
        //                    else
        //                    {
        //                        item = GetDefaultLandSceneRankingItemData(favorite);
        //                    }

        //                    //rsp.Items.Add(item);
        //                    items.Add((item, value));
        //                }
        //                break;
        //            case FavoriteSpaceCategory.MiniGame:
        //                {
        //                    LandSceneRankingItemData item;
        //                    var target = GameApplication.DataManager.FindValue<MiniGame>(favorite.TargetID);
        //                    if (target != null)
        //                    {
        //                        if (target.OwnerID == 0)
        //                        {
        //                            item = MiniGameConvertToLandRankingItem(target);
        //                            if (favorite.TargetName != target.Name)
        //                            {
        //                                favorite.TargetName = target.Name;
        //                                favorite.Save();
        //                            }
        //                        }
        //                        else
        //                        {
        //                            var fingerpost = target.Scenes.Find(p => p.IsDefault);
        //                            var spec = GameApplication.SceneManager.FindSpec<MiniGameSceneSpec>(fingerpost.ID);
        //                            item = SceneConvertToLandRankingItem(spec);
        //                            if (favorite.TargetName != spec.Name)
        //                            {
        //                                favorite.TargetName = spec.Name;
        //                                favorite.Save();
        //                            }
        //                        }

        //                        item.ID = favorite.ID;
        //                        value = GetRankingValue(req.Value, target);
        //                    }
        //                    else
        //                    {
        //                        item = GetDefaultLandSceneRankingItemData(favorite);
        //                    }

        //                    //rsp.Items.Add(item);
        //                    items.Add((item, value));
        //                }
        //                break;
        //            case FavoriteSpaceCategory.Township:
        //                {
        //                    var target = GameApplication.DataManager.FindValue<Character>(favorite.TargetID);
        //                    var scene = GameApplication.SceneManager.GetScene(target.TownshipSceneID, true);
        //                    var area = scene.FindArea(target.TownshipAreaID);
        //                    if (favorite.TargetName != area.Name)
        //                    {
        //                        favorite.TargetName = area.Name;
        //                        favorite.Save();
        //                    }

        //                    value = GetRankingValue(req.Value, new AreaLandSceneSpec(target, area));
        //                    var item = TownshipConvertToLandRankingItem(target, area);
        //                    item.ID = favorite.ID;
        //                    //rsp.Items.Add(item);
        //                    items.Add((item, value));
        //                }
        //                break;
        //        }
        //    }

        //    rsp.Items.AddRange(items.OrderByDescending(p => p.Item2).ThenBy(p => p.Item1.ID).Select(p => p.Item1));
        //    SendGoogleProto(ClientMethod.ShowFavoriteSpace, rsp);

        //    static int GetRankingValue(int sortType, ILandSceneSpec spec)
        //    {
        //        switch (sortType)
        //        {
        //            case 1://按当前人数排序
        //                return spec.PlayerCount;
        //            case 2://按七日访问量排序
        //                return spec.SevenDaysVisitCount;
        //            case 3://按点赞数排序
        //                return spec.RankingPraise;
        //            case 4://按收藏数排序
        //                return spec.RankingCollection;
        //            default:
        //                return 0;
        //        }
        //    }
        //    static LandSceneRankingItemData GetDefaultLandSceneRankingItemData(FavoriteSpace favorite)
        //    {
        //        var data = new LandSceneRankingItemData();
        //        data.ID = favorite.ID;
        //        data.TargetID = favorite.TargetID;
        //        data.TargetName = favorite.TargetName;
        //        var target = favorite.TargetCharacter;
        //        if (target != null)
        //        {
        //            data.OwnerID = target.ID;
        //            data.OwnerName = target.Name;
        //            data.Avatar = target.SceneCharacter.AvatarData;
        //        }
        //        return data;
        //    }
        //}


        //public void CollectFavoriteSpace()
        //{
        //    if (Character.FavoriteSpaces.Count >= GameConfig.BaseConfig.MaxFavoriteSpaceCount)
        //    {
        //        ShowError(LocalityStrings.limitedCount);
        //        return;
        //    }
        //    var scene = Character.SceneCharacter.CurrentScene;
        //    if (scene.OwnerID == Character.ID)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }

        //    var rsp = new Int64BooleanMessage();
        //    rsp.Item2 = true;
        //    switch (scene)
        //    {
        //        case BeginnerScene:
        //        case TownshipScene:
        //            {
        //                var area = Character.SceneCharacter.CurrentArea;
        //                var target = area.Owner;
        //                if (area.OwnerID == 0 || target == null)
        //                {
        //                    ShowError(LocalityStrings.limitedTargetStatus);
        //                    return;
        //                }
        //                if (Character.ID == target.ID)
        //                {
        //                    ShowError(LocalityStrings.limitedTargetStatus);
        //                    return;
        //                }
        //                var favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.Township && p.TargetID == target.ID);
        //                if (favorite != null)
        //                {
        //                    ShowError(LocalityStrings.repetitiveOperation);
        //                    return;
        //                }

        //                favorite = RowAdapter.Create<FavoriteSpace>(autoSaving: false);
        //                favorite.OwnerID = Character.ID;
        //                favorite.Category = FavoriteSpaceCategory.Township;
        //                favorite.TargetID = target.ID;
        //                favorite.TargetName = area.Name;
        //                favorite.TargetCharacterID = target.ID;
        //                favorite.TargetCharacter = target;
        //                favorite.CreateSuccess();
        //                favorite.Initialize();
        //                favorite.Save();
        //                Character.FavoriteSpaces.TryAdd(favorite.ID, favorite);

        //                rsp.Item1 = area.ID;
        //                target.AddTownshipCollection(1);
        //            }
        //            break;
        //        case HappyScene hScene:
        //            {
        //                var favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.Scene && p.TargetID == hScene.ID);
        //                if (favorite != null)
        //                {
        //                    ShowError(LocalityStrings.repetitiveOperation);
        //                    return;
        //                }

        //                favorite = RowAdapter.Create<FavoriteSpace>(autoSaving: false);
        //                favorite.OwnerID = Character.ID;
        //                favorite.Category = FavoriteSpaceCategory.Scene;
        //                favorite.TargetID = hScene.ID;
        //                favorite.TargetName = hScene.Name;
        //                favorite.TargetCharacterID = hScene.OwnerID;
        //                favorite.CreateSuccess();
        //                favorite.Initialize();
        //                favorite.Save();
        //                Character.FavoriteSpaces.TryAdd(favorite.ID, favorite);

        //                hScene.AddCollection(1);
        //            }
        //            break;
        //        case MiniGameScene mScene:
        //            {
        //                var miniGame = mScene.MiniGame;
        //                var favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.MiniGame && p.TargetID == miniGame.ID);
        //                if (favorite != null)
        //                {
        //                    ShowError(LocalityStrings.repetitiveOperation);
        //                    return;
        //                }

        //                favorite = RowAdapter.Create<FavoriteSpace>(autoSaving: false);
        //                favorite.OwnerID = Character.ID;
        //                favorite.Category = FavoriteSpaceCategory.MiniGame;
        //                favorite.TargetID = miniGame.ID;
        //                if (miniGame.OwnerID == 0)
        //                {
        //                    favorite.TargetName = miniGame.Name;
        //                }
        //                else
        //                {
        //                    var fingerpost = miniGame.Scenes.Find(p => p.IsDefault);
        //                    var spec = GameApplication.SceneManager.FindSpec<MiniGameSceneSpec>(fingerpost.ID);
        //                    favorite.TargetName = spec.Name;
        //                }

        //                favorite.TargetCharacterID = miniGame.OwnerID;
        //                favorite.CreateSuccess();
        //                favorite.Initialize();
        //                favorite.Save();
        //                Character.FavoriteSpaces.TryAdd(favorite.ID, favorite);

        //                miniGame.AddCollection(1);
        //            }
        //            break;
        //        default:
        //            ShowError(LocalityStrings.limitedTargetStatus);
        //            return;
        //    }

        //    SendGoogleProto(ClientMethod.CollectFavoriteSpaceResult, rsp);
        //}

        //public void CancelCollectFavoriteSpace()
        //{
        //    var scene = Character.SceneCharacter.CurrentScene;
        //    if (scene.OwnerID == Character.ID)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    FavoriteSpace favorite;
        //    var rsp = new Int64BooleanMessage();
        //    rsp.Item2 = false;
        //    switch (scene)
        //    {
        //        case BeginnerScene:
        //        case TownshipScene:
        //            {
        //                var area = Character.SceneCharacter.CurrentArea;
        //                var target = area.Owner;
        //                if (target == null)
        //                {
        //                    ShowError(LocalityStrings.limitedTargetStatus);
        //                    return;
        //                }
        //                if (Character.ID == target.ID)
        //                {
        //                    ShowError(LocalityStrings.limitedTargetStatus);
        //                    return;
        //                }
        //                favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.Township && p.TargetID == target.ID);
        //                if (favorite == null)
        //                {
        //                    ShowError(LocalityStrings.repetitiveOperation);
        //                    return;
        //                }

        //                rsp.Item1 = area.ID;
        //                Character.FavoriteSpaces.Remove(favorite.ID);
        //                target.AddTownshipCollection(-1);
        //            }
        //            break;
        //        case HappyScene hScene:
        //            favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.Scene && p.TargetID == hScene.ID);
        //            if (favorite == null)
        //            {
        //                ShowError(LocalityStrings.repetitiveOperation);
        //                return;
        //            }

        //            Character.FavoriteSpaces.Remove(favorite.ID);
        //            hScene.AddCollection(-1);
        //            break;
        //        case MiniGameScene mScene:
        //            var miniGame = mScene.MiniGame;
        //            favorite = Character.FavoriteSpaces.Find(p => p.Category == FavoriteSpaceCategory.MiniGame && p.TargetID == miniGame.ID);
        //            if (favorite == null)
        //            {
        //                ShowError(LocalityStrings.repetitiveOperation);
        //                return;
        //            }

        //            Character.FavoriteSpaces.Remove(favorite.ID);
        //            miniGame.AddCollection(-1);
        //            break;
        //        default:
        //            ShowError(LocalityStrings.limitedTargetStatus);
        //            return;
        //    }

        //    SendGoogleProto(ClientMethod.CollectFavoriteSpaceResult, rsp);
        //}

        //public void DeleteFavoriteSpace(IDMessage req)
        //{
        //    var favorite = Character.FavoriteSpaces.Find(req.Id);
        //    if (favorite == null)
        //    {
        //        ShowError(LocalityStrings.targetObjectNotFound);
        //        return;
        //    }
        //    Character.FavoriteSpaces.Remove(favorite.ID);
        //    favorite.Deleted = true;
        //    favorite.Save();
        //    var area = Character.SceneCharacter.CurrentArea;
        //    switch (favorite.Category)
        //    {
        //        case FavoriteSpaceCategory.Scene:
        //            var scene = GameApplication.SceneManager.GetScene<HappyScene>(favorite.TargetID, true);
        //            scene?.AddCollection(-1);
        //            if (area.Scene is HappyScene hScene && favorite.TargetID == hScene.ID)
        //            {
        //                var rsp = new Int64BooleanMessage();
        //                rsp.Item2 = false;
        //                SendGoogleProto(ClientMethod.CollectFavoriteSpaceResult, rsp);
        //            }
        //            break;
        //        case FavoriteSpaceCategory.MiniGame:
        //            var miniGame = GameApplication.DataManager.FindValue<MiniGame>(favorite.TargetID);
        //            miniGame?.AddCollection(-1);
        //            if (area.Scene is MiniGameScene mScene && favorite.TargetID == mScene.GameID)
        //            {
        //                var rsp = new Int64BooleanMessage();
        //                rsp.Item2 = false;
        //                SendGoogleProto(ClientMethod.CollectFavoriteSpaceResult, rsp);
        //            }
        //            break;
        //        case FavoriteSpaceCategory.Township:
        //            var target = GameApplication.DataManager.FindValue<Character>(favorite.TargetID);
        //            target?.AddTownshipCollection(-1);
        //            if (favorite.TargetID == area.OwnerID && (area.Scene is BeginnerScene || area.Scene is TownshipScene))
        //            {
        //                var rsp = new Int64BooleanMessage();
        //                rsp.Item1 = area.ID;
        //                rsp.Item2 = false;
        //                SendGoogleProto(ClientMethod.CollectFavoriteSpaceResult, rsp);
        //            }
        //            break;
        //    }

        //    SendGoogleProto(ClientMethod.DeleteFavoriteSpace, req);
        //}

        //public void EnterFavoriteSpace(IDMessage req)
        //{
        //    var favorite = Character.FavoriteSpaces.Find(req.Id);
        //    if (favorite == null)
        //    {
        //        ShowError(LocalityStrings.targetObjectNotFound);
        //        return;
        //    }
        //    switch (favorite.Category)
        //    {
        //        case FavoriteSpaceCategory.Scene:
        //            {
        //                var scene = GameApplication.SceneManager.GetScene(favorite.TargetID, true);
        //                if (scene == null)
        //                {
        //                    ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                    return;
        //                }
        //                if (!scene.CanEnter(Character, null))
        //                {
        //                    //ShowError(error);
        //                    return;
        //                }

        //                scene.Enter(Character.SceneCharacter);
        //            }
        //            break;
        //        case FavoriteSpaceCategory.MiniGame:
        //            {
        //                var miniGame = GameApplication.DataManager.FindValue<MiniGame>(favorite.TargetID);
        //                if (miniGame == null)
        //                {
        //                    ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                    return;
        //                }
        //                if (miniGame.OwnerID == 0)
        //                {
        //                    if (!miniGame.Source.Available || miniGame.Version == 0)
        //                    {
        //                        ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                        return;
        //                    }

        //                    var release = miniGame.Release;
        //                    var data = release.Scenes.Scenes.Find(p => p.IsDefault);
        //                    var dimension = GameApplication.MiniGameManger.GetDimension(miniGame, release);
        //                    var level = dimension.GetLevel(data);
        //                    dimension.Enter(Character.SceneCharacter, level);
        //                }
        //                else
        //                {
        //                    var fingerpost = miniGame.Scenes.Find(p => p.IsDefault);
        //                    var scene = fingerpost.GetScene(miniGame);
        //                    if (scene == null)
        //                    {
        //                        ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                        return;
        //                    }
        //                    if (!scene.CanEnter(Character, null))
        //                    {
        //                        //ShowError(error);
        //                        return;
        //                    }

        //                    scene.Enter(Character.SceneCharacter);
        //                }
        //            }
        //            break;
        //        case FavoriteSpaceCategory.Township:
        //            {
        //                var target = GameApplication.DataManager.FindValue<Character>(favorite.TargetID);
        //                if (target == null)
        //                {
        //                    ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                    return;
        //                }
        //                if (target.TownshipSceneID == target.BeginnerSceneID)
        //                {
        //                    //在远岛
        //                    ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                    return;
        //                }
        //                var scene = GameApplication.SceneManager.GetScene(target.TownshipSceneID, true);
        //                if (!scene.CanEnter(Character, null))
        //                {
        //                    //ShowError(error);
        //                    return;
        //                }

        //                var area = scene.FindArea(target.TownshipAreaID);
        //                scene.Enter(Character.SceneCharacter, area.ID);
        //            }
        //            break;
        //        default:
        //            ShowError(LocalityStrings.unknownError);
        //            return;
        //    }
        //}

        public void PraiseSpace()
        {
            var scene = Character.SceneCharacter.CurrentScene;
            if (scene.OwnerID == Character.ID)
            {
                ShowError(LocalityStrings.limitedTargetStatus);
                return;
            }
            var rsp = new Int64BooleanMessage();
            switch (scene)
            {
                //case MiniGameScene mScene:
                //    {
                //        var miniGame = mScene.MiniGame;
                //        if (miniGame.PraiseRecords.TryRemove(Character.ID, out MiniGamePraiseRecord record))
                //        {
                //            rsp.Item2 = false;
                //            record.Deleted = true;
                //            record.Save();

                //            miniGame.AddPraise(-1);
                //        }
                //        else
                //        {
                //            rsp.Item2 = true;
                //            record = RowAdapter.Create<MiniGamePraiseRecord>(autoSaving: false);
                //            record.GameID = mScene.GameID;
                //            record.CharacterID = Character.ID;
                //            record.CreateSuccess();
                //            record.Initialize();
                //            record.Save();

                //            miniGame.AddPraise(1);
                //            miniGame.PraiseRecords.TryAdd(record.CharacterID, record);
                //        }
                //    }
                //    break;
                default:
                    ShowError(LocalityStrings.limitedTargetStatus);
                    return;
            }

            SendGoogleProto(ClientMethod.PraiseSpaceResult, rsp);
        }

        //public void GetHistorySpaces()
        //{
        //    var rsp = new LandSceneRankingResponse();
        //    rsp.Total = Character.HistorySpaceRecord.Count;
        //    foreach (var history in Character.HistorySpaceRecord.OrderByDescending(p => p.Date))
        //    {
        //        switch (history.Category)
        //        {
        //            case HistorySpaceCategory.Scene:
        //                {
        //                    LandSceneRankingItemData item;
        //                    var target = GameApplication.SceneManager.FindSpec<HappySceneSpec>(history.TargetID);
        //                    if (target != null)
        //                    {
        //                        item = SceneConvertToLandRankingItem(target);
        //                        item.ID = history.ID;
        //                        if (history.TargetName != target.Name)
        //                        {
        //                            history.TargetName = target.Name;
        //                            history.Save();
        //                        }
        //                    }
        //                    else
        //                    {
        //                        item = GetDefaultHistoryItemData(history);
        //                    }

        //                    rsp.Items.Add(item);
        //                }
        //                break;
        //            case HistorySpaceCategory.MiniGame:
        //                {
        //                    LandSceneRankingItemData item;
        //                    var target = GameApplication.DataManager.FindValue<MiniGame>(history.TargetID);
        //                    if (target != null)
        //                    {
        //                        if (target.OwnerID == 0)
        //                        {
        //                            item = MiniGameConvertToLandRankingItem(target);
        //                            if (history.TargetName != target.Name)
        //                            {
        //                                history.TargetName = target.Name;
        //                                history.Save();
        //                            }
        //                        }
        //                        else
        //                        {
        //                            var fingerpost = target.Scenes.Find(p => p.IsDefault);
        //                            var spec = GameApplication.SceneManager.FindSpec<MiniGameSceneSpec>(fingerpost.ID);
        //                            item = SceneConvertToLandRankingItem(spec);
        //                            if (history.TargetName != spec.Name)
        //                            {
        //                                history.TargetName = spec.Name;
        //                                history.Save();
        //                            }
        //                        }

        //                        item.ID = history.ID;
        //                    }
        //                    else
        //                    {
        //                        item = GetDefaultHistoryItemData(history);
        //                    }

        //                    rsp.Items.Add(item);
        //                }
        //                break;
        //        }
        //    }

        //    SendGoogleProto(ClientMethod.ShowHistorySpaces, rsp);

        //    static LandSceneRankingItemData GetDefaultHistoryItemData(CharacterHistorySpaceRecord record)
        //    {
        //        var data = new LandSceneRankingItemData();
        //        data.ID = record.ID;
        //        data.TargetID = record.TargetID;
        //        data.TargetName = record.TargetName;
        //        return data;
        //    }
        //}

        //public void EnterHistorySpace(IDMessage req)
        //{
        //    var history = Character.HistorySpaceRecord.Find(req.Id);
        //    if (history == null)
        //    {
        //        ShowError(LocalityStrings.targetObjectNotFound);
        //        return;
        //    }
        //    switch (history.Category)
        //    {
        //        case HistorySpaceCategory.Scene:
        //            {
        //                var scene = GameApplication.SceneManager.GetScene(history.TargetID, true);
        //                if (scene == null)
        //                {
        //                    ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //                    return;
        //                }
        //                if (!scene.CanEnter(Character, null))
        //                {
        //                    //ShowError(error);
        //                    return;
        //                }

        //                scene.Enter(Character.SceneCharacter);
        //            }
        //            break;
        //        //case HistorySpaceCategory.MiniGame:
        //        //    {
        //        //        var miniGame = GameApplication.DataManager.FindValue<MiniGame>(history.TargetID);
        //        //        if (miniGame == null)
        //        //        {
        //        //            ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //        //            return;
        //        //        }
        //        //        if (miniGame.OwnerID == 0)
        //        //        {
        //        //            if (!miniGame.Source.Available || miniGame.Version == 0)
        //        //            {
        //        //                ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //        //                return;
        //        //            }

        //        //            var release = miniGame.Release;
        //        //            var data = release.Scenes.Scenes.Find(p => p.IsDefault);
        //        //            var dimension = GameApplication.MiniGameManger.GetDimension(miniGame, release);
        //        //            var level = dimension.GetLevel(data);
        //        //            dimension.Enter(Character.SceneCharacter, level);
        //        //        }
        //        //        else
        //        //        {
        //        //            var fingerpost = miniGame.Scenes.Find(p => p.IsDefault);
        //        //            var scene = fingerpost.GetScene(miniGame);
        //        //            if (scene == null)
        //        //            {
        //        //                ShowError(LocalityStrings.favoriteTargetNotAvailable);
        //        //                return;
        //        //            }
        //        //            if (!scene.CanEnter(Character, null))
        //        //            {
        //        //                //ShowError(error);
        //        //                return;
        //        //            }

        //        //            scene.Enter(Character.SceneCharacter);
        //        //        }
        //        //    }
        //        //    break;
        //        default:
        //            ShowError(LocalityStrings.unknownError);
        //            return;
        //    }
        //}

        public void UpdateSceneUrl(Int32StringMessage req)
        {
            var area = Character.SceneCharacter.CurrentArea;
            var scene = area.Scene;
            if (scene.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }

            scene.Url = req.Item2;
            scene.UrlVersion = req.Item1;
            scene.Save();
        }

        #region PartyScene

        private const int MinPartyGroup = 1;
        private const int MaxPartyGroup = 4;

        public void StartParty(Int32Message req)
        {
            if (Character.SceneCharacter.CurrentScene is not PersonalScene scene)
            {
                ShowError(LocalityStrings.limitedTargetStatus);
                return;
            }
            if (scene.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            if (scene.PartyGroup != 0)
            {
                ShowError(LocalityStrings.limitedTargetStatus);
                return;
            }
            if (req.Value < MinPartyGroup || req.Value > MaxPartyGroup)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (scene.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            if (!Character.DeleteEntity(GameConfig.BaseConfig.StartPartyItemType, 1, "开启派对", null, scene.ID))
            {
                ShowError(LocalityStrings.limitedCondition);
                return;
            }
            if (string.IsNullOrEmpty(scene.PartyTitle))
            {
                scene.PartyTitle = "一起参加派对吧";
            }
            if (string.IsNullOrEmpty(scene.Description))
            {
                scene.Description = "欢迎来到我的派对";
            }

            scene.PartyGroup = req.Value;
            scene.PartyEndTime = DateTimeExtension.Now + TimeSpan.FromDays(1);
            scene.Save();
            scene.UpdateToSceneService(p =>
            {
                p.PartyGroup = scene.PartyGroup;
                p.PartyTitle = scene.PartyTitle;
                p.PartyEndTimestamp = scene.PartyEndTime.ToTimestamp();
                p.Description = scene.Description;
            });
            ShowTipsMessage(LocalityStrings.startSuccess);

            AddToRanking(scene);
            AddToRanking(scene, scene.PartyGroup);
            static void AddToRanking(PersonalScene scene, int group = 0)
            {
                var ranking = GameApplication.RankingManager.GetRanking<PartySceneRanking>(scene, group);
                var item = ranking.CreateItem(scene.Spec);
                using (ranking.EnterWriterLock())
                {
                    ranking.Add(item);
                }
            }
        }

        public void ContinueParty()
        {
            if (Character.SceneCharacter.CurrentScene is not PersonalScene scene)
            {
                ShowError(LocalityStrings.targetStateError);
                return;
            }
            if (scene.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            if (scene.PartyGroup == 0)
            {
                ShowError(LocalityStrings.targetStateError);
                return;
            }
            lock (scene)
            {
                if (scene.PartyGroup == 0)
                {
                    //派对被关闭
                    return;
                }
                if (!Character.DeleteEntity(GameConfig.BaseConfig.ContinuePartyItemType, 1, "开启派对", null, scene.ID))
                {
                    ShowError(LocalityStrings.limitedCondition);
                    return;
                }

                scene.PartyEndTime += TimeSpan.FromHours(12);
                scene.Save();
                scene.UpdateToSceneService(p => p.PartyEndTimestamp = scene.PartyEndTime.ToTimestamp());
                ShowTipsMessage(LocalityStrings.continueSuccess1);
            }
        }

        public void GetPartySceneRanking(SceneRankingRequest req)
        {
            if (req.Index < 0)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (req.Count <= 0 || req.Count > 100)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (req.Type < 0 || req.Type > MaxPartyGroup)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }

            var scene = Character.SceneCharacter.CurrentScene;
            var ranking = GameApplication.RankingManager.GetRanking<PartySceneRanking>(scene, req.Type);
            var rsp = new PartySceneRankingResponse();
            rsp.Index = req.Index;
            rsp.Group = req.Group;
            rsp.Type = req.Type;
            using (ranking.EnterReaderLock())
            {
                rsp.Total = ranking.Count;
                foreach (var item in ranking.GetRange(req.Index, req.Count))
                {
                    rsp.Items.Add(item.PartyProtoData);
                }
            }
            User.SendGoogleProto(ClientMethod.ShowPartySceneRanking, rsp);
        }

        public void GetFavoritePartyScenes(SceneRankingRequest req)
        {
            if (req.Index < 0)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (req.Count <= 0 || req.Count > 100)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }

            var scenes = Character.FavoriteScenes
                .Find(p => p.SceneSpec != null)
                .OrderByDescending(p => p.SceneSpec.PlayerCount)
                .ThenByDescending(p => p.SceneSpec.VisitTimes)
                .ToList();

            var rsp = new PartySceneRankingResponse();
            rsp.Index = req.Index;
            rsp.Total = scenes.Count;
            rsp.Group = req.Group;
            foreach (var scene in scenes.Skip(req.Index).Take(req.Count))
            {
                rsp.Items.Add(scene.SceneSpec.PartyProtoData);
            }

            User.SendGoogleProto(ClientMethod.ShowFavoritePartyScenes, rsp);
        }

        public void SearchPartyScene(SearchPartySceneRequest req)
        {
            if (req.Index < 0)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }
            if (req.Count <= 0 || req.Count > 100)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }

            List<PersonalSceneSpec> scenes;
            switch (req.Category)
            {
                case SearchPartySceneCategory.ID:
                    scenes = new List<PersonalSceneSpec>();
                    var im = ProtoSerializer.Deserialize<IDMessage>(req.Bytes);
                    var sp = GameApplication.SceneManager.FindSpec<PersonalSceneSpec>(im.Id);
                    if (sp != null) scenes.Add(sp);
                    break;
                case SearchPartySceneCategory.Name:
                    var sm = ProtoSerializer.Deserialize<StringMessage>(req.Bytes);
                    scenes = GameApplication.SceneManager.FindAllSpec<PersonalSceneSpec>(p => p.Name.Contains(sm.Msg)).ToList();
                    break;
                default:
                    ShowError(LocalityStrings.argumentError);
                    return;
            }

            var rsp = new SearchPartySceneResponse();
            rsp.Total = scenes.Count;
            rsp.Index = req.Index;
            rsp.Group = req.Group;
            foreach (var scene in scenes.Skip(req.Index).Take(req.Count))
            {
                rsp.Items.Add(scene.PartyProtoData);
            }

            User.SendGoogleProto(ClientMethod.SearchPartySceneResult, rsp);
        }
        #endregion
        #endregion Scene

        #region SceneObject

        private Area FindArea(SceneAreaIDMessage saim)
        {
            var scene = GameApplication.DataManager.FindValue<Scene>(saim.SceneID);
            return scene.FindArea(saim.AreaID);
        }

        private Area FindArea(long sceneID, long areaID)
        {
            var scene = GameApplication.DataManager.FindValue<Scene>(sceneID);
            return scene.FindArea(areaID);
        }

        #region Pica 编辑场景

        private LocalityString CheckEditSceneObjectOnPlace(IDEditMessage idMessage, ref SceneObject sceneObj, PlaceSceneObjectBehavior behavior)
        {
            var area = Character.SceneCharacter.CurrentArea;

            if (idMessage.EntityPlace != SceneObjectEntityPlace.IsInScene)
            {
                return null;
            }
            if (idMessage.Transform == null)
            {
                return LocalityStrings.argumentError;
            }

            sceneObj = behavior.FindSceneObjectToPlace(area, Character, idMessage.Id);
            if (sceneObj == null) { return null; }
            return CheckPlace(area, sceneObj, idMessage.Transform.ToTransform());
        }

        private LocalityString CheckEditSceneObjectPutBag(IDEditMessage idMessage, ref SceneObject? sceneObj)
        {
            var area = Character.SceneCharacter.CurrentArea;
            if (area == null) { return LocalityStrings.targetAreaNotFound; }
            sceneObj = area.FindObject(idMessage.Id);
            if (sceneObj == null)
            {
                Character.ReportRoomVersion();
                return LocalityStrings.targetObjectNotFound;
            }
            return CheckPickUp(area, sceneObj);
        }

        private LocalityString CheckPickUp(Area area, SceneObject? so)
        {
            if (Character.AdminType == 0 && area.SellStatus == SellStatus.OnSell)
            {
                return (LocalityStrings.limitedActionOnAreaSellStatus);
            }

            //if (so.OwnerID != Character.ID && Character.AdminType <= 0)
            //{
            //    return (LocalityStrings.notOwner);
            //}
            if (!so.TypeData.CanPickUp)
            {
                return (LocalityStrings.limitedPickUp);
            }

            if (so.GetFunction<PicaUntakeBackFunction>() != null)
            {
                return (LocalityStrings.argumentError);
            }

            if (!so.CanPickUp(out LocalityString error))
            {
                return (error);
            }
            return null;
        }

        private void DoPickUp(Area area, SceneObject so, PlaceSceneObjectBehavior behavior)
        {
            //if (so.OwnerID != Character.ID && Character.AdminType > 0)
            //{
            //    //如果管理员回收不是自己的物品，就变成自己的
            //    so.OwnerID = Character.ID;
            //    so.OwnerCategory = OwnerCategory.Player;
            //    so.Save();
            //}

            behavior.PickUpSceneObject(Character, area, so);
            OnAreaObjetChanged(area);
        }

        private LocalityString CheckPlace(Area area, SceneObject so, Transform transform)
        {
            var scene = Character.SceneCharacter.CurrentArea.Scene;

            //if (transform != null)
            //{
            //    var p = transform.Position;
            //    if (!area.LocalContains(p.X, p.Z))
            //    {
            //        return (LocalityStrings.coordinateError);
            //    }
            //}

            if (so == null || !Character.IsInBag(so, scene))
            {
                return LocalityStrings.targetObjectNotFound;
            }
            if (so.AreaID != 0)
            {
                //操作了一个已经放置了的建筑
                Logger.GameError.Write("Character => ID:{0}. Scene => ID:{1}. SceneObject => ID:{2}. {3} area error!", Character.ID, scene.ID, so.ID, nameof(CheckPlace));
                return LocalityStrings.argumentError;
            }
            //if (area.OwnerID != Character.ID && so.GetFunction<PicaNoteFunction>() == null)
            //{
            //    return (LocalityStrings.limitedAuthority);
            //}
            if (Character.AdminType == 0 && area.SellStatus == SellStatus.OnSell)
            {
                return (LocalityStrings.limitedActionOnAreaSellStatus);
            }
            var sot = so.TypeData;

            if (!CanPlace(area, so, sot, transform, 0))
            {
                // TODO need better error
                return LocalityStrings.limitedPlaceScene;
            }
            return null;
        }

        struct BagMountStruct
        {
            public long id;
            public long mountTargetID;
            public int mountTargetIndex;
        }

        struct EditSceneObjectResult
        {
            public SceneObject SceneObject;
            public IDEditMessage Result;
        }

        /// <summary>
        /// 这个用于放置特殊的家具，目前只有留言条
        /// </summary>
        /// <param name="req"></param>
        public void PutSpecialSceneObject(IDEditResultListMessage req)
        {
            var area = Character.SceneCharacter.CurrentArea;
            var scene = area?.Scene;
            if (scene is not PicaRoomScene room) return;

            if (req.Result.Count != 1) return;

            var data = req.Result.First();
            var soId = data.Id;

            var soInBag = Character.FindEntity<SceneObject>(soId);
            if (soInBag == null)
            {
                Character.ShowMessage(PicaLocalityStrings.PKT_SYS2_0000014);
                return;
            }

            var transform = data.Transform ?? soInBag.Transform.ProtoData;

            var behavior = PlaceSceneObjectFactory.GetInstance(PlaceSceneObjectFactory.Mode.MyRoom);
            var so = DoPlace(area, soInBag, transform, 0, behavior);

            OnAreaObjetChanged(area);
            behavior.OnSceneEdit(scene, Character);

            Character.CheckUpdateAction(Character, UserAction.摆放家具, soInBag.Type);
        }
        /// <summary>
        /// 编辑小屋
        /// 
        /// </summary>
        /// <param name="req"></param>
        public void SyncEditSceneObjects(IDEditResultListMessage req)
        {
            var area = Character.SceneCharacter.CurrentArea;
            var scene = area?.Scene;

            var editMode = PlaceSceneObjectFactory.Mode.MyRoom;
            if (scene is OrganizationScene)
            {
                editMode = PlaceSceneObjectFactory.Mode.Organization;
            }
            var behavior = PlaceSceneObjectFactory.GetInstance(editMode);

            bool success = false;
            LocalityString error = null;

            if (!behavior.CheckScene(scene))
            {
                error = (LocalityStrings.invalidOperation);
                goto ShowEnd;
            }

            if (!behavior.CheckEditRight(scene, Character))
            {
                error = LocalityStrings.GetUndefinedString("没有编辑权限！");
                goto ShowEnd;
            }

            //if (((PicaRoomScene)scene).Frozen)
            //{
            //    error = (LocalityStrings.invalidOperation);
            //    goto ShowEnd;
            //}

            if (area == null)
            {
                error = (LocalityStrings.targetAreaNotFound);
                goto ShowEnd;
            }

            if (req.BornPositionData != null)
            {
                behavior.ProcessBornPosition(scene, req.BornPositionData);
            }

            if (req.Result.Count == 0)
            {
                Logger.Warning.Write($"{nameof(SyncEditSceneObjects)}: {nameof(IDEditResultListMessage)} Result is empty just return");
                return;
            }

            var needPlaceSceneObjectList = new List<EditSceneObjectResult>();
            var needPickUpSceneObjectList = new List<EditSceneObjectResult>();

            Dictionary<long, Dictionary<int, long>> mountIds = new Dictionary<long, Dictionary<int, long>>();

            var placeJson = new JsonObject();
            var place_item_ids = new JsonArray();
            var place_item_names = new JsonArray();
            var backBagJson = new JsonObject();
            var back_item_ids = new JsonArray();
            var back_item_names = new JsonArray();
            // check 
            foreach (var idMessage in req.Result)
            {
                SceneObject sceneObj = null;

                if (idMessage.EntityPlace == SceneObjectEntityPlace.IsInScene)
                {
                    error = CheckEditSceneObjectOnPlace(idMessage, ref sceneObj, behavior);
                    if (error != null) { goto ShowEnd; }
                    if (sceneObj != null)
                    {
                        sceneObj.Transform = idMessage.Transform.ToTransform();
                        needPlaceSceneObjectList.Add(new EditSceneObjectResult
                        {
                            SceneObject = sceneObj,
                            Result = idMessage
                        });
                        //placeJson[I18nManager.SceneObjectName(sceneObj.Type)] = sceneObj.Stat;
                        place_item_ids.Add(sceneObj.Type);
                        place_item_names.Add(I18nManager.SceneObjectName(sceneObj.Type));

                        if (editMode == PlaceSceneObjectFactory.Mode.MyRoom)
                        {
                            Character.CheckUpdateAction(Character, UserAction.摆放家具, sceneObj.Type);
                        }
                    }
                }
                else if (idMessage.EntityPlace == SceneObjectEntityPlace.IsInBag) 
                {
                    error = CheckEditSceneObjectPutBag(idMessage, ref sceneObj);
                    if (error != null) { goto ShowEnd; }
                    if (sceneObj != null)
                    {
                        needPickUpSceneObjectList.Add(new EditSceneObjectResult
                        {
                            SceneObject = sceneObj,
                            Result = idMessage
                        });
                    }
                    //backBagJson[I18nManager.SceneObjectName(sceneObj.Type)] = sceneObj.Stat;
                    back_item_ids.Add(sceneObj.Type);
                    back_item_names.Add(I18nManager.SceneObjectName(sceneObj.Type));
                    if (editMode == PlaceSceneObjectFactory.Mode.MyRoom)
                    {
                        Character.CheckUpdateAction(Character, UserAction.摆放家具, sceneObj.Type);
                    }
                }
                if (sceneObj == null || idMessage.MountTargetID == 0)
                {
                    continue;
                }


                //for (int i = 0; i <idMessage.MountIDs.Count; i++)
                //{
                //    if (!sceneObj.CanMount(i))
                //    {
                //        error = LocalityStrings.GetUndefinedString("无法挂载对象");
                //        goto ShowEnd;
                //    }
                //}
            }
            if (place_item_ids.Count != 0 || back_item_ids.Count != 0)
            {
                placeJson["id"] = place_item_ids.ToString();
                placeJson["name"] = place_item_names.ToString();
                backBagJson["id"] = back_item_ids.ToString();
                //backBagJson["name"] = back_item_names.ToString();
                behavior.ReportEditToTA(scene, Character, placeJson, backBagJson);
            }

            // Place SceneObjects
            foreach (var obj in needPlaceSceneObjectList)
            {
                var transform = obj.Result.Transform ?? obj.SceneObject.Transform.ProtoData;
                var so = DoPlace(area, obj.SceneObject, transform, 0, behavior);

                //如果是新加入场景的物件 需要替换成目前场景中的sceneobject的
                foreach (var obj_res in req.Result)
                {
                    for (int i = 0; i < obj_res.MountIDs.Count; i++)
                    {
                        //这里需要判断break以确保是一对一的关系
                        var if_break = false;
                        if (obj_res.MountIDs[i] == obj.SceneObject.ID)
                        {
                            obj_res.MountIDs.RemoveAt(i);
                            obj_res.MountIDs.Insert(i, so.ID);
                            if_break = true;
                            break;
                        }
                        if (if_break)
                        {
                            break;
                        }
                    }
                }

                var dirty = so.CompareMountChanged(obj.Result.MountIDs);
                if (dirty.Count > 0)
                {
                    mountIds.Add(so.ID, dirty);
                }

                ProcessAdditionalData(obj.Result, so);

            }

            //  PickUp SceneObjects
            foreach (var obj in needPickUpSceneObjectList)
            {
                DoPickUp(area, obj.SceneObject, behavior);
            }

            // DoSyncEdit transform 
            foreach (var idMessage in req.Result)
            {
                if (idMessage.EntityPlace == SceneObjectEntityPlace.IsInBag) continue;
                var sceneObj = area.FindObject(idMessage.Id);
                if (sceneObj == null) continue;
                if (idMessage.Transform != null && !sceneObj.Transform.Equals(Transform.FromTransformData(idMessage.Transform)))
                {
                    area.TransformObject(sceneObj, idMessage.Transform, area);
                }
            }

            foreach (var idMessage in req.Result)
            {
                if (!idMessage.HasDynamicMountGroup)
                {
                    continue;
                }
                foreach (var point in idMessage.DynamicMountGroup.Group)
                {
                    if (idMessage.EntityPlace == SceneObjectEntityPlace.IsInBag) continue;
                    var sceneObj = area.FindObject(idMessage.Id);
                    if (sceneObj == null) continue;
                    area.AddSceneObjectDynamicMount(
                        sceneObj,
                        idMessage.DynamicMountGroup.Animation,
                        point.Index,
                        new Vector3() { X = point.Point.X, Y = point.Point.Y, Z = point.Point.Z }
                    );
                }
            }

            // DoMount
            var mountReq = new SceneSceneObjectActionStatusDataList()
            {
                SceneID = scene.ID,
                AreaID = area.ID,
            };
            Dictionary<int, int> objQuelis = new Dictionary<int, int>()
            {
                {1,0 },
                {2,0 },
                {3,0 },
                {4,0 },
                {5,0 },
            };
            foreach (var idMessage in req.Result)
            {
                // 没有挂载关系的跳过
                if (idMessage.MountIDs == null || idMessage.MountIDs.Count == 0) continue;
                // 在包里的跳过
                if (idMessage.EntityPlace == SceneObjectEntityPlace.IsInBag) continue;

                var sceneObj = area.FindObject(idMessage.Id);
                if (sceneObj == null) continue;
                DoMount(sceneObj, sceneObj.CompareMountChanged(idMessage.MountIDs), ref mountReq);
            }
            foreach (var mountId in mountIds)
            {
                var sceneObj = area.FindObject(mountId.Key);
                if (sceneObj == null) continue;
                DoMount(sceneObj, mountId.Value, ref mountReq);
            }

            if (mountReq.ActionStatusDataList.Count > 0)
            {
                area.SendToSceneService(SceneManagerClientMethod.ChangeActionStatusList, mountReq);
            }

            foreach (var obj in area.Objects)
            {
                if (obj.Value.TypeData != null && obj.Value.TypeData.Quality != Quality.None)
                {
                    objQuelis[(int)(obj.Value.TypeData.Quality)]++;
                }
            }
            if (editMode == PlaceSceneObjectFactory.Mode.MyRoom)
            {
                Character.CheckUpdateAction(Character, UserAction.摆放家具数量, value: area.ObjectCount, mode: MissionValueTriggerCategory.Assignment);
                foreach (var pair in objQuelis)
                {
                    Character.CheckUpdateAction(Character, UserAction.摆放星级家具, (long)pair.Key, value: pair.Value, mode: MissionValueTriggerCategory.Assignment, sceneID: area.SceneID);
                }
                Character.CheckUpdateAction(Character, UserAction.编辑小屋);
            }
            success = true;
            //((PicaRoomScene)(Character.SceneCharacter.CurrentScene)).UpdateProsperity();
            if (Character.IsInLove(scene.OwnerID))
            {
                Character.CheckUpdateAction(Character, UserAction.编辑羁绊对象房间);
            }
            OnAreaObjetChanged(area);
            behavior.OnSceneEdit(Character.SceneCharacter.CurrentScene, Character);

            if (Character.CheckOnEditRoom)
            {
                TestCheckSceneObjectEdit(scene);
            }

            return;

        ShowEnd:
            Logger.Debug.Write($"success: {success}, error {error?.ToString()}");
            if (error != null) ShowError(error);
        }


        private void TestCheckSceneObjectEdit(Scene scene)
        {
            var hasError = false;
            var error = "";
            foreach (var area in scene.Areas.Values)
            {
                foreach (var obj in area.Objects.Values)
                {
                    if (obj.InteractionPoint != null)
                    {
                        var targetId = obj.InteractionPoint.TargetID;
                        if (targetId == 0) continue;
                        var target = area.FindObject(targetId);
                        if (target == null)
                        {
                            hasError = true;
                            error += $"家具 {obj.LogDisplayName} ({obj.ID}) 应该放置在 {targetId} 上但是目标不存在\n";
                            continue;
                        }
                        if (target.InteractionSceneObjects == null || !target.InteractionSceneObjects.ContainsKey(obj.InteractionPoint.TargetIndex))
                        {
                            hasError = true;
                            error += $"家具 {obj.LogDisplayName} ({obj.ID}) 应该放置在 {target.LogDisplayName} ({targetId}) 上但是目标没有放置信息\n";
                            continue;
                        }
                        if (target.InteractionSceneObjects[obj.InteractionPoint.TargetIndex] != obj.ID)
                        {
                            hasError = true;
                            error += $"家具 {obj.LogDisplayName} ({obj.ID}) 应该放置在 {target.LogDisplayName} ({targetId}) 位置{obj.InteractionPoint.TargetIndex} 上但是目标的位置上的放置记录为 {target.InteractionSceneObjects[obj.InteractionPoint.TargetIndex]}\n";
                            continue;
                        }
                    }
                    if (obj.InteractionSceneObjects != null)
                    {
                        foreach (var pair in obj.InteractionSceneObjects)
                        {
                            var index = pair.Key;
                            var target = area.FindObject(pair.Value);
                            if (target == null)
                            {
                                hasError = true;
                                error += $"家具 {obj.LogDisplayName} ({obj.ID}) 的交互点 {index} 应该放置有家具 {pair.Value}) 但是目标不存在 \n";
                                continue;
                            }
                            if (target.InteractionPoint == null)
                            {
                                hasError = true;
                                error += $"家具 {obj.LogDisplayName} ({obj.ID}) 的交互点 {index} 应该放置有家具 {target.LogDisplayName}) 但是目标的 mount 信息为空 \n";
                                continue;
                            }
                            if (target.InteractionPoint.TargetID != obj.ID)
                            {
                                hasError = true;
                                error += $"家具 {obj.LogDisplayName} ({obj.ID}) 的交互点 {index} 应该放置有家具 {target.LogDisplayName}) 但是目标的 mount parent 为 {target.InteractionPoint.TargetID} \n";
                                continue;
                            }
                            if (target.InteractionPoint.TargetIndex != index)
                            {
                                hasError = true;
                                error += $"家具 {obj.LogDisplayName} ({obj.ID}) 的交互点 {index} 应该放置有家具 {target.LogDisplayName}) 但是目标的 mount index 为 {target.InteractionPoint.TargetIndex} \n";
                                continue;
                            }

                        }
                    }
                }
            }
            if (hasError)
            {
                Character.ShowMessage(error);
            }
            else
            {
                Character.ShowTipsMessage(new PicaLocalityString() { DefaultString = "布置自检通过" });
            }
        }

        /// <summary>
        /// 注意这个调用只会出现在放置下来的时候，因为如果设置的时候家具已经在场内，则会立刻调用 Function.Use().
        /// </summary>
        /// <param name="message"></param>
        /// <param name="so"></param>
        private void ProcessAdditionalData(IDEditMessage message, SceneObject so)
        {
            var func = so.GetFunction<UsableSceneObjectWithSettingFunction>();
            if (func == null)
            {
                //Logger.Error.Write($"物件[{so.ID}][{so.Type}]收到设置消息但没有设置相关 Function");
                return;
            }
            foreach (var data in message.AdditionalMessages)
            {
                func.Use(Character.SceneCharacter, so, ProtoSerializer.Serialize(data));
            }
        }

        public void RecycleAllFurnis()
        {
            //这个先不要了

            //var scene = Character.SceneCharacter.CurrentScene;
            //if (scene.OwnerID != Character.ID)
            //{
            //    Logger.Error.Write("Character is not Owner while RecycleAllFurnis");
            //    return;
            //}

            //if(scene.Dimension.Type != DimensionType.PicaRoom)
            //{
            //    Logger.Error.Write("Scene is not a room while RecycleAllFurnis");
            //    return;
            //}

            //(scene as PicaRoomScene).RecycleAllFurnis();

        }

        private void DoMount(SceneObject target, Dictionary<int, long> MountIDs, ref SceneSceneObjectActionStatusDataList req)
        {
            foreach (var p in MountIDs)
            {
                var i = p.Key;
                if (MountIDs[i] == 0)
                {
                    if (!target.InteractionSceneObjects.ContainsKey(i))
                    {
                        // 说明已经被之前的 mount 移除掉了
                        continue;
                    }
                    var sob = target.Area.FindObject(target.InteractionSceneObjects[i]);
                    if (sob == null)
                    {
                        target.Umount(i);
                    }
                    else if (target.Umount(sob))
                    {
                        req.ActionStatusDataList.Add(new IDActionStatusData()
                        {
                            Id = sob.ID,
                            TargetData = sob.ActionStatusData
                        });
                    }
                    continue;
                }

                var so = target.Area.FindObject(MountIDs[i]);
                if (so == null)
                {
                    Logger.GameError.Write($"{nameof(DoMount)}, Can not find so [{MountIDs[i]}] by mount " + Environment.StackTrace);
                    continue;
                }
                if (target.Mount(so, i, true))
                {
                    req.ActionStatusDataList.Add(new IDActionStatusData()
                    {
                        Id = so.ID,
                        TargetData = so.ActionStatusData
                    });
                }
            }
        }

        //public void PlaceSceneObject(PlaceSceneObjectRequest psor)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    var area = scene.FindArea(psor.AreaCoordinate.ToIntVector2());
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    var p = psor.Transform.Position;
        //    var so = Character.FindEntity<SceneObject>(psor.Id);
        //    var error = CheckPlace(area, so, Transform.FromTransformData(psor.Transform));
        //    if (error != null)
        //    {
        //        ShowError(error);
        //        return;
        //    }
        //    DoPlace(area, so, psor.Transform, psor.Group, );
        //}

        #endregion 

        /// <summary>
        /// 玩家先占一个地方，然后可以选择一个模版，然后这个就变成了选择的家具
        /// </summary>
        /// <param name="req"></param>

        public void SelectFalseworkBuildingTemplate(SceneAreaIDInt64Message req)
        {
            var scene = Character.SceneCharacter.CurrentScene;
            if (req.SceneID != scene.ID)
            {
                ShowError(LocalityStrings.operationError);
                return;
            }
            var area = scene.FindArea(req.AreaID);
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }
            var obj = area.FindObject(req.ID);
            if (obj == null)
            {
                ShowError(LocalityStrings.targetObjectNotFound);
                return;
            }
            if (obj.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            var function = obj.GetFunction<FalseworkBuildingFunction>();
            if (function == null)
            {
                ShowError(LocalityStrings.limitedTargetStatus);
                return;
            }
            var option = function.Templates.Find(p => p.Type == req.Value);
            if (option == null)
            {
                ShowError(LocalityStrings.targetObjectNotFound);
                return;
            }
            var template = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(option.Type);
            if (template == null || !template.Available)
            {
                ShowError(LocalityStrings.unknownError);
                return;
            }
            //if (!CanAddWeight(area,template.Weight ))
            //{
            //    ShowError(LocalityStrings.limitedSceneWeight);
            //    return;
            //}
            if (template.BuildingQuota > area.LeftBuildingQuota)
            {
                ShowError(LocalityStrings.lackBuildingQuota);
                return;
            }
            //if (template.RequestAtmospheres?.Count > 0)
            //{
            //    int value;
            //    Atmosphere atmosphere;
            //    foreach (var request in template.RequestAtmospheres)
            //    {
            //        atmosphere = area.GetAtmosphere(request.Category);
            //        if (atmosphere == null || atmosphere.Value < request.Value)
            //        {
            //            //建造该建筑需要{0}氛围{1}点，当前{2}点，氛围不足，无法建造
            //            value = atmosphere?.Value ?? 0;
            //            ShowError(LocalityStrings.lackOfAtmosphere, request.Category.ToString(), request.Value, value);
            //            return;
            //        }
            //    }
            //}
            if (!template.CanPlace(area, out LocalityString error))
            {
                ShowError(error);
                return;
            }
            if (!area.Scene.CanPlace(template, out error))
            {
                ShowError(error);
                return;
            }

            area.Remove(obj);
            Character.OnEntityChanged(obj, obj.Count, 0, nameof(SelectFalseworkBuildingTemplate));

            obj.Type = option.Type;
            obj.TypeData = null;
            obj.CreateSuccess();
            obj.Initialize();
            obj.Save();
            area.Add(obj);
            Character.OnEntityChanged(obj, 0, obj.Count, nameof(SelectFalseworkBuildingTemplate));
        }

        private bool CanPlace(Area area, SceneObject so, SceneObjectType st, Transform transform, double minRadius, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if ((int)Character.AdminType < 4)
            {
                //if (!area.LocalContains(transform.Position.X, transform.Position.Z))
                //{
                //    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.coordinateError);
                //    return false;
                //}
                //if (st.Category == 0)
                //{
                //    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedBuyPlaceScene);
                //    return false;
                //}
                //if (!st.IsFitDimension(area.Scene.TypeData))
                //{
                //    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedBuyPlaceScene);
                //    return false;
                //}
                //if (!st.IsFitScene(area.Scene.Type))
                //{
                //    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedBuyPlaceScene);
                //    return false;
                //}
                //if (!area.CanAddWeight(st.Weight))
                //{
                //    if (mode != ClientNotificationMode.Ignore) 
                //        ShowError(LocalityStrings.limitedSceneWeight);
                //    return false;
                //}
                if (st.BuildingQuota > area.LeftBuildingQuota)
                {
                    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.lackBuildingQuota);
                    return false;
                }
                //if (st.RequiredMemberLv > Character.MemberLv)
                //{
                //    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedMemberLv);
                //    return false;
                //}
                //if (st.RequestAtmospheres?.Count > 0)
                //{
                //    int value;
                //    Atmosphere atmosphere;
                //    foreach (var request in st.RequestAtmospheres)
                //    {
                //        atmosphere = area.GetAtmosphere(request.Category);
                //        if (atmosphere == null || atmosphere.Value < request.Value)
                //        {
                //            if (mode != ClientNotificationMode.Ignore)
                //            {
                //                //建造该建筑需要{0}氛围{1}点，当前{2}点，氛围不足，无法建造
                //                value = atmosphere?.Value ?? 0;
                //                ShowError(LocalityStrings.lackOfAtmosphere, request.Category.ToString(), request.Value, value);
                //            }

                //            return false;
                //        }
                //    }
                //}
            }
            if (so != null)
            {
                if (so.Deleted || so.Count <= 0)
                {
                    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedTargetStatus);
                    return false;
                }
                if (!Character.IsInBag(so, area.Scene))
                {
                    if (mode != ClientNotificationMode.Ignore) ShowError(LocalityStrings.limitedPlaceScene);
                    return false;
                }
            }
            if (!st.CanPlace(area, out LocalityString error))
            {
                if (mode != ClientNotificationMode.Ignore) ShowError(error);
                return false;
            }

            if (!area.Scene.CanPlace(st, out error))
            {
                if (mode != ClientNotificationMode.Ignore) ShowError(error);
                return false;
            }
            //if (!CheckAreasSceneObjectOverlay(st, transform.Position, area, minRadius, 0))
            //{
            //    if (mode != ClientNotificationMode.Ignore) ShowTipsMessage(LocalityStrings.coordinateOverlay);
            //    return false;
            //}

            return true;
        }

        protected bool placeToOwner = false;

        private SceneObject DoPlace(Area area, SceneObject so, TransformData tfd, short group, PlaceSceneObjectBehavior behavior)
        {
            var res = behavior.RemoveSceneObjectFromVault(Character, so);
            res.Group = group;
            if (tfd != null) res.Transform = Transform.FromTransformData(tfd);
            res.Save();
            area.Add(res);
            //GameApplication.ObjectManager.AddObjectQuantityRecord(so.TypeData, so, area, Character, 1);
            OnAreaObjetChanged(area);
            return res;
        }

        //private SceneObject DoPlaceFromVault(Area area, SceneObject so, TransformData tfd, short group)
        //{

        //}

        //public void PickUpSceneObject(SceneAreaIDMessage saim)
        //{
        //    var area = FindArea(saim);
        //    if (area == null || area.SceneID != Character.SceneCharacter.CurrentArea.SceneID)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    var so = area.FindObject(saim.Id);
        //    if (so == null)
        //    {
        //        return;
        //    }
        //    var error = CheckPickUp(area, so);
        //    if (error != null)
        //    {
        //        ShowError(error);
        //        return;
        //    }
        //    DoPickUp(area, so);
        //}


        int transformErrorTimes = 0;
        public void TransformSceneObject(TransformSceneObjectRequest request)
        {
            var oldArea = Character.SceneCharacter.CurrentArea.Scene.FindArea(request.OldAreaID);
            var newArea = Character.SceneCharacter.CurrentArea.Scene.FindArea(request.NewAreaCoordinate.ToIntVector2());
            if (oldArea == null || newArea == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }
            //if ((int)Character.AdminType < 5 && oldArea.OwnerID != Character.ID)
            //{
            //    ShowError(LocalityStrings.limitedAuthority);
            //    return;
            //}
            //if ((int)Character.AdminType < 5 && newArea.OwnerID != Character.ID)
            //{
            //    ShowError(LocalityStrings.limitedAuthority);
            //    return;
            //}
            var p = request.Transform.Position;
            if (!newArea.LocalContains(p.X, p.Z))
            {
                ShowError(LocalityStrings.coordinateError);
                return;
            }
            if (Character.AdminType == 0 && oldArea.ID != newArea.ID && oldArea.SellStatus == SellStatus.OnSell)
            {
                ShowError(LocalityStrings.limitedActionOnAreaSellStatus);
                return;
            }
            var so = oldArea.FindObject(request.ObjectID);
            if (so == null)
            {
                transformErrorTimes++;
                if (transformErrorTimes > 1)
                {
                    CheckSceneObjectArea(request.OldAreaID, request.ObjectID);
                    transformErrorTimes = 0;
                }
                ShowIDNotFoundError(request.ObjectID);
                return;
            }
            if (!so.CanMove(out _))
            {
                ShowError(LocalityStrings.limitedStatus);
                return;
            }
            if (Character.AdminType == 0 && oldArea.ID != newArea.ID)
            {
                oldArea.ShowTransformSceneObject(so, so.Transform.ProtoData, oldArea);
                return;
            }
            var np = request.Transform.Position.ToVector3();
            //if (!CheckAreasSceneObjectOverlay(so.TypeData, np, newArea, request.MinRadius, so.ID))
            //{
            //    oldArea.ShowTransformSceneObject(so, so.Transform.ProtoData, oldArea);
            //    ShowTipsMessage(LocalityStrings.coordinateOverlay);
            //    return;
            //}

            so.Group = request.Group;
            if (oldArea.ID != newArea.ID)
            {
                oldArea.Remove(so, ClientNotificationMode.Ignore);
                newArea.Add(so, false);
                OnAreaObjetChanged(oldArea);
            }

            newArea.TransformObject(so, request.Transform, oldArea);
            OnAreaObjetChanged(newArea);
            transformErrorTimes = 0;
        }

        void CheckSceneObjectArea(long areaID, long objectID)
        {
            var scene = Character.SceneCharacter.CurrentArea.Scene;
            var so = GameApplication.DataManager.FindValue<EntityBase, SceneObject>(objectID);
            if (so != null)
            {
                CheckSceneObjectAreaRequest csoar = new CheckSceneObjectAreaRequest();
                csoar.SceneID = scene.ID;
                csoar.ClientAreaID = areaID;
                csoar.ServiceAreaID = so.AreaID;
                csoar.ObjectID = so.ID;
                scene.SendToSceneService(SceneManagerClientMethod.CheckSceneObjectArea, csoar);
            }
        }

        public void ChangeSceneObjectStatus(IDSceneObjectStatusData isosd)
        {
            var area = Character.SceneCharacter.CurrentArea;
            var so = area?.FindObject(isosd.Id);
            if (so == null)
            {
                ShowError(LocalityStrings.targetObjectNotFound);
                return;
            }
            if (so.StatusData == null)
            {
                so.StatusData = new SceneObjectStatus();
            }

            so.StatusData.Status = isosd.StatusData.Status;
            so.StatusData.Animation = isosd.StatusData.Animation;
            so.Save();
            SceneAreaSceneObjectStatusData sisosd = new SceneAreaSceneObjectStatusData()
            {
                SceneID = so.Area.SceneID,
                AreaID = so.AreaID,
                IdStatusData = isosd
            };

            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.ChangeSceneObjectStatus, sisosd);
        }

        //public void DestroyAllSceneObject(SceneAreaMessage req)
        //{
        //    var scene = GameApplication.DataManager.FindValue<Scene>(req.SceneID);
        //    if (scene == null)
        //    {
        //        ShowError(LocalityStrings.targetSceneNotFound);
        //        return;
        //    }
        //    var area = scene.FindArea(req.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (area.OwnerID != Character.ID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    if (!area.Scene.TypeData.IsMainDimension)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    if (Character.AdminType == 0 && area.SellStatus == SellStatus.OnSell)
        //    {
        //        ShowError(LocalityStrings.limitedActionOnAreaSellStatus);
        //        return;
        //    }

        //    /* 1、该建筑是玩家自己的
        //     * 2、该建筑可以被删除CanDelete = 1 (YL 移除）
        //     * 3、该建筑DestroyReturnNashRate = 0
        //     */
        //    foreach (var so in area.FindObject(p => p.OwnerID == Character.ID && p.TypeData.DestroyReturnNashRate == 0))
        //    {
        //        if (so.CanDestroy(out LocalityString error))
        //        {
        //            so.Delete(so.Count, nameof(DestroySceneObject), string.Empty, area.ID);

        //            var rr = RowAdapter.Create<SceneObjectDestroyRecord>(cache: false);
        //            rr.CreateSuccess();
        //            rr.Date = DateTimeExtension.Now;
        //            rr.OwnerID = Character.ID;
        //            rr.AreaID = area.ID;
        //            rr.ObjectID = so.ID;
        //            rr.Type = so.Type;
        //            rr.BuyNash = so.TypeData.BuyPrice;
        //            rr.DestroyNash = 0;
        //            rr.Action = nameof(DestroyAllSceneObject);
        //            rr.Initialize();
        //            rr.Save();
        //        }
        //    }

        //    area.UpdateCacheVersionToSceneServer();
        //}

        public void PickUpAllSceneObject(SceneAreaMessage req)
        {
            var scene = GameApplication.DataManager.FindValue<Scene>(req.SceneID);
            if (scene == null)
            {
                ShowError(LocalityStrings.targetSceneNotFound);
                return;
            }
            var area = scene.FindArea(req.AreaID);
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }
            if (area.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.notOwner);
                return;
            }
            //if (!area.Scene.TypeData.IsMainDimension)
            //{
            //    ShowError(LocalityStrings.limitedTargetStatus);
            //    return;
            //}
            if (Character.AdminType == 0 && area.SellStatus == SellStatus.OnSell)
            {
                ShowError(LocalityStrings.limitedActionOnAreaSellStatus);
                return;
            }
            if (Character.LeftBackpackCapacity <= 0)
            {
                //没有可叠加的分类
                ShowError(LocalityStrings.limitedBackpackCapacity);
                return;
            }

            area.ClearSceneObjects(Character);
            OnAreaObjetChanged(area);
        }

        //public void PlaceAllSceneObject(PlaceAllSceneObjectRequest req)
        //{
        //    var scene = Character.SceneCharacter.CurrentScene;
        //    if (scene.TypeData.DimensionType != DimensionType.MainWorld)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    var area = scene.FindArea(req.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (area.OwnerID != Character.ID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    if (Character.AdminType == 0 && area.SellStatus == SellStatus.OnSell)
        //    {
        //        ShowError(LocalityStrings.limitedActionOnAreaSellStatus);
        //        return;
        //    }
        //    var sot = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(req.Type);
        //    if (sot == null)
        //    {
        //        ShowError(LocalityStrings.targetObjectNotFound);
        //        return;
        //    }
        //    if (!sot.AutoPlacement)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    if ((int)Character.AdminType < 5)
        //    {
        //        if (!sot.IsFitDimension(area.Scene.TypeData))
        //        {
        //            ShowError(LocalityStrings.limitedBuyPlaceScene);
        //            return;
        //        }
        //        if (!sot.IsFitScene(area.Scene.Type))
        //        {
        //            ShowError(LocalityStrings.limitedBuyPlaceScene);
        //            return;
        //        }
        //    }
        //    if (area.ObjectCount > 0)
        //    {
        //        ShowError(LocalityStrings.limitedEmptyArea);
        //        return;
        //    }
        //    var objList = Character.FindEntities<SceneObject>(p => p.Type == req.Type && p.AreaID == 0, true);
        //    if (objList.Count == 0)
        //    {
        //        //没有可摆放的建筑
        //        return;
        //    }

        //    var areaPosition = area.StartPosition;
        //    var areaRange = scene.TypeData.AreaRange;
        //    var areaRectangle = new Rect(areaPosition.X, areaPosition.Z, areaRange, areaRange);

        //    var newSize = new Size(Math.Max(sot.Size.X, req.Size.X), Math.Max(sot.Size.Z, req.Size.Z));
        //    var newPosition = new Vector2(areaPosition.X, areaPosition.Z);
        //    foreach (var obj in objList)
        //    {
        //        var objCount = obj.Count;
        //        while (objCount > 0)
        //        {
        //            var newRectangle = new Rect(newPosition, newSize);
        //            if (newRectangle.Width > areaRectangle.Width)
        //            {
        //                newPosition = new Vector2(areaPosition.X, newPosition.Y + newSize.Height);
        //                newRectangle = new Rect(newPosition, newSize);
        //                if (newRectangle.Height > areaRectangle.Height)
        //                {
        //                    //超出地块范围
        //                    return;
        //                }
        //            }

        //            var transform = new Transform();
        //            transform.Scale = Vector3.One;
        //            transform.Rotation = Quaternion.Identity;
        //            transform.Position = new Vector3(newPosition.X + newSize.Width * 0.5f, 0, newPosition.Y + newSize.Height * 0.5f);//建筑位置是中心点
        //            if (!CanPlace(area, obj, sot, transform, req.MinRadius, ClientNotificationMode.Ignore))
        //            {
        //                //条件不满足
        //                return;
        //            }

        //            DoPlace(area, obj, transform.ProtoData, req.Group);

        //            objCount--;
        //            newPosition += new Vector2(newSize.Width, 0);
        //        }
        //    }

        //    OnAreaObjetChanged(area);
        //}
        #endregion SceneObject

        #region Area

        public void GetAreaSpecGroup(GetAreaSpecGroupRequest request)
        {
            var scene = GameApplication.SceneManager.GetScene(request.SceneID, true);
            if (scene == null)
            {
                ShowError(LocalityStrings.targetSceneNotFound);
                return;
            }

            scene.GetAreaSpecGroup(User, request);
        }

        public void GetAreaDetail(SceneAreaMessage sam)
        {
            var scene = GameApplication.SceneManager.GetScene(sam.SceneID, true);
            if (scene == null)
            {
                ShowError(LocalityStrings.targetSceneNotFound);
                return;
            }
            var area = scene.FindArea(sam.AreaID);
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }

            User.SendGoogleProto(ClientMethod.ShowAreaDetail, area.DetailData);
        }

        public void GetAreaSummary()
        {
            var area = Character.SceneCharacter.CurrentArea;
            var rsp = new AreaSummaryData();
            rsp.ID = area.ID;
            if (area.OwnerID != 0)
            {
                rsp.OwnerLv = area.Owner.Lv;
            }

            SendGoogleProto(ClientMethod.ShowAreaSummary, rsp);
        }

        protected void OnAreaChanged(Area area)
        {
            area.Save();
            area.UpdateDetailToClient();
            User.SendGoogleProto(ClientMethod.ShowAreaDetail, area.DetailData);
            ProtoExtension.BroadCastByPanel(PanelCode.SceneMapPanel, ClientMethod.UpdateAreaSpec, area.SpecData);
        }

        public void EditorAreasName(IDStringMessageList list)
        {
            IDStringMessageList message = new IDStringMessageList();
            Dictionary<Area, string> dict = new Dictionary<Area, string>();
            foreach (var item in list.IdStrList)
            {
                var area = GameApplication.DataManager.FindValue<Area>(item.Id);
                if (area == null)
                {
                    ShowError(LocalityStrings.targetAreaNotFound);
                    return;
                }
                if (area.OwnerID != Character.ID)
                {
                    ShowError(LocalityStrings.limitedAuthority);
                    return;
                }
                if (area.SellStatus != SellStatus.NotForSell && Character.ID != area.OwnerID)
                {
                    //租个地而已，还想把地卖了不成
                    ShowError(LocalityStrings.notOwner);
                    return;
                }
                if (DateTimeExtension.Now < area.StopChangeNameTime ||
                   DateTimeExtension.Now < Character.StopChangeAreaNameTime)
                {
                    ShowError(LocalityStrings.GetUndefinedString("由于玩家举报，您暂时无法修改土地名称。"));
                    return;
                }
                if (item.Value == null)
                {
                    ShowError(LocalityStrings.argumentError);
                    return;
                }
                dict.Add(area, item.Value);
            }
            foreach (var item in dict.Keys)
            {
                item.Name = dict[item];
                item.Save();
                OnAreaChanged(item);
                message.IdStrList.Add(new IDStringMessage() { Id = item.ID, Value = item.Name });

            }
            User.SendGoogleProto(ClientMethod.EditorAreasName, message);
        }

        public void EditorArea(EditorAreaRequest req)
        {
            var area = GameApplication.DataManager.FindValue<Area>(req.Id);
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }
            if (area.OwnerID != Character.ID)
            {
                ShowError(LocalityStrings.limitedAuthority);
                return;
            }
            if (req.HasName)
            {
                if (string.IsNullOrEmpty(req.Name))
                {
                    ShowError(LocalityStrings.nameIllegal);
                    return;
                }
                if (req.Name.Length > GameConfig.BaseConfig.MaxAreaNameLength)
                {
                    ShowError(LocalityStrings.argumentOutOfRangeError);
                    return;
                }
                if (DateTimeExtension.Now < area.StopChangeNameTime || DateTimeExtension.Now < Character.StopChangeAreaNameTime)
                {
                    ShowError(LocalityStrings.GetUndefinedString("由于玩家举报，您暂时无法修改土地名称。"));
                    return;
                }

                area.Name = req.Name;
            }
            if (req.HasDescription)
            {
                if (req.Description.Length > GameConfig.BaseConfig.MaxAreaDescriptionLength)
                {
                    ShowError(LocalityStrings.argumentOutOfRangeError);
                    return;
                }

                area.Description = req.Description;
            }
            lock (area)
            {
                if (area.OwnerID != Character.ID)
                {
                    //已被别人凭租
                    return;
                }
                if (req.HasSellStatus)
                {
                    var setting = area.Scene.Setting;
                    switch (req.SellStatus)
                    {
                        case SellStatus.OnSell:
                            if (!area.Scene.TypeData.IsMainDimension)
                            {
                                ShowError(LocalityStrings.operationError);
                                return;
                            }
                            if (area.ActiveObjectCount > 0)
                            {
                                ShowError(LocalityStrings.operationError);
                                return;
                            }
                            if (DateTimeExtension.Now < area.NextSellTime)
                            {
                                var duration = area.NextSellTime - DateTimeExtension.Now;
                                ShowError(LocalityStrings.limitedCDHours, Math.Round(duration.TotalHours, 1));
                                return;
                            }
                            if (req.SellPrice < setting.MinAreaSellPrice || req.SellPrice > setting.MaxAreaSellPrice)
                            {
                                ShowError(LocalityStrings.argumentError);
                                return;
                            }
                            break;
                        case SellStatus.StopSelling:
                            switch (area.SellStatus)
                            {
                                case SellStatus.OnSell:
                                    area.NextSellTime = DateTimeExtension.Now + setting.AreaSellCD;
                                    break;
                            }
                            break;
                        default:
                            ShowError(LocalityStrings.argumentError);
                            return;
                    }

                    area.SellStatus = req.SellStatus;
                    area.SellPrice = req.SellPrice;
                    area.Save();
                    User?.SendGoogleProto(ClientMethod.SellAreaSuccess, area.DetailData);
                }
            }

            OnAreaChanged(area);
        }

        public void TryJumpToArea(JumpAreaRequest jar)
        {
            var scene = GameApplication.DataManager.FindValue<Scene>(jar.SceneID);
            if (scene == null)
            {
                ShowError(LocalityStrings.targetSceneNotFound);
                return;
            }
            var area = scene.FindArea(jar.AreaCoordinate.ToIntVector2(), true);
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }

            User.SendGoogleProto(ClientMethod.TryJumpToArea, area.DetailData);
        }

        public void DoJumpToArea(SceneActorEnterAreaRequest saear)
        {
            if (Character.SceneCharacter.RoleChanged)
            {
                ShowError(LocalityStrings.limitedTransport);
                return;
            }
            var oldscene = Character.SceneCharacter.CurrentScene;
            var newscene = GameApplication.DataManager.FindValue<Scene>(saear.SceneID);
            if (newscene == null)
            {
                ShowError(LocalityStrings.targetSceneNotFound);
                return;
            }
            if (newscene.TypeData.DimensionGroup != oldscene.TypeData.DimensionGroup)
            {
                ShowError(LocalityStrings.limitedStatus);
                return;
            }
            var newarea = newscene.FindArea(saear.EnterAreaRequest.NewArea.Coordinate.ToIntVector2());
            if (newarea == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }

            Area oldarea;
            var needCurrency = 0;
            if (oldscene.ID != newscene.ID)
            {
                needCurrency = GameConfig.BaseConfig.TransportSceneCurrency;
                oldarea = newscene.FindArea(newscene.TypeData.GetCenterArea());
            }
            else
            {
                oldarea = Character.SceneCharacter.CurrentArea;
            }
            var dx = newarea.X - oldarea.X;
            var dy = newarea.Y - oldarea.Y;
            var distance = Math.Sqrt(dx * dx + dy * dy);
            needCurrency += (int)Math.Ceiling(distance * newscene.Setting.TransportAreaCurrency);
            if (needCurrency > 0)
            {
                if (!Character.CheckVirtualCurrencyCountEnough(GameConfig.BaseConfig.CoinType, needCurrency))
                {
                    ShowError(LocalityStrings.lackOfCurrency);
                    return;
                }

                Character.Pay(GameConfig.BaseConfig.CoinType, needCurrency, nameof(DoJumpToArea), $"{newscene.ID}-{newarea.Coordinate}");
            }

            saear.ActorID = Character.SceneCharacter.ID;
            var isNewScene = Character.SceneCharacter.CurrentArea.SceneID != saear.SceneID;
            if (isNewScene)
            {
                newscene.Enter(Character.SceneCharacter, newarea.ID);
            }
            else
            {
                saear.EnterAreaRequest.Transform = newarea.GetEntranceTransformData(saear.EnterAreaRequest.Transform);
                newscene.SendToSceneService(SceneManagerClientMethod.TransportActorToArea, saear);
            }
        }

        public void GetMyAreas(GetMyAreasRequest req)
        {
            if (req.StartIndex < 0 || req.Count < 0)
            {
                ShowError(LocalityStrings.argumentOutOfRangeError);
                return;
            }

            List<Area> areas;
            if (req.SceneID != 0)
            {
                var scene = GameApplication.SceneManager.GetScene(req.SceneID, true);
                if (scene == null)
                {
                    ShowError(LocalityStrings.targetSceneNotFound);
                    return;
                }

                areas = SearchFromScene(scene);
            }
            else
            {
                var currentScene = Character.SceneCharacter.CurrentScene;
                var scenes = GameApplication.DataManager.FindValue<Scene>(p => p.TypeData.DimensionType == currentScene.TypeData.DimensionType && p.TypeData.IsMainDimension);
                areas = scenes.SelectMany(p => SearchFromScene(p)).ToList();
            }

            var rsp = new AreaItemDataList();
            rsp.TotalCount = areas.Count;
            if (req.StartIndex < areas.Count)
            {
                var endSentinel = Math.Min(req.StartIndex + req.Count, areas.Count);
                for (int i = req.StartIndex; i < endSentinel; i++)
                {
                    rsp.Datas.Add(areas[i].ItemData);
                }
            }

            User.SendGoogleProto(ClientMethod.ShowMyAreas, rsp);

            List<Area> SearchFromScene(Scene scene)
            {
                return scene.FindAreas(p => p.OwnerID == Character.ID);
            }
        }

        public void ChangeEntranceSettings(EntranceData esd)
        {
            var area = Character.SceneCharacter.CurrentArea;
            if (Character.ID != area.OwnerID && (int)Character.AdminType < 5)
            {
                ShowError(LocalityStrings.limitedAuthority);
                return;
            }
            var est = Transform.FromTransformData(esd.EntranceTransform);
            if (!area.LocalContains(est.Position.X, est.Position.Z))
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            if (esd.CameraTransform != null)
            {
                var cst = Transform.FromTransformData(esd.CameraTransform);
                area.CameraTransform = cst;
            }

            area.EntranceTransform = est;
            area.ClearCache();
            area.Save();
            area.SendToSceneService(SceneManagerClientMethod.UpdateAreaDetailData, area.DetailData);
            ShowTipsMessage(LocalityStrings.changeSuccess);
        }

        /// <summary>
        /// 土地升级
        /// </summary>
        //public void AreaUpgrade(IDMessage req)
        //{
        //    var scene = Character.SceneCharacter.CurrentScene;
        //    var area = scene.FindArea(req.Id);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (area.OwnerID != Character.ID && !scene.Any<SceneAdventureMissionFunctionRecord>(p => p.CharacterID == Character.ID))
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    if (area.Type == 0)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    var setting = area.TypeData.LvSettings?.Find(p => p.Lv == area.Lv);
        //    if (setting == null || area.Exp < setting.Exp)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    if (Character.Lv < setting.MinCharacterLv)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    var nextLv = area.Lv + 1;
        //    var nextLvSetting = area.TypeData.LvSettings?.Find(p => p.Lv == nextLv);
        //    if (nextLvSetting == null)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    var record = scene.GetFunctionRecord<SceneAreaUpgradingFunctionRecord>(false);
        //    if (record != null && nextLv > record.MaxLv)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }

        //    area.Upgrade();
        //}

        #region BlockObject

        //public void AddBlockObjects(SceneAreaBlockObjectListData sabod)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    //if (scene.TypeData.DimensionType == DimensionType.MainWorld)
        //    //{
        //    //    ShowError(LocalityStrings.operationError);
        //    //    return;
        //    //}
        //    var area = scene.FindArea(sabod.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (Character.AdminType < AdminType.超级 && Character.ID != area.OwnerID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    int count = sabod.Datas.Count;
        //    for (int i = 0; i < count; i++)
        //    {
        //        if(i<0)
        //        {
        //            break;
        //        }
        //        var data = sabod.Datas[i];
        //        var totalWeight = IsBlockObjectAvailable(data, area);
        //        if (totalWeight < 0)
        //        {
        //            sabod.Datas.RemoveAt(i);
        //            i--;
        //            count--;
        //            continue;
        //        }

        //        if(!area.AddBlockObject(data, totalWeight))
        //        {
        //            sabod.Datas.RemoveAt(i);
        //            i--;
        //            count--;
        //            continue;
        //        }
        //    }
        //    area.SendToSceneService(SceneManagerClientMethod.AddBlockObjects, sabod);
        //    OnAreaObjetChanged(area);
        //    RecordManager.AddNewBlockRecord(area, sabod.Datas);
        //}

        //public void UpdateBlockObjects(SceneAreaBlockObjectListData sabod)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    var area = scene.FindArea(sabod.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (Character.AdminType < AdminType.超级 && Character.ID != area.OwnerID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    int count = sabod.Datas.Count;
        //    for (int i = 0; i < count; i++)
        //    {
        //        if (i < 0)
        //        {
        //            break;
        //        }
        //        var data = sabod.Datas[i];
        //        var totalWeight = IsBlockObjectAvailable(data, area);
        //        if (totalWeight<0)
        //        {
        //            sabod.Datas.RemoveAt(i);
        //            i--;
        //            count--;
        //            continue;
        //        }
        //        if(!area.UpdateBlockObject(data))
        //        {
        //            sabod.Datas.RemoveAt(i);
        //            i--;
        //            count--;
        //            continue;
        //        }
        //    }
        //    OnAreaObjetChanged(area);
        //    area.SendToSceneService(SceneManagerClientMethod.UpdateBlockObjects, sabod);
        //    RecordManager.AddUpdateBlockRecord(area,sabod.Datas);
        //}

        //public void GetAreaTotalWeight(IDMessage im)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    var area = scene.FindArea(im.Id);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    UpdateAreaWeight(area);
        //}

        protected void OnAreaObjetChanged(Area area)
        {
            //UpdateAreaWeight(area);
            area.UpdateCacheVersionToSceneServer();
        }

        //protected void UpdateAreaWeight(Area area)
        //{
        //    var im = new IDInt32Message();
        //    im.ID = area.ID;
        //    im.Value = area.TotalWeight;
        //    SendGoogleProto(ClientMethod.UpdateAreaWeight, im);
        //}

        //int IsBlockObjectAvailable(BlockObjectData data,Area area)
        //{
        //    int totalWeight = data.TotalWeight;  
        //    if(totalWeight<0)
        //    {
        //        return -1;
        //    }

        //    if (!CanAddWeight(area, totalWeight))
        //    {
        //        ShowError(LocalityStrings.limitedSceneWeight);
        //        return -1;
        //    }
        //    return totalWeight;
        //}

        //bool CanAddWeight(Area area,int totalWeight)
        //{
        //    if ((int)Character.AdminType >= 3)
        //    {
        //        return true;
        //    }
        //    return area != null&&area.CanAddWeight(totalWeight);
        //}

        //public void RemoveBlockObjects(SceneAreaCoordinate3ListMessage sac3m)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    //if (scene.TypeData.DimensionType == DimensionType.MainWorld)
        //    //{
        //    //    ShowError(LocalityStrings.operationError);
        //    //    return;
        //    //}
        //    var area = scene.FindArea(sac3m.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (Character.AdminType < AdminType.超级 && Character.ID != area.OwnerID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    foreach (var data in sac3m.Datas)
        //    {
        //        area.RemoveBlockObject(data);
        //    }

        //    area.SendToSceneService(SceneManagerClientMethod.RemoveBlockObjects, sac3m);
        //    OnAreaObjetChanged(area);

        //    RecordManager.AddRemoveBlockRecord(area, sac3m.Datas);
        //}

        //public void TransformBlockObjects(SceneAreaTransformBlockObjectListData satbold)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    //if (scene.TypeData.DimensionType == DimensionType.MainWorld)
        //    //{
        //    //    ShowError(LocalityStrings.operationError);
        //    //    return;
        //    //}
        //    var area = scene.FindArea(satbold.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (Character.AdminType < AdminType.超级 && Character.ID != area.OwnerID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    for (int i = 0; i < satbold.Datas.Count; i++)
        //    {
        //        var tbod = satbold.Datas[i];
        //        var key = new IntVector3(tbod.OldX, tbod.OldY, tbod.OldZ);
        //        if (!area.AreaStructure.HaveBlock(key))
        //        {
        //            continue ;
        //        }

        //        area.AreaStructure.TransformBlockObject(key, tbod);
        //    }

        //    area.SendToSceneService(SceneManagerClientMethod.TransformBlockObjects, satbold);
        //    OnAreaObjetChanged(area);
        //    RecordManager.AddTransformBlockRecord(area, satbold.Datas);
        //}

        //public void ClearBlockObjects(SceneAreaMessage sam)
        //{
        //    var scene = Character.SceneCharacter.CurrentArea.Scene;
        //    if (scene.TypeData.DimensionType == DimensionType.MainWorld)
        //    {
        //        ShowError(LocalityStrings.operationError);
        //        return;
        //    }
        //    var area = scene.FindArea(sam.AreaID);
        //    if (area == null)
        //    {
        //        ShowError(LocalityStrings.targetAreaNotFound);
        //        return;
        //    }
        //    if (Character.AdminType < AdminType.超级 && Character.ID != area.OwnerID)
        //    {
        //        ShowError(LocalityStrings.notOwner);
        //        return;
        //    }
        //    area.ClearBlockObjects();
        //    //area.SendToSceneService(SceneManagerClientMethod.ClearBlockObjects, sam);
        //}

        //public void AddOrUpdateBlockPrefabGroup(BlockPrefabGroupData bpgd)
        //{
        //    if (Character.BlockPrefabGroups.Count > Character.MemberSet.MaxBlockGroupCount)
        //    {
        //        ShowTipsMessage(LocalityStrings.maximumAmountLimit);
        //        return;
        //    }

        //    if (bpgd.Group<=0)
        //    {
        //        ShowError(LocalityStrings.argumentError);
        //        return;
        //    }

        //    var bpg = Character.BlockPrefabGroups.First(p=>p.Group== bpgd.Group&&p.Shape== bpgd.Shape);


        //    if (bpg == null)
        //    {
        //        bpg = Character.CreateBlockPrefabGroup(bpgd);
        //    }
        //    else
        //    {
        //        bpg.Update(bpgd);
        //    }

        //    User.SendGoogleProto(ClientMethod.AddOrUpdateBlockPrefabGroup, bpg.ProtoData);
        //}

        //public void DeleteBlockPrefabGroup(IDMessage im)
        //{
        //    var bpg = Character.BlockPrefabGroups.Find(im.Id);
        //    if (bpg == null)
        //    {
        //        ShowIDNotFoundError(im.Id);
        //        return;
        //    }
        //    bpg.Delete();
        //    Character.BlockPrefabGroups.Remove(bpg.ID);

        //    var rsp = new IDMessage();
        //    rsp.Id = bpg.ID;
        //    User.SendGoogleProto(ClientMethod.DeleteBlockPrefabGroup, rsp);
        //}

        //public void GetBlockPrefabGroupDatas()
        //{
        //    BlockPrefabGroupDatas bgds = new BlockPrefabGroupDatas();
        //    var bpg = Character.BlockPrefabGroups;
        //    if(bpg.Count==0)
        //    {
        //        var bpd = new BlockPrefabGroupData();
        //        bpd.Group = 1;
        //        bpd.Shape = 1001;
        //        Character.CreateBlockPrefabGroup(bpd);
        //    }

        //    foreach (var bg in Character.BlockPrefabGroups.Data.Values)
        //    {
        //        bgds.Groups.Add(bg.ProtoData);
        //    }
        //    var bpf = Character.BlockPrefabs;
        //    if(bpf.Count==1)
        //    {
        //        var bpd = new BlockPrefabData();
        //        bpd.Group = 1;
        //        bpd.Shape = 1001;
        //        bpd.Material = 1002;
        //        var mt = GameApplication.DataManager.FindValue<MaterialSpec>(1002);
        //        if(mt!=null)
        //        {
        //            foreach(var mp in mt.Properties)
        //            {
        //                var mpvd = new MaterialPropertyValueData();
        //                mpvd.Name = mp.Name;
        //                mpvd.Type = mp.Type;
        //                if (mp.Type == MaterialPropertyTypes.Color)
        //                {
        //                    mpvd.ColorValue = new Color4Data() { A = 255 };
        //                }
        //                else if (mp.Type == MaterialPropertyTypes.Float)
        //                {
        //                    mpvd.FloatValue = mp.DefaultFloatValue;
        //                }
        //                bpd.MaterialPropertyValues.Add(mpvd);
        //            }
        //        }
        //        Character.CreateBlockPrefab(bpd);
        //    }
        //    foreach (var bg in Character.BlockPrefabs.Data.Values)
        //    {
        //        bgds.PrefabDatas.Add(bg.ProtoData);
        //    }
        //    User.SendGoogleProto(ClientMethod.ShowBlockPrefabGroupDatas, bgds);
        //}

        //public void AddOrUpdateBlockPrefab(BlockPrefabData bpd)
        //{
        //    if (Character.BlockPrefabs.Count > Character.MemberSet.MaxBlockPrefabCount)
        //    {
        //        ShowError(LocalityStrings.maximumAmountLimit);
        //        return;
        //    }
        //    if (bpd.Group <= 0)
        //    {
        //        ShowError(LocalityStrings.argumentError);
        //        return;
        //    }

        //    var bp = Character.BlockPrefabs.Find(bpd.Id);
        //    if (bp == null)
        //    {
        //        bp = Character.CreateBlockPrefab(bpd);
        //    }
        //    else
        //    {
        //        bp.Update(bpd);
        //    }

        //    User.SendGoogleProto(ClientMethod.AddOrUpdateBlockPrefab, bp.ProtoData);
        //}

        //public void DeleteBlockPrefab(IDMessage im)
        //{
        //    var bp = Character.BlockPrefabs.Find(im.Id);
        //    if (bp == null)
        //    {
        //        ShowIDNotFoundError(im.Id);
        //        return;
        //    }
        //    bp.Delete();
        //    Character.BlockPrefabs.Remove(bp.ID);

        //    var rsp = new IDMessage();
        //    rsp.Id = bp.ID;
        //    User.SendGoogleProto(ClientMethod.DeleteBlockPrefab, rsp);
        //}
        #endregion

        #region AreaTemplate

        public void GetAreaTemplateSpecs(GetAreaTemplateSpecsRequest iim)
        {
            AreaTemplateSpecListData stsld = new AreaTemplateSpecListData();
            stsld.Group = iim.Group;
            if (iim.Group == 0)
            {
                var ats = GameApplication.DataManager.FindValue<AreaTemplateSpec>(p => p.ID > 0);
                foreach (var at in ats)
                {
                    stsld.Datas.Add(at.ProtoData);
                }
            }
            else if (iim.Group == 1)
            {
                var tt = Character.AreaTemplates;
                foreach (var at in tt.Data.Values)
                {
                    stsld.Datas.Add(at.ProtoData);
                }
            }
            User.SendGoogleProto(ClientMethod.ShowAreaTemplateSpecs, stsld);
        }

        public void PreviewAreaTemplate(PreviewAreaTemplateRequest patr)
        {
            if (patr.Group == 0)
            {
                var ats = GameApplication.DataManager.FindValue<AreaTemplateSpec>(patr.Id);
                if (ats != null)
                {
                    var td = ats.Data;
                    ShowPreviewAreaTemplate(ats.ID, td);
                }
            }
            else
            {
                var ats = Character.AreaTemplates.Find(patr.Id);
                if (ats != null)
                {
                    var td = ats.GetTemplateData();
                    ShowPreviewAreaTemplate(ats.ID, td);
                }
            }
        }

        protected void ShowPreviewAreaTemplate(long id, AreaStructureTemplateData td)
        {
            PreviewAreaTemplateData patd = new PreviewAreaTemplateData();
            patd.Id = id;
            patd.TemplateData = td;
            User.SendGoogleProto(ClientMethod.PreviewAreaTemplate, patd);
        }

        public void SaveAreaTemplate(SaveAreaTemplateRequest iim)
        {
            if (iim.Id == 0)
            {
                var at = Character.CreateAreaTemplate(iim.Name, iim.TemplateData);

                SendGoogleProto(ClientMethod.AddAreaTemplateSpec, at.ProtoData);
            }
            else
            {
                var at = Character.AreaTemplates.Find(iim.Id);
                if (at != null)
                {
                    at.Update(iim.Name, iim.TemplateData);
                }
                //SendGoogleProto(ClientMethod.AddAreaTemplateSpec, at.ProtoData);
            }

        }

        public void DeleteAreaTemplate(SaveAreaTemplateRequest iim)
        {
            if (iim.Id == 0)
            {
                var at = Character.CreateAreaTemplate(iim.Name, iim.TemplateData);

                SendGoogleProto(ClientMethod.AddAreaTemplateSpec, at.ProtoData);
            }
            else
            {
                var at = Character.AreaTemplates.Find(iim.Id);
                if (at != null)
                {
                    at.Update(iim.Name, iim.TemplateData);
                }
            }

        }

        #endregion AreaTemplate

        #region BlockStructure

        public void AddOrUpdateBlockStructureGroup(BlockStructureGroupData bpgd)
        {
            if (Character.BlockPrefabGroups.Count > Character.MemberSet.MaxBlockGroupCount)
            {
                ShowTipsMessage(LocalityStrings.maximumAmountLimit);
                return;
            }

            if (bpgd.Group <= 0)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }

            var bpg = Character.BlockStructureGroups.First(p => p.Group == bpgd.Group);


            if (bpg == null)
            {
                bpg = Character.CreateBlockStructureGroup(bpgd);
            }
            else
            {
                bpg.Update(bpgd);
            }

            User.SendGoogleProto(ClientMethod.AddOrUpdateBlockStructureGroup, bpg.ProtoData);
        }

        public void DeleteBlockStructureGroup(IDMessage im)
        {
            var bpg = Character.BlockStructureGroups.Find(im.Id);
            if (bpg == null)
            {
                ShowIDNotFoundError(im.Id);
                return;
            }
            bpg.Delete();
            Character.BlockStructureGroups.Remove(bpg.ID);

            var rsp = new IDMessage();
            rsp.Id = bpg.ID;
            User.SendGoogleProto(ClientMethod.DeleteBlockStructureGroup, rsp);
        }

        public void GetBlockStructureGroupDatas()
        {
            BlockStructureGroupDatas bgds = new BlockStructureGroupDatas();
            var bpg = Character.BlockStructureGroups;
            if (bpg.Count == 0)
            {
                var bpd = new BlockStructureGroupData();
                bpd.Group = 1;
                bpd.Name = "未命名";
                Character.CreateBlockStructureGroup(bpd);
            }

            foreach (var bg in Character.BlockStructureGroups.Data.Values)
            {
                bgds.Groups.Add(bg.ProtoData);
            }
            var bpf = Character.BlockStructures;
            if (bpf.Count == 1)
            {
                var sbsr = new SaveBlockStructureRequest();
                sbsr.Name = "未命名";
                sbsr.Group = 1;
                Character.CreateBlockStructure(sbsr);
            }
            foreach (var bg in Character.BlockStructures.Data.Values)
            {
                bgds.Datas.Add(bg.ProtoData);
            }
            User.SendGoogleProto(ClientMethod.ShowBlockStructureGroupDatas, bgds);
        }

        public void PreviewBlockStructure(PreviewBlockStructureRequest patr)
        {
            if (patr.Group == 0)
            {
                var ats = GameApplication.DataManager.FindValue<BlockStructureSpec>(patr.Id);
                if (ats != null)
                {
                    var td = ats.Data;
                    ShowPreviewBlockStructure(ats.ID, td);
                }
            }
            else
            {
                var ats = Character.BlockStructures.Find(patr.Id);
                if (ats != null)
                {
                    var td = ats.GetTemplateData();
                    ShowPreviewBlockStructure(ats.ID, td);
                }
            }
        }

        protected void ShowPreviewBlockStructure(long id, BlockStructureData td)
        {
            var patd = new PreviewBlockStructureData();
            patd.Id = id;
            patd.Data = td;
            User.SendGoogleProto(ClientMethod.PreviewBlockStructure, patd);
        }

        public void SaveBlockStructure(SaveBlockStructureRequest iim)
        {
            if (iim.Id == 0)
            {
                var at = Character.CreateBlockStructure(iim);

                SendGoogleProto(ClientMethod.AddBlockStructureSpec, at.ProtoData);
            }
            else
            {
                var at = Character.BlockStructures.Find(iim.Id);
                if (at != null)
                {
                    if (iim.Range != null)
                    {
                        at.Range = IntVector3.FromVector3Data(iim.Range);
                    }
                    at.Update(iim.Name, iim.Data);
                }
            }

        }
        public void DeleteBlockStructure(IDMessage im)
        {
            var bp = Character.BlockStructures.Find(im.Id);
            if (bp == null)
            {
                ShowIDNotFoundError(im.Id);
                return;
            }
            bp.Delete();
            Character.BlockStructures.Remove(bp.ID);

            var rsp = new IDMessage();
            rsp.Id = bp.ID;
            User.SendGoogleProto(ClientMethod.DeleteBlockStructureSpec, rsp);
        }
        #endregion BlockStructure
        #endregion Area

        #region SceneComponents

        public void ChangeSceneComponent(ChangeSceneComponentRequest cscr)
        {
            var scene = User.Character.SceneCharacter.CurrentScene;
            if (scene.OwnerID != User.Character.ID)
            {
                return;
            }
            foreach (var scd in cscr.Datas)
            {
                scene.AddOrUpdateSceneComponent(scd);
            }

            SceneAreaSceneComponentListData sascld = new SceneAreaSceneComponentListData();
            sascld.SceneID = scene.ID;
            sascld.AreaID = User.Character.SceneCharacter.CurrentArea.ID;
            foreach (var sc in scene.SceneComponents)
            {
                sascld.Datas.Add(sc.Value.ProtoData);
            }

            scene.SendToSceneService(SceneManagerClientMethod.ChangeSceneComponent, sascld);
        }
        #endregion  SceneComponents

        public void GotoTarget(StringMessage req)
        {
            var target = GameApplication.DataManager.FirstValue<Character>(p => string.Equals(p.Name, req.Msg, StringComparison.OrdinalIgnoreCase));
            if (target == null)
            {
                ShowError(LocalityStrings.targetCharacterNotFound);
                return;
            }
            if (target.IsOnline)
            {
                var area = target.SceneCharacter.CurrentArea;
                if (area == null)
                {
                    ShowError(LocalityStrings.limitedDimensionVisitCharacter);
                    return;
                }
                var scene = area.Scene;
                //if (scene.ID == target.BeginnerSceneID)
                //{
                //    ShowError(LocalityStrings.limitedDimensionVisitCharacter);
                //    return;
                //}

                scene.Enter(Character.SceneCharacter, area.ID);
            }
            else
            {
                ShowError(LocalityStrings.limitedDimensionVisitCharacter);
                return;
            }
        }

        public void VisitTarget(StringMessage req)
        {
            var target = GameApplication.DataManager.FirstValue<Character>(p => string.Equals(p.Name, req.Msg, StringComparison.OrdinalIgnoreCase));
            if (target == null)
            {
                ShowError(LocalityStrings.targetCharacterNotFound);
                return;
            }

            ShowError(LocalityStrings.limitedDimensionVisitCharacter);
            return;
        }

        public void ChangeActionStatus(IDActionStatusData iasd)
        {
            var area = Character.SceneCharacter?.CurrentArea;
            if (area == null)
            {
                ShowError(LocalityStrings.targetAreaNotFound);
                return;
            }
            if (iasd == null)
            {
                ShowError(PicaLocalityStrings.PKT_SYS2_0000015);
                return;
            }

            if (iasd.TargetData.Action != ActionStatus.InteractionObject &&
                iasd.TargetData.Action != ActionStatus.InteractionPlayer)
            {
                goto SendToScene;
            }

            if (!area.DoInteraction(iasd.Id, iasd.TargetData))
            {
                ShowError(PicaLocalityStrings.PKT_SYS2_0000015);
                return;
            }

        SendToScene:
            var req = new SceneSceneObjectActionStatusDataList()
            {
                SceneID = area.Scene.ID,
                AreaID = area.ID

            };

            req.ActionStatusDataList.Add(new IDActionStatusData()
            {
                Id = iasd.Id,
                TargetData = iasd.TargetData
            });
            area.SendToSceneService(SceneManagerClientMethod.ChangeActionStatusList, req);
        }

        //public void VisitPersonal(IDStringActionBytesMessage req)
        //{

        //    var game = GameApplication.DataManager.FindValue<MiniGame>(req.ID);
        //    if (game == null)
        //    {
        //        ShowError(LocalityStrings.targetMiniGameNotFound);
        //        return;
        //    }
        //    if (game.OwnerID == 0)
        //    {
        //        if (!game.Source.Available)
        //        {
        //            ShowError(LocalityStrings.notAvailable);
        //            return;
        //        }
        //        if (game.Version == 0)
        //        {
        //            ShowError(LocalityStrings.limitedTargetStatus);
        //            return;
        //        }
        //    }
        //    Character target;
        //    switch (req.Action)
        //    {
        //        case 1:
        //            var ireq = ProtoSerializer.Deserialize<IDMessage>(req.Bytes);
        //            target = GameApplication.DataManager.FindValue<Character>(ireq.Id);
        //            break;
        //        case 2:
        //            var nreq = ProtoSerializer.Deserialize<StringMessage>(req.Bytes);
        //            target = GameApplication.DataManager.FirstValue<Character>(p => string.Equals(p.Name, nreq.Msg, StringComparison.OrdinalIgnoreCase));
        //            break;
        //        default:
        //            ShowError(LocalityStrings.argumentError);
        //            return;
        //    }
        //    if (target == null)
        //    {
        //        ShowError(LocalityStrings.targetCharacterNotFound);
        //        return;
        //    }
        //    var data = game.Release.Scenes.Scenes.Find(p => p.IsDefault && p.Category == MiniGameSceneCategory.Personal);
        //    if (data == null)
        //    {
        //        ShowError(LocalityStrings.limitedTargetStatus);
        //        return;
        //    }
        //    var link = target.Scenes.Find(p => p.Type == data.Data.Type);
        //    if (link == null)
        //    {
        //        ShowError(LocalityStrings.targetSceneNotFound);
        //        return;
        //    }

        //    var dimension = GameApplication.MiniGameManger.GetDimension(game, game.Release, true);
        //    var level = dimension.GetLevel(data);
        //    var scene = level.GetScene(link.ID, true);
        //    scene.Enter(Character.SceneCharacter);
        //}

        /// <summary>
        /// 广播角色动画
        /// </summary>
        /// <param name="nd"></param>
        /// <remarks>SceneBroadcast</remarks>
        /// <returns>PlayActorAnim <see cref="Proto.GameModel.NoteData"/></returns>
        public void PlayActorAnim(SceneAreaIDInt64StringMessage nd)
        {
            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayActorAnim, nd);
        }

        /// <summary>
        /// 广播角色特效
        /// </summary>
        /// <param name="nd"></param>
        /// <remarks>SceneBroadcast</remarks>
        /// <returns>PlayActorEff <see cref="Proto.GameModel.NoteData"/></returns>
        public void PlayActorEff(SceneAreaIDInt64StringMessage nd)
        {
            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayActorEff, nd);
        }

        //public void PlayObjEff(ThrowData nd)
        //{
        //    Character.SceneCharacter.Area.SendToSceneService(SceneManagerClientMethod.PlayObjEff, nd);
        //}

        /// <summary>
        /// 选择初始房型
        /// </summary>
        /// <param name="msg"></param>
        public void ChooseDefaultRoomType(IDMessage msg)
        {
            Character.DefaultRoomType = msg.Id;
        }

        /// <summary>
        /// 庭舍随机串门
        /// </summary>
        public void PicaVisitRandomGarden()
        {
            var scene = GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(spec => IsAvailable(spec) &&
                                                                                    spec.OwnerID != Character.ID &&
                                                                                    spec.PlayerCount > 0 &&
                                                                                    spec.PlayerCount < spec.Scene.TypeData.MaxPlayerCount)
                        .OrderByDescending(spec => spec.PlayerCount).Take(10).Random();
            if (scene == null)
            {
                ShowError(LocalityStrings.argumentError);
                return;
            }
            if (Constants.AllowTaLog)
            {
                Character.GetLeiTingTrackWriter("random_enter_garden")
                    .WriteStringEntry("scene_id", scene.ID)
                    .WriteEntry("scene_name", scene.Name == "" ? scene.OwnerName + "的庭舍" : scene.Name)
                    .WriteStringEntry("target_player_id", scene.OwnerID)
                    .WriteEntry("target_player_name", scene.OwnerName)
                    .WriteToTaLogger();
            }

            TryEnterSceneRequest ter = new TryEnterSceneRequest();
            ter.SceneID = scene.Scene.ID;
            ter.AreaID = 0;
            TryEnterScene(ter);
        }

        public void ChangeConnectRoomRequest(IDMessage msg)
        {
            var scene = Character.SceneCharacter.CurrentScene;
            var record = scene.GetFunctionRecord<PicaRoomExtraDatasFunctionRecord>(false);
            if (record == null)
            {
                return;
            }
            record.ChangeConnentScene(msg.Id);
            var target = GameApplication.SceneManager.GetScene<PicaRoomScene>(msg.Id, true);
            target.GetFunctionRecord<PicaRoomExtraDatasFunctionRecord>(true).ChangeConnentScene(Character.SceneCharacter.CurrentSceneID);
            target.Save();
            scene.Save();
            User.SendGoogleProto(ClientMethod.GetSceneSpecDataResponse, ((PicaRoomSceneSpec)target.Spec).ProtoData);
            User.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000120);
        }

        /// <summary>
        /// 同步玩家传送特效
        /// </summary>
        public void ShowLoadSceneEffect(Int32Message data)
        {
            var scene = Character.SceneCharacter.CurrentScene;
            Character.LoadSceneEffectType = data.Value;
            scene.Broadcast(ClientMethod.ShowLoadSceneEffect, new IDInt32Message() { ID = Character.ID, Value = data.Value });
        }

        public void PublicCake(PublicCakeData idm)
        {
            if (!DataCache.Instance.PublicCakeFunctionCache.TryGetValue(idm.Eid, out var config)) return;

            ThrowData td = new ThrowData();
            td.AreaId = Character.SceneCharacter.CurrentAreaID;
            td.SceneId = Character.SceneCharacter.CurrentSceneID;
            td.PlayerId = Character.ID;
            td.TargetId = idm.Id;
            td.Eff = config.Effect;

            Character.SceneCharacter.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayActorMissile, td);
            Character.SceneCharacter.ResetInteractionAvatar();
            var player = Character.SceneCharacter.CurrentArea.FindPlayer(idm.Id);
            if (player != null)
            {
                player.Owner.OnSuit(config.TargetSuitId, "美食狂欢", 1, showPhantom: true);
            }
        }

        //Dictionary<long, string> cakeEff = new Dictionary<long, string>() { { 303962, "cakeBallEffect_s1" }, { 303963, "cakeBallEffect_s2" }, { 303964, "cakeBallEffect_s3" } };
        //Dictionary<long, long> cakeEid = new Dictionary<long, long>() { { 303962, 202 }, { 303963, 203 }, { 303964, 204 } };

        /// <summary>
        /// 场景需要等待所有玩家加载完毕时的确认
        /// </summary>
        public void EnterSceneComplete()
        {
            Character.SceneCharacter.CurrentScene.OnCharacterEnterSceneComplete(Character.SceneCharacter);
            Character.SceneCharacter.CurrentScene.Trigger<ISceneEnterComplete>(p => p.OnCharacterEnterSceneComplete(Character.SceneCharacter));
        }

        public void AddOrRemoveFavouriteRoom(IDBoolMessage req)
        {
            var sp = GameApplication.SceneManager.FindSpec(req.Id) as PicaRoomBaseSpec;
            if (sp == null)
            {
                Character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000029);
                return;
            }
            if (req.Value)
            {
                if (Character.ListDatas.FavouriteRoomIDs.Count >= Constants.MaxFavouriteSceneCount)
                {
                    Character.ShowTipsMessage("PKT_FavouriteRoomOverLimit", Constants.MaxFavouriteSceneCount);//TODO 收藏失败，当前版本最多可收藏{0}个房间
                    return;
                }
                if (Character.ListDatas.FavouriteRoomIDs.Contains(req.Id))
                {
                    Character.ShowTipsMessage("PKT_DuplicateFavouriteRoom");//TODO 收藏失败，请勿重复收藏房间
                    return;
                }
                Character.ListDatas.FavouriteRooms.Add(sp);
                Character.ListDatas.FavouriteRoomIDs.Add(req.Id);
            }
            else
            {
                if (!Character.ListDatas.FavouriteRoomIDs.Contains(req.Id))
                {
                    Character.ShowTipsMessage("PKT_FavouriteRoomNotFound");//TODO 取消收藏失败，房间没有被收藏
                    return;
                }
                Character.ListDatas.FavouriteRooms.Remove(sp);
                Character.ListDatas.FavouriteRoomIDs.Remove(req.Id);
            }
            Character.ListDatas.Save();
            Character.UpdateToClient(c => { c.FavouriteRooms.AddRange(Character.ListDatas.FavouriteRoomIDs); c.RefreshListName = nameof(c.FavouriteRooms); });
        }

        public void RequestMapData()
        {
            var data = new MapData();
            data.DayNight = DesertWeatherManager.CurrentDayNight;
            data.Weather = DesertWeatherManager.CurrentWeather;
            Character.SendGoogleProto(ClientMethod.MapRequest, data);
        }

    }
}