using System.Collections.Generic;

namespace GameFramework.Networking.Http.Routing
{
    internal class RouteEndpoint
    {
        public RouteEndpoint(IEnumerable<string> methods, string pattern, RequestDelegate handler)
        {
            Methods = methods;
            Pattern = pattern;
            Handler = handler;
        }

        public IEnumerable<string> Methods { get; }

        public string Pattern { get; }

        public RequestDelegate Handler { get; }
    }
}
