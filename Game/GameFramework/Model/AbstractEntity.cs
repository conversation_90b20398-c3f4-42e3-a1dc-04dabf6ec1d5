using System;
using DataBase;
using DataBase.Entity;

namespace GameFramework.Model
{
    public abstract class AbstractEntity : RowAdapter
    {
        private IDBManager dbManager;

        public sealed override ICacheManager CacheManager => throw new NotSupportedException();

        public sealed override IDBManager DBManager
        {
            get
            {
                if (dbManager == null)
                {
                    var view = EntitySchemaManager.GetSchema(GetType(), true);
                    dbManager = DbConnectionManager.Gain(view.DbGroup, true);
                }
                return dbManager;
            }
        }
    }
}
