using System;
using GameFramework.Application.GameRanking.Manager;
using GameFramework.Rankings;
using Proto.GameModel;

namespace GameFramework.Application.GameRanking.Model
{
    public class SceneGameRanking : IndexGameRanking<SceneGameRankingItem, SceneGameRankingScore>
    {
        private readonly GameRankingSceneGameRankingCategory category;
        private readonly long sourceID;

        public SceneGameRanking(GameRankingSceneGameRankingCategory category, long sourceID)
        {
            this.category = category;
            this.sourceID = sourceID;
        }

        protected override Func<SceneGameRankingItem, long> KeySelector { get; } = p => p.CharacterID;

        protected override Ranking<SceneGameRankingItem, SceneGameRankingScore> CreateRanking()
        {
            var dataManager = Games.GetComponent<DataManager>();
            var items = dataManager.LoadEntities<SceneGameRankingItem>(p => p.Category == category && p.SourceID == sourceID && !p.Deleted);
            return new Ranking<SceneGameRankingItem, SceneGameRankingScore>(new SceneGameRankingScore.Comparer(), items);
        }

        public SceneGameRankingItem GetItem(long characterID)
        {
            Indices.TryGetValue(characterID, out SceneGameRankingItem value);
            return value;
        }
    }
}
