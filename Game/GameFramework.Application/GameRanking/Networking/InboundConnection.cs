using System;
using ConnectionBase;
using ConnectionBase.Sockets;
using HelpBase;
using Proto.GameModel;
using ProtoBuf;

namespace GameFramework.Application.GameRanking.Networking
{
    public class InboundConnection : BaseDisposed
    {
        private readonly SocketListener socketListener;
        private readonly ExSocket socket;
        private readonly IServiceMethodInvoker invoker = new GameRankingServiceCommandInvoker(new InboundCommand());

        public InboundConnection(SocketListener socketListener, ExSocket socket)
        {
            this.socketListener = socketListener;
            this.socket = socket;
        }

        public Guid Guid => socket.Guid;

        private void SendMessage(Message message)
        {
            var bytes = ProtoSerializer.Serialize(message);
            socketListener.Send(socket, bytes);
        }

        protected override void Dispose(bool disposing)
        {
            if (!IsDisposed)
            {
                try
                {
                    socket.Close();
                }
                finally
                {
                    base.Dispose(disposing);
                }
            }
        }

        public void Received(Message request)
        {
            var response = new Message();
            response.Method = request.Method;

            try
            {
                var result = invoker.Invoke(request.Method, request.Body);
                if (result != null)
                {
                    response.Body = ProtoSerializer.Serialize(result);
                    response.Result = Message.ResponseTypes.Success;
                }
            }
            catch (Exception ex)
            {
                var faulted = new MessageFaulted();
                faulted.Message = ex.Message;

                response.Body = ProtoSerializer.Serialize(faulted);
                response.Result = Message.ResponseTypes.Faulted;

                var name = invoker.GetMethodName(request.Method);
                Logger.Warning.Write("service:{0} method:{1} error:{2}", invoker.GetType().FullName, name, ex);
            }

            SendMessage(response);
        }

        public void Close()
        {
            socket.Close();
        }
    }
}
