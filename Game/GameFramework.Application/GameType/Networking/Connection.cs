using System;
using System.Collections.Concurrent;
using System.Threading;
using ConnectionBase.Sockets;
using GameFramework.Application.GameType.Options;
using Proto.GameModel;
using ProtoBuf;

namespace GameFramework.Application.GameType.Networking
{
    public abstract class Connection : IServiceMethodCaller
    {
        private readonly ConnectionOptions options;
        private ConcurrentDictionary<int, CompletionSource<byte[]>> contexts = new ConcurrentDictionary<int, CompletionSource<byte[]>>();
        private int nextMessageGuid;

        public Connection(ConnectionOptions options)
        {
            if (options == null)
                throw new ArgumentNullException(nameof(options));

            this.options = options;
        }

        private Message CreateResponse(Message request, byte[] bytes)
        {
            var response = new Message();
            response.Guid = request.Guid;
            response.Method = request.Method;
            response.Direction = Message.Directions.Response;
            response.Result = Message.ResponseTypes.Success;
            response.Body = bytes;
            return response;
        }

        private Message CreateException(Message request, Exception exception)
        {
            var faulted = new MessageFaulted();
            faulted.Message = exception.Message;

            var response = new Message();
            response.Guid = request.Guid;
            response.Method = request.Method;
            response.Direction = Message.Directions.Response;
            response.Result = Message.ResponseTypes.Faulted;
            response.Body = ProtoSerializer.Serialize(faulted);
            return response;
        }

        protected abstract void SendMessage(Message message);

        protected abstract IProtoObject InvokeMessage(Message message);

        protected void MessageReceived(Message message)
        {
            switch (message.Direction)
            {
                case Message.Directions.Request:
                    {
                        Message response;
                        try
                        {
                            var result = InvokeMessage(message);
                            var bytes = result != null ? ProtoSerializer.Serialize(result) : null;
                            response = CreateResponse(message, bytes);
                        }
                        catch (Exception ex)
                        {
                            response = CreateException(message, ex);
                        }
                        SendMessage(response);
                    }
                    break;
                case Message.Directions.Response:
                    {
                        if (contexts.TryGetValue(message.Guid, out var context))
                        {
                            switch (message.Result)
                            {
                                case Message.ResponseTypes.Success:
                                    context.SetResult(message.Body);
                                    break;
                                case Message.ResponseTypes.Faulted:
                                    var response = ProtoSerializer.Deserialize<MessageFaulted>(message.Body);
                                    context.SetException(new ServiceMethodInvokeException(message.Method, response));
                                    break;
                            }
                        }
                    }
                    break;
                case Message.Directions.OneWay:
                    try
                    {
                        InvokeMessage(message);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error.Write(ex.ToString());
                    }
                    break;
            }
        }

        byte[] IServiceMethodCaller.Invoke(object obj, int method, IProtoObject req, InvokeMethodOptions options)
        {
            var message = new Message();
            message.Guid = Interlocked.Increment(ref nextMessageGuid);
            message.Method = method;
            if (req != null)
            {
                message.Body = ProtoSerializer.Serialize(req);
            }

            switch (options)
            {
                case InvokeMethodOptions.None:
                    message.Direction = Message.Directions.Request;
                    var context = new CompletionSource<byte[]>();
                    contexts[message.Guid] = context;
                    SendMessage(message);

                    try
                    {
                        return context.GetResult(this.options.RequestTimeout);
                    }
                    finally
                    {
                        contexts.TryRemove(message.Guid, out _);
                        context.Dispose();
                    }
                case InvokeMethodOptions.OneWay:
                    message.Direction = Message.Directions.OneWay;
                    SendMessage(message);
                    return null;
                default:
                    throw new InvalidOperationException(string.Format("unknown {0}.{1}", nameof(InvokeMethodOptions), options));
            }
        }
    }
}
