namespace GameModel
{
    public interface IUpdatable
    { }

    public interface ISecondUpdatable : IUpdatable
    {
        void GoSecond();
    }

    public interface IMinuteUpdatable : IUpdatable
    {
        void GoMinute();
    }

    public interface IHourUpdatable : IUpdatable
    {
        void GoHour();
    }

    public interface IDayUpdatable : IUpdatable
    {
        void GoDay();
    }

    public interface IWeekUpdatable : IUpdatable
    {
        void GoWeek();
    }

    public interface IMonthUpdatable : IUpdatable
    {
        void GoMonth();
    }
}
