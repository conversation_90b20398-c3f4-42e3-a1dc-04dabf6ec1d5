using Proto.GameModel;
using System;
using System.Numerics;

namespace GridMap
{
    public abstract class NodeCell<T> : INodeCell
        where T:NodeBox
    {
        protected static int maxDepth = 50;
        protected static int startDepth = 4;
        protected static int walkDepth = 4;

        protected  byte x;
        protected  byte z;          
 
        protected T _groundBox;        
        public T GroundBox
        {
            get { return _groundBox; }
        }

        NodeBox INodeCell.GroundBox
        {
            get
            {
                return _groundBox;
            }
        }

        protected T[] boxs;  
                
        protected Vector3 position;   

        public Vector3 Position
        {
            get
            {
                return position;
            }
        }
 
        public bool Available
        {
            get;
            set;
        }

        public T Top
        {
            get
            {
                if (boxs == null)
                    return null;
                else
                    return boxs[GetTopIndex()];
            }
        }

        NodeBox INodeCell.Top
        {
            get
            {
                if (boxs == null)
                    return null;
                else
                    return boxs[GetTopIndex()];
            }
        }

        #region Neighbor

        NodeCell<T> _front;
        NodeCell<T> _back;
        NodeCell<T> _left;
        NodeCell<T> _right;
        NodeCell<T> _frontLeft;
        NodeCell<T> _frontRight;
        NodeCell<T> _backLeft;
        NodeCell<T> _backRight;

        public NodeCell<T> Front
        {
            get { return _front; }
            set
            {
                if (_front != null && value == null)
                    _front._back = null;
                _front = value;
                if (_front != null)
                    _front._back = this;
            }
        }

        public NodeCell<T> Back
        {
            get { return _back; }
            set
            {
                if (_back != null && value == null)
                    _back._front = null;
                _back = value;
                if (_back != null)
                    _back._front = this;
            }
        }

        public NodeCell<T> Left
        {
            get { return _left; }
            set
            {
                if (_left != null && value == null)
                    _left._right = null;
                _left = value;
                if (_left != null)
                    _left._right = this;
            }
        }

        public NodeCell<T> Right
        {
            get { return _right; }
            set
            {
                if (_right != null && value == null)
                    _right._left = null;
                _right = value;
                if (_right != null)
                    _right._left = this;
            }
        }

        public NodeCell<T> FrontLeft
        {
            get { return _frontLeft; }
            set
            {
                if (_frontLeft != null && value == null)
                    _frontLeft._backRight = null;
                _frontLeft = value;
                if (_frontLeft != null)
                    _frontLeft._backRight = this;
            }
        }

        public NodeCell<T> FrontRight
        {
            get { return _frontRight; }
            set
            {
                if (_frontRight != null && value == null)
                    _frontRight._backLeft = null;
                _frontRight = value;
                if (_frontRight != null)
                    _frontRight._backLeft = this;
            }
        }

        public NodeCell<T> BackLeft
        {
            get { return _backLeft; }
            set
            {
                if (_backLeft != null && value == null)
                    _backLeft._frontRight = null;
                _backLeft = value;
                if (_backLeft != null)
                    _backLeft._frontRight = this;
            }
        }

        public NodeCell<T> BackRight
        {
            get { return _backRight; }
            set
            {
                if (_backRight != null && value == null)
                    _backRight._frontLeft = null;
                _backRight = value;
                if (_backRight != null)
                    _backRight._frontLeft = this;
            }
        }
        #endregion

        public NodeCell()
        { }

        public virtual void Initialize(Vector3 startPosition, byte _x, byte _z)
        {
            x = _x;
            z = _z;

            position = startPosition + new Vector3(x * NodeBox.Size, 0, z * NodeBox.Size);
            _groundBox = NewBox(-1);
        }

        public abstract T NewBox(sbyte height);

        public override string ToString()
        {
            return "X: " + x + " Z: " + z;
        }

        public T Fill(sbyte height, object data)
        {
            if (height >= maxDepth || height < 0)
                return null;

            if (boxs == null)
            {
                boxs = new T[startDepth];
            }

            while (height >= boxs.Length)
            {
                var newBoxs = new T[boxs.Length * 2];
                Array.Copy(boxs, 0, newBoxs, 0, boxs.Length);
                boxs = newBoxs;
            }

            T box = boxs[height];
            if (box == null)
            {
                box = NewBox(height);
                boxs[height] = box;
            }
            box.Data = data;
            return box;
        }

        /// <summary>
        /// 得到NodeCell的物理高度
        /// </summary>
        /// <param name="height"></param>
        /// <returns></returns>
        public float GetHeight(int height)
        {
            return height * NodeBox.Size;
        }
        
        /// <summary>
        /// 取得该NodeCell最高的NodeBox的高度坐标
        /// </summary>
        /// <returns></returns>
        public int GetTopIndex()
        {
            if (boxs == null)
                return 0;

            for (int i = boxs.Length - 1; i >= 0; i--)
            {
                if (boxs[i] != null)
                    return i;
            }
            return 0;
        }

        /// <summary>
        /// 得到相应的NodeBox
        /// </summary>
        /// <param name="y"></param>
        /// <returns></returns>
        public NodeBox GetBox(int y)
        {
            if (y < -1)
                return null;
            else if (y == -1)
                return _groundBox;
            else
            {
                if (boxs == null || y >= boxs.Length)
                    return null;
                if (boxs[y] != null)
                    return boxs[y];
                return null;
            }
        }
 
        /// <summary>
        /// 设置不可摆放的cell
        /// </summary>
        /// <param name="forbid"></param>
        public virtual void SetForbidCell(bool forbid)
        { }

        /// <summary>
        ///  重置
        /// </summary>
        public virtual void Reset()
        {
            _groundBox.Reset();
            if (boxs != null)
            {
                for (int i = 0; i < boxs.Length; i++)
                    boxs[i] = null;
            }
        }

        /// <summary>
        /// NodeCell是不是空的
        /// </summary>
        /// <returns></returns>
        public bool IsNodeCellEmpty()
        {
            if (boxs == null)
            {
                return true;
            }

            int length = boxs.Length;
            for (int i = 0; i < length; i++)
            {
                if (boxs[i] != null)
                    return false;
            }
            return true;             
        }
  
        protected bool IsEmpty(int y)
        {
            if (boxs == null)
                return true;

            for (int i = y; i < y + walkDepth; i++)
            {
                if (i < boxs.Length && boxs[i] != null)
                    return false;
            }
            return true;
        }

        /// <summary>
        /// 清除y到y+height的NodeBox
        /// </summary>
        /// <param name="y"></param>
        /// <param name="height"></param>
        public virtual void ClearData(int y, int height)
        {
            if (boxs != null)
            {
                for (int i = y; i < y + height; i++)
                {
                    if (i < boxs.Length)
                        boxs[i] = null;
                }
            }
        }

        public void CheckNeighbor()
        {
            GroundBox.ResetNeighbor();
            if (boxs != null)
            {
                for (int i = 0; i < boxs.Length; i++)
                {
                    if (boxs[i] != null)
                        boxs[i].ResetNeighbor();
                }
            }

            CheckCell(Left, Direction.Left);
            CheckCell(Right, Direction.Right);
            CheckCell(Front, Direction.Front);
            CheckCell(Back, Direction.Back);
            CheckCell(FrontLeft, Direction.FrontLeft);
            CheckCell(FrontRight, Direction.FrontRight);
            CheckCell(BackLeft, Direction.BackLeft);
            CheckCell(BackRight, Direction.BackRight);
        }

        protected void CheckCell(NodeCell<T> cell, Direction d)
        {
            if (cell == null)
                return;

            CheckBoxNeighbor(cell, d, -1);
            if (boxs != null)
            {
                for (int i = 0; i < boxs.Length; i++)
                    CheckBoxNeighbor(cell, d, i);
            }
        }

        protected void CheckBoxNeighbor(NodeCell<T> pgc, Direction d, int y)
        {
            var my = GetBox(y);
            if (my == null || my.MoveType == MoveType.Disable)
                return;

            if (pgc.CanWalk(y - 1))
                my.SetNeighbor(d, pgc.GetBox(y - 1));
            else if (pgc.CanWalk(y))
                my.SetNeighbor(d, pgc.GetBox(y));
            else if (pgc.CanWalk(y + 1))
                my.SetNeighbor(d, pgc.GetBox(y + 1));
        }

        protected bool CanWalk(int y)
        {
            if (y == -1)
                return GroundBox.MoveType != MoveType.Disable && IsEmpty(0);
            else if (y < -1 || boxs == null || y >= boxs.Length)
                return false;
            else
                return boxs[y] != null && boxs[y].MoveType != MoveType.Disable && IsEmpty(y + 1);
        }

        NodeBox INodeCell.Fill(sbyte height, object data)
        {
            return Fill(height, data);
        }
    }

    public class NodeCell : NodeCell<NodeBox>
    {
        public override NodeBox NewBox(sbyte height)
        {
            return new NodeBox(Position + new Vector3(0, height * NodeBox.Size, 0), x, height, z);
        }
    }
}