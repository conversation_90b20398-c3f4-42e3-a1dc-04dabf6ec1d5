using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using Proto.GameModel;

namespace GameModel.Award
{
    public class AwardSpec : IProtoObject<AwardSpecData>
    {
        public long Type { get; set; }

        public string Name { get; set; } = "";

        public double Value { get; set; }

        public int LastDays { set; get; }

        public EntityName EntityName { get; set; }

        public List<PropertyCondition> Conditions { get; set; }

        /// <summary>
        /// 仅用于后台使用。不需进入ProtoBuf
        /// </summary>
        public string Origin { get; set; }

        public ObjectSpecCategory Category { get; set; }

        public JsonObject AwardDetails
        {
            get
            {
                var res = new JsonObject();
                res["Type"] = Type;
                res["Name"] = Name;
                res["Value"] = Value;
                res["EntityEnum"] = Enum.GetName(typeof(EntityName), EntityName);
                res["Category"] = Enum.GetName(typeof(ObjectSpecCategory), Category);
                return res;
            }
        }

        public AwardSpecData ProtoData
        {
            get
            {
                var awardSpec = new AwardSpecData();
                awardSpec.Type = Type;
                awardSpec.Name = Name;
                awardSpec.Value = Value;
                awardSpec.EntityEnum = EntityName;
                awardSpec.Category = Category;
                awardSpec.LastDays = LastDays;
                if (Conditions?.Count > 0) awardSpec.Conditions.AddRange(Conditions.Select(p => p.ProtoData));
                return awardSpec;
            }
        }
    }
}