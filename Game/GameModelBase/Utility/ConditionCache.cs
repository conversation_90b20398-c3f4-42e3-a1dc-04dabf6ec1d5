using System;
using System.Collections.Generic;
using System.Text;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 条件组
    /// </summary>
    [Serializable]
    public class ConditionGroup : JsonDynamicList<ConditionBase>
    {
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder(Count);
            foreach (var cb in this)
            {
                sb.Append(cb.ToString());
                sb.Append(" ");
            }
            return sb.ToString().TrimEnd(' ');
        }

        /// <summary>
        /// 获取特定类的Condition
        /// </summary>
        public List<ConditionBase> GetSpecialConditions(Type conditionType)
        {
            return FindAll(p => p.GetType() == conditionType);
        }
    }

    [Serializable]
    public abstract class ConditionBase : Function, ICloneable
    {
        /// <summary>
        /// 是否删除条件物品或者减少相应属性
        /// </summary>
        [JsonMember]
        [WebMember("删除条件物品")]
        public bool Reduce { get; set; } = true;

        [JsonMember]
        [WebMember("显示名")]
        public string ShowName { get; set; }

        public abstract ConditionBase Multiply(int fator);

        public override string ToString()
        {
            return ShowName;
        }

        public override object Clone()
        {
            return MemberwiseClone();
        }
    }

    public enum TaskConditonType
    {
        ActionCondition,
        ObjectCondition,
        PropertyCondition,
    }

    /// <summary>
    /// 任一值满足属性条件
    /// </summary>
    [Serializable]
    [WebClass("属性任一条件")]
    public class PropertyAnyCondition : ConditionBase
    {
        [JsonMember]
        [WebMember("属性名", UIType = "ConditionProperty")]
        public string PropertyName { get; set; }
        
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        [WebMember("数值")]
        public List<long> Value { get; set; }
        
        /// <summary>
        /// 测试值
        /// </summary>
        public bool TestValue(long checkValue)
        {
            return Value.Contains(checkValue);
        }

        public bool Check(object c)
        {
            dynamic oldValue = c.GetValueByPropertyName(PropertyName);
            if (oldValue is DateTime)
            {
                oldValue = ((DateTime)oldValue).ToTimestamp();
            }
            return TestValue((long)oldValue);
        }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (PropertyCondition)Clone();
            return newCondition;
        }
    }
    
    /// <summary>
    /// 属性条件
    /// </summary>
    [Serializable]
    [WebClass("属性条件")]
    public class PropertyCondition : ConditionBase, IProtoObject<PropertyConditionData>
    {
        [JsonMember]
        [WebMember("属性名", UIType = "ConditionProperty")]
        public string PropertyName { get; set; }

        /// <summary>
        /// 条件类型
        /// </summary>
        [JsonMember]
        [WebMember("条件类型")]
        public CompareType CompareType { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public long Value { get; set; }

        public PropertyConditionData ProtoData
        {
            get
            {
                PropertyConditionData pcd = new PropertyConditionData()
                {
                    PropertyName = PropertyName,
                    CompareType = CompareType,
                    Value = Value
                };
                return pcd;
            }
        }

        /// <summary>
        /// 测试值
        /// </summary>
        public bool TestValue(long checkValue, long questValue)
        {
            if (CompareType == CompareType.小于)
            {
                return checkValue < questValue;
            }
            else if (CompareType == CompareType.小于等于)
            {
                return checkValue <= questValue;
            }
            else if (CompareType == CompareType.等于)
            {
                return checkValue == questValue;
            }
            else if (CompareType == CompareType.大于)
            {
                return checkValue > questValue;
            }
            else if (CompareType == CompareType.大于等于)
            {
                return checkValue >= questValue;
            }
            return true;
        }

        public bool Check(object c, long needValue)
        {
            dynamic oldValue = c.GetValueByPropertyName(PropertyName);
            if (oldValue is DateTime)
            {
                oldValue = ((DateTime)oldValue).ToTimestamp();
            }
            //if (oldValue is DateTime dt)//目前生产环境中编译不通过
            //{
            //    oldValue = dt.ToTimestamp();
            //}
            return TestValue((long)oldValue, needValue);
        }

        /// <summary>
        /// 删除属性值
        /// </summary>
        public bool RemoveValue(object c, long needValue, out long leftValue)
        {
            dynamic oldValue = c.GetValueByPropertyName(PropertyName);
            leftValue = oldValue;

            if (!TestValue(oldValue, needValue))
            {
                return false;
            }
            if (Reduce)
            {
                var newvalue = oldValue - needValue;
                leftValue = newvalue;
                c.SetValueByPropertyName(PropertyName, (int)newvalue);
            }

            return true;
        }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (PropertyCondition)Clone();
            newCondition.Value = Value * fator;
            return newCondition;
        }
    }

    [Serializable]
    [WebClass("玩家条件")]
    public abstract class SceneCharacterCondition : ConditionBase
    {
        public abstract bool Check(SceneActorData actorData);
    }

    [Serializable]
    [WebClass("玩家等级条件")]
    public class SceneCharacterLevelCondition : SceneCharacterCondition
    {

        [JsonMember]
        [WebMember("数值")]
        public int Value { get; set; }

        public override bool Check(SceneActorData actorData)
        {
            return actorData.Lv >= Value;
        }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (PropertyCondition)Clone();
            return newCondition;
        }
    }


    /// <summary>
    /// 动态属性条件
    /// </summary>
    public sealed class DynamicPropertyCondition : PropertyCondition
    {
        private string formula = "160 - (Lv-3)*5";

        [JsonMember]
        public string Formula
        {
            get { return formula; }
            set { formula = value; }
        }

        private Func<object, decimal> expression;

        public Func<object, decimal> Expression
        {
            get
            {
                if (expression == null)
                {
                    expression = new ExpressionEvaluator().Compile(Formula);
                }
                return expression;
            }
        }

        public int GetValue(object argument)
        {
            var re = Expression(argument);
            return (int)re;
        }

        public override string ToString()
        {
            return PropertyName + "x" + Formula;
        }
    }

    /// <summary>
    /// 和npc的对话条件
    /// </summary>
    [Serializable]
    public sealed class DialogCondition : ConditionBase
    {
        /// <summary>
        /// npc的type
        /// </summary>
        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public long Value { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            throw new NotSupportedException();
        }
    }

    /// <summary>
    /// 需要的物品
    /// </summary>
    [Serializable]
    [WebClass("对象条件")]
    public sealed class ObjectCondition : ConditionBase
    {
        private List<PropertyCondition> propertyConditins = new List<PropertyCondition>();

        public ObjectCondition()
        {
            Reduce = true;
            Value = 1;
        }

        public ObjectCondition(long type, int value)
        {
            Type = type;
            Value = value;
        }

        /// <summary>
        /// 需要的物品类型
        /// </summary>
        [JsonMember]
        [WebMember("物品类型", UIType = UIType.EntitySearch)]
        public long Type { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public int Value { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<PropertyCondition>))]
        public List<PropertyCondition> PropertyConditions
        {
            get { return propertyConditins; }
            set { propertyConditins = value; }
        }

        public IList<T> FitCondtionsObject<T>(IList<T> checkobjects)
        {
            List<T> passedObject = new List<T>();

            foreach (var co in checkobjects)
            {
                bool allOK = PropertyConditions.Count == 0;

                foreach (var pc in PropertyConditions)
                {
                    if (!pc.Check(checkobjects[0], pc.Value))
                    {
                        break;
                    }

                    allOK = true;
                }

                if (allOK)
                {
                    passedObject.Add(co);
                }
            }

            return passedObject;
        }

        public bool CheckCount(uint checknum, int times)
        {
            return checknum * times >= Value;
        }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (ObjectCondition)Clone();
            newCondition.Value = Value * fator;
            return newCondition;
        }

        public override string ToString()
        {
            return Type + "x" + Value;
        }
    }

    [WebClass("虚拟货币条件")]
    public sealed class VirtualCurrencyCondition : ConditionBase
    {
        public VirtualCurrencyCondition()
        { }

        public VirtualCurrencyCondition(long type, int value)
        {
            Type = type;
            Value = Value;
        }

        [JsonMember]
        [WebMember("货币类型", UIType = UIType.VirtualCurrencySearch)]
        public long Type { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public int Value { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (VirtualCurrencyCondition)Clone();
            newCondition.Value = Value * fator;
            return newCondition;
        }
    }

    [WebClass("角色属性条件")]
    public sealed class ActorPropertyCondition : ConditionBase
    {
        [JsonMember]
        [WebMember("类型", UIType = UIType.ActorPropertySearch)]
        public long Type { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public long Value { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            var newCondition = (ActorPropertyCondition)Clone();
            newCondition.Value = Value * fator;
            return newCondition;
        }
    }

    public sealed class ShowByQualityCondition : ConditionBase
    {
        [JsonMember]
        public Quality Quality { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            return (ShowByQualityCondition)Clone();
        }
    }

    /// <summary>
    /// 缺少的物品
    /// </summary>
    [Serializable]
    public sealed class LackCondition
    {
        public ConditionBase Condition { get; set; }

        public long Type { get; set; }

        public int LackCount { get; set; }
    }

    [Serializable]
    public sealed class ActionCondition : ConditionBase
    {
        [JsonMember]
        public long Param { get; set; }

        [JsonMember]
        [WebMember("数值")]
        public long Value { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            throw new NotSupportedException();
        }
    }

    public sealed class AvatarCondition : ConditionBase
    {
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> Types { get; set; }

        public override ConditionBase Multiply(int fator)
        {
            throw new NotSupportedException();
        }
    }
}