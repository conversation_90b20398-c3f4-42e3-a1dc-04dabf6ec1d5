using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Utility
{
    public class IntRect : JsonFormatObject
    {
        [JsonMember]
        public int X { get; set; }
        [JsonMember]
        public int Y { get; set; }
        [JsonMember]
        public int Width { get; set; }
        [JsonMember]
        public int Height { get; set; }
    }

    public class FloatRect : JsonFormatObject
    {
        [JsonMember]
        public float X { get; set; }
        [JsonMember]
        public float Y { get; set; }
        [JsonMember]
        public float Width { get; set; }
        [JsonMember]
        public float Height { get; set; }
    }
}
