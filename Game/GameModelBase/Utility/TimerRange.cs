using HelpBase.Json;
using HelpBase.Web;
using System;

namespace GameModel.Utility
{
    [WebClass("时间范围")]
    public class TimerRange : JsonFormatObject
    {
        [WebMember("最小范围")]
        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan MinTime { get; set; }

        [WebMember("最大范围")]
        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan MaxTime { get; set; }

        public TimeSpan GetTimeWithRange()
        {
            return TimeSpan.FromMilliseconds(RandomEvent.Next(MinTime.TotalMilliseconds, MaxTime.TotalMilliseconds));
        }

        //public JsonObject ProtoData
        //{
        //    get
        //    {
        //        var json = new JsonObject();
        //        json[nameof(ID)] = ID;
        //        json[nameof(Probability)] = Probability;
        //        return json;
        //    }
        //}
    }
}
