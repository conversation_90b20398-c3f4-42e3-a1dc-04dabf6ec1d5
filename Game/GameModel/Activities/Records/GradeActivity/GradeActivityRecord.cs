using System.Collections.Generic;
using System.Json;
using System.Linq;
using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Activities
{
    public abstract class GradeActivityRecord<TGrade> : RetroactiveAwardCharacterRecord
        where TGrade : GradeActivityOptions
    {
        [TableColumn]
        public int TotalValue { get; set; }

        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonListFormat<GradeActivityOptionRecord>))]
        public List<GradeActivityOptionRecord> Statuses { get; set; }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            Statuses = new List<GradeActivityOptionRecord>();
        }

        public bool CheckRecords(List<TGrade> awards)
        {
            GradeActivityOptionRecord gs = null;
            bool change = false;
            foreach (var award in awards)
            {
                gs = Statuses.FirstOrDefault(p => p.Grade == award.Grade);
                if (gs == null)
                {
                    gs = new GradeActivityOptionRecord();
                    gs.Grade = award.Grade;
                    gs.Status = AwardsStatus.没有;
                    Statuses.Add(gs);
                    change = true;
                }
                if (TotalValue >= award.CheckValue && gs.Status == AwardsStatus.没有)
                {
                    gs.Status = AwardsStatus.待领取;
                    change = true;
                    HasRetroactiveAward = true;
                }
            }
            if (change)
            {
                Save();
            }
            return change;
        }
    }

    public class GradeActivityOptionRecord : JsonFormatObject
    {
        [JsonMember]
        public int Grade { get; set; }

        [JsonMember]
        public AwardsStatus Status { get; set; }

        [NonJsonMember]
        public JsonObject ProtoData
        {
            get
            {
                var json = new JsonObject();
                json[nameof(Grade)] = Grade;
                json[nameof(Status)] = (int)Status;
                return json;
            }
        }
    }
}
