using DataBase;
using GameModel.Award;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;
using ProtoBuf;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Activities
{
    [WebClass("拼一刀活动")]
    public class BingxixiBargainActivity : ActivityFunction<BingxixiCharacterRecord>, IActionTrigger<Character, UserAction2>, IInteractionActivity, IChargeShopActivity
    {
        private List<BingxixiGoodSetting> _goodSettings = new List<BingxixiGoodSetting>();
        [WebMember("商品配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<BingxixiGoodSetting>))]
        public List<BingxixiGoodSetting> GoodSettings
        {
            set
            {
                _goodSettings = value;
                _goodSettingDic = null;
            }
            get
            {
                return _goodSettings;
            }
        }

        private Dictionary<int, BingxixiGoodSetting> _goodSettingDic;
        public Dictionary<int, BingxixiGoodSetting> GoodSettingDic
        {
            get
            {
                if (_goodSettingDic == null)
                {
                    _goodSettingDic = new Dictionary<int, BingxixiGoodSetting>();
                    foreach (var setting in GoodSettings)
                    {
                        _goodSettingDic.Add(setting.GoodID, setting);
                    }
                }
                return _goodSettingDic;
            }
        }

        private List<BingxixiBargainTemplate> _bargainSettings = new List<BingxixiBargainTemplate>();
        [WebMember("折扣配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<BingxixiBargainTemplate>))]
        public List<BingxixiBargainTemplate> BargainSettings
        {
            set
            {
                _bargainSettings = value;
                _goodSettingDic = null;
            }
            get
            {
                return _bargainSettings;
            }
        }

        private Dictionary<int, BingxixiBargainTemplate> _bargainSettingsDic;
        public Dictionary<int, BingxixiBargainTemplate> BargainSettingsDic
        {
            get
            {
                if (_bargainSettingsDic == null)
                {
                    _bargainSettingsDic = new Dictionary<int, BingxixiBargainTemplate>();
                    foreach (var setting in BargainSettings)
                    {
                        _bargainSettingsDic.Add(setting.BargainID, setting);
                    }
                }
                return _bargainSettingsDic;
            }
        }

        [WebMember("未竟折扣(百分比)")]
        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> NoComplyDiscounts { set; get; } = new List<int>();

        [WebMember("面板展示文案")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> ShowPanelWords { set; get; } = new List<string>();


        [WebMember("预设帮砍文案")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> NeedHelpWords { set; get; } = new List<string>();

        [WebMember("砍刀道具")]
        [JsonMember]
        public long CutItemID { set; get; }

        [WebMember("免费砍到文案合集")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> FreeGotWords { set; get; } = new List<string>();

        [WebMember("低价砍到文案合集")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> LowDiscountWords { set; get; } = new List<string>();

        [WebMember("全价购买文案合集")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> NoDiscountWords { set; get; } = new List<string>();


        public Queue<string> FreeBroadQueue = new Queue<string>();
        public Queue<string> LowDiscountBroadQueue = new Queue<string>();
        public Queue<string> NoDiscountBroadQueue = new Queue<string>();


        public UserAction2 Action => UserAction2.跨日 | UserAction2.登录;

        public override void FromJson(JsonObject jsonObj)
        {
            _goodSettingDic = null;
            _bargainSettings = null;
            base.FromJson(jsonObj);
        }

        public void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID, byte[] bytes)
        {
            switch (action)
            {
                case 1://查看面板数据
                    var data_1 = ProtoHelp.GetObject<IDStringMessage>(bytes);
                    var target = GameApplication.DataManager.FindValue<Character>(data_1.Id);
                    if (target == null) return;
                    GetCutPanelData(character, target, data_1.Value);
                    break;
                case 2://选择砍价一个商品
                    var data_2 = ProtoHelp.GetObject<Int32Message>(bytes);
                    SetInCutList(character, data_2.Value);
                    UpdateToClient(character);
                    break;
                case 3://找他人帮忙
                    var data_3 = ProtoHelp.GetObject<IDStringMessage>(bytes);
                    var helping_hand = GameApplication.DataManager.FindValue<Character>(data_3.Id);
                    if (helping_hand == null) return;
                    SeekForHelp(character, helping_hand, data_3.Value);
                    break;
                case 4://砍一刀
                    var data_4 = ProtoHelp.GetObject<IDStringMessage>(bytes);
                    var cut_chara = GameApplication.DataManager.FindValue<Character>(data_4.Id);
                    if (cut_chara == null) return;
                    ChooseOneToCut(character, cut_chara, data_4.Value);
                    GetCutPanelData(character, cut_chara, data_4.Value);
                    break;
                case 5://获取轮巡数据
                    GetBroadQueue(character);
                    break;
            }
        }

        public string GetActionNameByAction(byte action)
        {
            switch (action)
            {
                case 1: return "查看面板数据";
                case 2: return "选择砍价一个商品";
                case 3: return "找他人帮忙";
                case 4: return "砍一刀";
                case 5: return "获取轮巡数据";
                default: return "未知操作";
            }
        }

        public void Trigger(Character source, UserAction2 action, object param)
        {
            RefreshCharacterRecord(source);
            if (source.IsOnline)
            {
                UpdateToClient(source);
            }
            if (action == UserAction2.跨日)
            {
                var record = GetCharacterRecord(source, true);
                record?.CutGoodIds.Clear();
                record?.HelpingHands.Clear();
                record?.Save();
            }
        }

        public void RefreshCharacterRecord(Character character)
        {
            var record = GetCharacterRecord(character, true, true);

            if (DateTimeExtension.Now.PassedDays(record.LastRefreshTime) <= 1)
            {
                return;
            }
            record.LastRefreshTime = DateTimeExtension.Now;
            //移除待选择的奖励
            record.WaitForChooseIds.Clear();
            var recs_to_remove = new List<BingxixiCharacterGoodsRecord>();

            //移除原价购买的奖品
            lock (record.GoodRecords)
            {
                foreach (var item in record.GoodRecords)
                {
                    if (!item.CanBeCut)
                    {
                        recs_to_remove.Add(item);
                    }
                }

                foreach (var item in recs_to_remove)
                {
                    record.GoodRecords.Remove(item);
                    record.GoodRecordsDic.Remove(item.RecordID, out var _);
                }
            }

            for (int i = 1; i <= 3; i++)
            {
                var random_good = GoodSettings.Where(g => g.AppearIndexes.Contains(i) && !record.WaitForChooseIds.Contains(g.GoodID) && !record.GoodRecords.Any(r => r.GoodID == g.GoodID)).ToList().Random();
                record.WaitForChooseIds.Add(random_good.GoodID);
            }
            record.Save();
        }

        public void SetInCutList(Character character, int goodId)
        {
            var record = GetCharacterRecord(character, true, true);
            if (!record.WaitForChooseIds.Contains(goodId))
            {
                return;
            }

            lock (record.GoodRecords)
            {
                var in_cut_good = record.GoodRecords.FirstOrDefault(g => g.CanBeCut);
                if (in_cut_good != null)
                {
                    record.GoodRecords.Remove(in_cut_good);
                    record.GoodRecordsDic.Remove(in_cut_good.RecordID, out var _);
                }
            }

            foreach (var id in record.WaitForChooseIds)
            {
                CreateRecords(record, id, id == goodId);
            }
            record.WaitForChooseIds.Clear();
            record.Save();
        }

        private void CreateRecords(BingxixiCharacterRecord record, int goodId, bool canBeCut)
        {
            if (!GoodSettingDic.TryGetValue(goodId, out var conf))
            {
                return;
            }
            var good_rec = new BingxixiCharacterGoodsRecord();
            //这里id是多少其实都可以 只是需要一个 这个类型传什么无所谓
            var id = RowAdapter.CreateID<Item>();
            good_rec.RecordID = id.ToString();
            good_rec.GoodID = goodId;
            good_rec.CanBeCut = canBeCut;
            good_rec.CreateDate = DateTimeExtension.Now;

            if (canBeCut)
            {
                good_rec.NowPrice = conf.Price * RandomEvent.Next(conf.MinStartDiscount, conf.MaxStartDiscount + 1) / 100;
                good_rec.BoughtStatus = BingxixiGoodBuyStatus.未解锁;
                good_rec.InitCuts(this);
            }
            else
            {
                good_rec.BoughtStatus = BingxixiGoodBuyStatus.可购买;
            }

            record.GoodRecords.Add(good_rec);
            if (!record.GoodRecordsDic.ContainsKey(good_rec.RecordID))
            {
                record.GoodRecordsDic.TryAdd(good_rec.RecordID, good_rec);
            }
        }

        public void ChooseOneToCut(Character character, Character target, string id)
        {
            var my_record = GetCharacterRecord(character, true, true);
            if (my_record.CutGoodIds.Contains(id))
            {
                return;
            }

            my_record.CutGoodIds.Add(id);
            my_record.Save();

            var target_record = GetCharacterRecord(target, true, true);
            CheckToRefresh(target_record);
            if (!target_record.GoodRecordsDic.TryGetValue(id, out var good_record) || !good_record.CanBeCut)
            {
                return;
            }

            if (good_record.BoughtStatus != BingxixiGoodBuyStatus.未解锁)
            {
                return;
            }

            if (good_record.CutCount == good_record.Cuts.Count)
            {
                return;
            }

            if (!character.DeleteEntity(CutItemID, 1, "砍一刀"))
            {
                return;
            }

            var cut_price = good_record.Cuts[good_record.CutCount++];
            good_record.CutCharacters.Add(character.Name);

            good_record.NowPrice = good_record.NowPrice > cut_price ? good_record.NowPrice - cut_price : 0;
            if (good_record.NowPrice == 0)
            {
                good_record.BoughtStatus = BingxixiGoodBuyStatus.可购买;
            }
            else if (good_record.CutCount == good_record.Cuts.Count)
            {
                CheckToRefresh(target_record, true);
            }
            target_record.Save();
            UpdateToClient(character);
        }

        public void GetCutPanelData(Character character, Character target, string goodId)
        {
            var record = GetCharacterRecord(target, true);
            if (record == null)
            {
                return;
            }

            CheckToRefresh(record);

            if (!record.GoodRecordsDic.TryGetValue(goodId, out var good_rec))
            {
                return;
            }

            var aid = new ActivityInteractionData();
            aid.ActivityId = ID;
            aid.Action = 1;
            aid.TargetID = target.ID;
            aid.Character = target.ShortSpecData;
            aid.Bytes = ProtoSerializer.Serialize(new StringMessage() { Msg = good_rec.PanelProto(this).ToString() });

            character.SendGoogleProto(ClientMethod.DoActivityInteraction, aid);
            if (character.ID == target.ID)
            {
                UpdateToClient(character);
            }
        }

        public void GetBroadQueue(Character character)
        {
            var aid = new ActivityInteractionData();
            aid.ActivityId = ID;
            aid.Action = 5;

            var braod_queues = new JsonObject();
            var free_broad_queue = new JsonArray();
            var low_discount_broad_queue = new JsonArray();
            var no_discount_broad_queue = new JsonArray();

            foreach (var msg in FreeBroadQueue)
            {
                free_broad_queue.Add(msg);
            }
            foreach (var msg in LowDiscountBroadQueue)
            {
                low_discount_broad_queue.Add(msg);
            }
            foreach (var msg in NoDiscountBroadQueue)
            {
                no_discount_broad_queue.Add(msg);
            }
            braod_queues[nameof(FreeBroadQueue)] = free_broad_queue;
            braod_queues[nameof(LowDiscountBroadQueue)] = low_discount_broad_queue;
            braod_queues[nameof(NoDiscountBroadQueue)] = no_discount_broad_queue;

            aid.Bytes = ProtoSerializer.Serialize(new StringMessage() { Msg = braod_queues.ToString() });

            character.SendGoogleProto(ClientMethod.DoActivityInteraction, aid);
        }

        public void SeekForHelp(Character character, Character target, string goodId)
        {
            if (!character.RelationAgent.CheckIntimacy(target, 0))
            {
                return;
            }
            var record = GetCharacterRecord(character, true);

            if (record == null)
            {
                return;
            }

            if (!record.GoodRecordsDic.TryGetValue(goodId, out var good))
            {
                return;
            }
            if (record.HelpingHands.Contains(target.ID))
            {
                character.ShowTipsMessage("今日已经邀请过这个玩家咯");
                return;
            }

            var target_record = GetCharacterRecord(target, true, true);

            if (target_record.CutGoodIds.Contains(goodId))
            {
                character.ShowTipsMessage("PKT_BINGXIXI01");
                return;
            }
            record.HelpingHands.Add(target.ID);
            var goodAward = good.GetGoodSetting(this).Award;
            character.PrivateSpeak(target.ID, SpeakSubGroup.砍一刀, $"<link=GoodID><size=0>{goodId}</size></link>" + string.Format(NeedHelpWords.Random(), goodAward.Spec.Name), awards: new List<AwardBase>() { goodAward }, force: true, needLog: false);
            character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000005);
        }

        public PayResult BuyCheck(Character character, long item_id, int count, bool usingVoucher = false)
        {
            var record = GetCharacterRecord(character, true);
            if (record == null) return PayResult.过期商品;
            CheckToRefresh(character);
            if (!record.GoodRecordsDic.TryGetValue(item_id.ToString(), out var good_record))
            {
                return PayResult.商品未找到;
            }

            if (good_record.BoughtStatus != BingxixiGoodBuyStatus.可购买)
            {
                return PayResult.未开售;
            }

            if (good_record.NowPrice > 0)
            {
                if (!character.CheckVirtualCurrencyCountEnough(Constants.VirtualCurrency.金钻, good_record.NowPrice))
                {
                    return PayResult.金额不足;
                }
            }
            else if (!good_record.CanBeCut)
            {
                if (!character.CheckVirtualCurrencyCountEnough(Constants.VirtualCurrency.金钻, good_record.GetGoodSetting(this).Price))
                {
                    return PayResult.金额不足;
                }
            }

            return PayResult.成功;
        }

        public void Buy(Character character, long item_id, int count, bool usingVoucher = false)
        {
            var record = GetCharacterRecord(character, true);
            if (record == null) return;
            if (!record.GoodRecordsDic.TryGetValue(item_id.ToString(), out var good_record))
            {
                return;
            }
            if (good_record.NowPrice > 0)
            {
                if (!character.CheckVirtualCurrencyCountEnough(Constants.VirtualCurrency.金钻, good_record.NowPrice))
                {
                    return;
                }
                character.Pay(Constants.VirtualCurrency.金钻, good_record.NowPrice, "购买商品");
            }
            else if (!good_record.CanBeCut)
            {
                if (!character.CheckVirtualCurrencyCountEnough(Constants.VirtualCurrency.金钻, good_record.GetGoodSetting(this).Price))
                {
                    return;
                }
                character.Pay(Constants.VirtualCurrency.金钻, good_record.GetGoodSetting(this).Price, "购买商品");
            }
            var broad_str = "";
            if (good_record.NowPrice == 0)
            {
                if (good_record.CanBeCut)
                {
                    broad_str = string.Format(FreeGotWords.Random(), character.Name, good_record.GetGoodSetting(this).Award.Spec.Name);
                    FreeBroadQueue.Enqueue(broad_str);
                    if (FreeBroadQueue.Count >= 10)
                    {
                        FreeBroadQueue.Dequeue();
                    }
                }
                else
                {
                    broad_str = string.Format(NoDiscountWords.Random(), character.Name, good_record.GetGoodSetting(this).Award.Spec.Name);
                    NoDiscountBroadQueue.Enqueue(broad_str);
                    if (NoDiscountBroadQueue.Count >= 10)
                    {
                        NoDiscountBroadQueue.Dequeue();
                    }
                }
            }
            else if (good_record.CanBeCut && good_record.NowPrice <= good_record.GetGoodSetting(this).Price * 0.3)
            {
                broad_str = string.Format(LowDiscountWords.Random(), character.Name, good_record.NowPrice, good_record.GetGoodSetting(this).Award.Spec.Name);
                LowDiscountBroadQueue.Enqueue(broad_str);
                if (LowDiscountBroadQueue.Count >= 20)
                {
                    LowDiscountBroadQueue.Dequeue();
                }
            }
            if (!string.IsNullOrEmpty(broad_str))
            {
                GameApplication.UserManager.ShowRollMessage(broad_str, type : 1);
            }
            good_record.BoughtStatus = BingxixiGoodBuyStatus.已购买;
            record.Save();
            character.GetAwards(good_record.GetGoodSetting(this).Award, "购买商品");

            UpdateToClient(character);
        }

        private void CheckToRefresh(Character character)
        {
            var record = GetCharacterRecord(character, true);
            if (record == null) return;
            CheckToRefresh(record);
        }

        private void CheckToRefresh(BingxixiCharacterRecord record, bool force = false)
        {
            lock (record.GoodRecords)
            {
                foreach (var good_rec in record.GoodRecords)
                {
                    if (good_rec.BoughtStatus != BingxixiGoodBuyStatus.未解锁)
                    {
                        continue;
                    }

                    if (!force && DateTimeExtension.Now - good_rec.CreateDate < new TimeSpan(24, 0, 0))
                    {
                        continue;
                    }

                    var base_price = good_rec.GetGoodSetting(this).Price;
                    var now_at_discount = good_rec.NowPrice * 100 / base_price;

                    var reach_discount = NoComplyDiscounts.LastOrDefault(cd => cd > now_at_discount);
                    reach_discount = reach_discount == 0 ? 100 : reach_discount;
                    good_rec.NowPrice = base_price * reach_discount / 100;
                    good_rec.BoughtStatus = BingxixiGoodBuyStatus.可购买;
                }
            }
        }
        public override JsonObject Protocol()
        {
            var res = base.Protocol();

            res[nameof(CutItemID)] = CutItemID;
            var show_panel_wards = new JsonArray();
            foreach (var word in ShowPanelWords)
            {
                show_panel_wards.Add(word);
            }
            res[nameof(ShowPanelWords)] = show_panel_wards;

            return res;
        }
        public override JsonObject Personal(Character character)
        {
            var res = base.Personal(character);

            var record = GetCharacterRecord(character, true);
            if (record == null) return res;

            res.AddRange(record.Proto(this));

            return res;
        }
    }


    [EntityTable(DbGroup.Entity)]
    public class BingxixiCharacterRecord : ActivityCharacterRecord
    {
        private List<BingxixiCharacterGoodsRecord> _goodRecords = new List<BingxixiCharacterGoodsRecord>();
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<BingxixiCharacterGoodsRecord>))]
        public List<BingxixiCharacterGoodsRecord> GoodRecords
        {
            set
            {
                _goodRecords = value;
                _goodRecordsDic = null;
            }
            get
            {
                return _goodRecords;
            }
        }

        private ConcurrentDictionary<string, BingxixiCharacterGoodsRecord> _goodRecordsDic;
        public ConcurrentDictionary<string, BingxixiCharacterGoodsRecord> GoodRecordsDic
        {
            get
            {
                if (_goodRecordsDic == null)
                {
                    _goodRecordsDic = new ConcurrentDictionary<string, BingxixiCharacterGoodsRecord>();
                    lock (GoodRecords)
                    {
                        foreach (var rec in GoodRecords)
                        {
                            _goodRecordsDic.TryAdd(rec.RecordID, rec);
                        }
                    }
                }
                return _goodRecordsDic;
            }
        }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<int>))]
        public List<int> WaitForChooseIds { set; get; } = new List<int>();

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<string>))]
        public List<string> CutGoodIds { set; get; } = new List<string>();

        [TableColumn]
        public DateTime LastRefreshTime { set; get; } = DateTimeExtension.MinTime;

        public HashSet<long> HelpingHands = new HashSet<long>();

        public BingxixiGoodSetting GetGoodSetting(BingxixiBargainActivity act, int goodId)
        {
            if (act.GoodSettingDic.TryGetValue(goodId, out var res))
            {
                return res;
            }

            return null;
        }

        public JsonObject Proto(BingxixiBargainActivity act)
        {
            var res = new JsonObject();

            res[nameof(GoodRecords)] = new JsonArray(GoodRecords.Select(g => g.BaseProto(act)));

            var cut_good_ids = new JsonArray();
            foreach (var id in CutGoodIds)
            {
                cut_good_ids.Add(id);
            }
            res[nameof(CutGoodIds)] = cut_good_ids;

            var wait_for_choose_ids = new JsonArray();
            foreach (var id in WaitForChooseIds)
            {
                if (act.GoodSettingDic.TryGetValue(id, out var good))
                {
                    wait_for_choose_ids.Add(good.Proto(act));
                }
            }
            res["WaitForChoose"] = wait_for_choose_ids;
            res[nameof(LastRefreshTime)] = LastRefreshTime.ToTimestamp();

            return res;
        }
    }

    public class BingxixiCharacterGoodsRecord : JsonFormatObject
    {
        [JsonMember]
        public string RecordID { set; get; }

        [JsonMember]
        public override long ID { set; get; } = long.MaxValue;

        [JsonMember]
        public int GoodID { set; get; }

        [JsonMember]
        public bool CanBeCut { set; get; }

        [JsonMember]
        public int CutCount { set; get; }

        [JsonMember]
        public int NowPrice { set; get; }

        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> Cuts { set; get; } = new List<int>();

        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> CutCharacters { set; get; } = new List<string>();


        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime CreateDate { set; get; }

        [JsonMember]
        public BingxixiGoodBuyStatus BoughtStatus { set; get; }

        public BingxixiGoodSetting GetGoodSetting(BingxixiBargainActivity act)
        {
            if (act.GoodSettingDic.TryGetValue(GoodID, out var res))
            {
                return res;
            }

            return null;
        }

        public void InitCuts(BingxixiBargainActivity act)
        {
            var setting = GetGoodSetting(act);
            Cuts.Clear();
            if (!act.BargainSettingsDic.TryGetValue(setting?.BargainID ?? 0, out var cut_temp))
            {
                return;
            }

            static List<int> Shuffle(List<int> list)
            {
                Random rng = new Random();
                int n = list.Count;
                for (int i = n - 1; i > 0; i--)
                {
                    int j = rng.Next(i + 1);
                    int temp = list[i];
                    list[i] = list[j];
                    list[j] = temp;
                }

                return list;
            }

            foreach (var cut_unit in cut_temp.TemplateUnits.OrderBy(t => t.Index))
            {
                var base_price = cut_unit.BasePrice;
                var cut_points = new List<int>();
                for (int i = 0; i < cut_unit.EffectDiscountCount - 1; i++)
                {
                    var rand_index = RandomEvent.Next(1, base_price + 1);
                    //下面处理过0 所以这里无需再处理
                    //if (cut_points.Contains(rand_index))
                    //{
                    //    i++;
                    //    continue;
                    //}
                    cut_points.Add(rand_index);
                }
                cut_points = cut_points.OrderBy(s => s).ToList();
                var temp_cuts = new List<int>();
                var cut_value_0 = (cut_unit.BasePrice - cut_points[cut_points.Count - 1]) * (RandomEvent.Next(100 - cut_unit.FloatDiscount, 100 + cut_unit.FloatDiscount + 1)) / 100;

                if (cut_value_0 <= 0)
                {
                    cut_value_0 = 1;
                }
                temp_cuts.Add(cut_value_0);
                for (int i = cut_points.Count - 1; i > 0; i--)
                {
                    var cut_value = (cut_points[i] - cut_points[i - 1]) * (RandomEvent.Next(100 - cut_unit.FloatDiscount, 100 + cut_unit.FloatDiscount + 1)) / 100;
                    if (cut_value <= 0)
                    {
                        cut_value = 1;
                    }
                    temp_cuts.Add(cut_value);
                    if (i == 1)
                    {
                        var cut_value_end = cut_points[i - 1] * (RandomEvent.Next(100 - cut_unit.FloatDiscount, 100 + cut_unit.FloatDiscount + 1)) / 100;
                        if (cut_value_end <= 0)
                        {
                            cut_value_end = 1;
                        }
                        temp_cuts.Add(cut_value_end);
                    }
                }
                temp_cuts = Shuffle(temp_cuts);
                Cuts.AddRange(temp_cuts);
            }
        }

        public JsonObject BaseProto(BingxixiBargainActivity act)
        {
            var res = GetGoodSetting(act)?.Proto(act);

            res[nameof(ID)] = RecordID;
            res[nameof(GoodID)] = GoodID;
            res[nameof(CanBeCut)] = CanBeCut;
            res[nameof(NowPrice)] = NowPrice;
            res[nameof(CreateDate)] = CreateDate.ToTimestamp();
            res[nameof(BoughtStatus)] = (int)BoughtStatus;

            if (act.BargainSettingsDic.TryGetValue(GetGoodSetting(act)?.BargainID ?? 0, out var cut_temp))
            {
                res["CutTotalCount"] = cut_temp.TemplateUnits.Select(t => t.EffectDiscountCount).Sum();
            }

            return res;
        }

        public JsonObject PanelProto(BingxixiBargainActivity act)
        {
            var res = BaseProto(act);

            var cut_chara_records = new JsonArray();

            for (int i = 0; i < CutCharacters.Count; i++)
            {
                var jo = new JsonObject();
                jo["CharacterName"] = CutCharacters[i];
                jo["CutDiscount"] = Cuts[i];
                cut_chara_records.Add(jo);
            }

            res["CutCharacters"] = cut_chara_records;

            return res;
        }
    }

    public enum BingxixiGoodBuyStatus
    {
        未解锁,
        可购买,
        已购买
    }

    public class BingxixiGoodSetting : JsonFormatObject
    {
        [WebMember("商品ID")]
        [JsonMember]
        public int GoodID { set; get; }

        [WebMember("商品名")]
        [JsonMember]
        public string GoodName { set; get; }

        [WebMember("初始价格")]
        [JsonMember]
        public int Price { set; get; }

        [WebMember("可能出现的栏位")]
        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> AppearIndexes { set; get; }

        [WebMember("奖品")]
        [JsonMember(FormatType = typeof(JsonDataFormat<ObjectAward>))]
        public ObjectAward Award { set; get; }

        [WebMember("稻草人配置集")]
        [JsonMember(FormatType = typeof(JsonListFormat<KakashiType>))]
        public List<KakashiType> Kakashies { get; set; }

        [WebMember("初始最低折扣")]
        [JsonMember]
        public int MinStartDiscount { set; get; }

        [WebMember("初始最高折扣")]
        [JsonMember]
        public int MaxStartDiscount { set; get; }

        [WebMember("砍刀模板ID")]
        [JsonMember]
        public int BargainID { set; get; }

        public JsonObject Proto(BingxixiBargainActivity act)
        {
            var res = new JsonObject();

            res[nameof(GoodName)] = GoodName;
            res[nameof(GoodID)] = GoodID;
            res["BasePrice"] = Price;
            res[nameof(Award)] = Award.Spec.ProtoData.ToJson();
            res[nameof(Kakashies)] = new JsonArray(Kakashies.Select(k => k.ToClientJson()));

            if (act.BargainSettingsDic.TryGetValue(BargainID, out var cut_temp))
            {
                res["CutTotalCount"] = cut_temp.TemplateUnits.Select(t => t.EffectDiscountCount).Sum();
            }

            return res;
        }
    }

    public class BingxixiBargainTemplate : JsonFormatObject
    {
        [WebMember("模板ID")]
        [JsonMember]
        public int BargainID { set; get; }

        [WebMember("砍一刀配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<BingxixiBargainTemplateUnit>))]
        public List<BingxixiBargainTemplateUnit> TemplateUnits { set; get; } = new List<BingxixiBargainTemplateUnit>();
    }

    public class BingxixiBargainTemplateUnit : JsonFormatObject
    {
        [WebMember("序列")]
        [JsonMember]
        public int Index { set; get; }

        [WebMember("生效刀数")]
        [JsonMember]
        public int EffectDiscountCount { set; get; }

        [WebMember("基准价格")]
        [JsonMember]
        public int BasePrice { set; get; }

        [WebMember("浮度")]
        [JsonMember]
        public int FloatDiscount { set; get; }
    }
}
