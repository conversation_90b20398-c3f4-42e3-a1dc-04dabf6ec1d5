using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Json;
using System.Linq;
using System.Text;
using AppLibrary.Biff.Drawing;
using DataBase;
using GameCapsule.ConfigObjects;
using GameFramework.Linq;
using GameModel.Award;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using Google.Protobuf.WellKnownTypes;
using HelpBase;
using HelpBase.Json;
using HelpBase.Linq;
using HelpBase.Web;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Activities
{
    [WebClass("抽卡活动")]
    public class DrawCardActivity : ActivityWithSpecialShop<DrewCardRecord>, IInteractionActivity, INeedExtraItemsShopActivity
    {
        //public override ActivityShowType ClientShowType => ActivityShowType.活动中心并推送;

        [WebMember("货币类型")]
        [JsonMember]
        public long VirtualCurrencyType { get; set; }

        [JsonMember]
        [WebMember("是否是常驻卡池")]
        public bool AllwaysOpenDrawCardAct { set; get; }
        public override bool AllwaysOpen => AllwaysOpenDrawCardAct;
        public override bool UniquelyExist => false;

        [JsonMember]
        [WebMember("是否显示兑换商店")]
        public bool ShowExchangeShop { set; get; }

        private List<DayOfWeek> showdays = new List<DayOfWeek>();
        [WebMember("显示日期")]
        [JsonMember(FormatType = typeof(JsonListFormat<DayOfWeek>))]
        public List<DayOfWeek> ShowDays
        {
            set
            {
                showdays = value;
            }
            get
            {
                if (VirtualCurrencyType == 0)
                {
                    return showdays;
                }
                else
                {
                    return new List<DayOfWeek>() {
                        DayOfWeek.Sunday,
                        DayOfWeek.Monday,
                        DayOfWeek.Tuesday,
                        DayOfWeek.Wednesday,
                        DayOfWeek.Thursday,
                        DayOfWeek.Friday,
                        DayOfWeek.Saturday,
                    };
                }
            }
        }

        [WebMember("抽卡配置集")]
        [JsonMember(FormatType = typeof(JsonListFormat<DrewCardType>))]
        public List<DrewCardType> Pools { get; set; }
        [WebMember("背景图地址")]
        [JsonMember]
        public string BackGroudPicture { get; set; }

        [WebMember("活动Icon图地址")]
        [JsonMember]
        public string GachaIconPicture { get; set; }

        [WebMember("模板场景ID")]
        [JsonMember]
        public long TemplateSceneId { get; set; }

        [WebMember("稻草人配置集")]
        [JsonMember(FormatType = typeof(JsonListFormat<KakashiType>))]
        public List<KakashiType> Kakashies { get; set; }

        [WebMember("抽卡奖券")]
        [JsonMember]
        public long TicketType { get; set; }

        [WebMember("单抽价格")]
        [JsonMember]
        public int SinglePrice { get; set; }

        [WebMember("额外奖励id")]
        [JsonMember]
        public long ExtraAward { get; set; }

        [WebMember("NPC商店ID")]
        [JsonMember]
        public long NPCShopID { get; set; }


        public static int DailyLimit { get; set; } = 1000; // -1 表示无限

        public override bool NeedClient => true;

        public virtual bool ShowActivityAfterDraw => true;
        public virtual AwardHistoryType AwardRecordType => AwardHistoryType.None;

        public virtual bool ShowItemPool => VirtualCurrencyType == 1;

        public DayOfWeek day_of_Week;

        private bool CheckAvailable()
        {
            return ShowDays.Contains(DateTimeExtension.Now.GetExtensionDayOfWeek());
        }

        public virtual void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID, byte[] bytes)
        {
            if (Owner.Phase != ActivityPhase.开启 || !CheckAvailable())
            {
                character.ShowError(LocalityStrings.activityNotOpen);
                return;
            }

            var logaction = "抽卡";
            var record = GetCharacterRecord(character, true, true);
            var currency_name = GetCurrenyName();

            var ta_log_records = new List<DrawCardTalogRecord>();
            switch (action)
            {
                case 1://抽卡
                    if (DailyLimit > 0 && record.TodayDrawCardTime + 1 > DailyLimit)
                    {
                        character.ShowErrorTipsMessage("今日抽卡已达到限额");
                        return;
                    }
                    bool isFree = false;
                    if (VirtualCurrencyType == 1)
                    {
                        logaction = currency_name + logaction;
                        if (character.SilverPoolFreeSingleStatus <= DateTimeExtension.Now.ToTimestamp())
                        {
                            character.SilverPoolFreeSingleStatus = DateTimeExtension.Now.AddHours(24).ToTimestamp();
                            character.Save();
                            isFree = true;
                            logaction = "免费" + logaction;
                        }
                    }
                    else
                    {
                        logaction = currency_name + logaction;
                    }
                    var extra_award_spec = new List<AwardSpec>();
                    if (ExtraAward > 0)
                    {
                        extra_award_spec.AddRange(character.GetSystemAward(ExtraAward, logaction));
                    }
                    logaction = logaction + "单抽";
                    if (!CutDrawItems(character, 1, logaction, out ta_log_records, isFree)) return;
                    //var pack = new HighLightAwardSpecs();
                    //pack.Cate = HighLightAwardCategory.Gacha;
                    //pack.Awards.Add(DrewCard(character, logaction));
                    //pack.ExtraAwards.AddRange(extra_award_spec.Select(e => e.ProtoData));
                    //character.SendGoogleProto(ClientMethod.HighLightAwardSpecs, pack);
                    //pack.Awards.ForEach(a =>
                    //{
                    //    if (a.Category == ObjectSpecCategory.Object)
                    //    {
                    //        character.CheckToShowGalleryProcessPanel(GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(a.Type).GetGalleryId(), ClientNotificationMode.New);
                    //    }
                    //});
                    var draw_res = new List<AwardSpecData>();
                    DrawCardTalogRecord s_record;
                    draw_res.Add(DrewCard(character, logaction, ta_log_records[0], out s_record));
                    ta_log_records[0] = s_record;
                    character.SendHighLightAwards(draw_res, HighLightAwardCategory.Gacha, extra_award_spec.Select(e => e.ProtoData).ToList());
                    ta_log_records.ForEach(record =>
                    {
                        record.Num = 1;
                    });
                    //if (VirtualCurrencyType == 1)
                    //{
                    //    character.AddProperty(1019, 1, logaction);
                    //}
                    //else
                    //{
                    //    character.AddProperty(1019, 3, logaction);
                    //}

                    //if (isFree)
                    //{
                    //    //var data = Owner.GetProtoData(character);
                    //    Logger.TA.WriteJson(new LeiTingLogEvent(character.ID.ToString(), character.Name, character.ThisLoginIP, "free_gacha")
                    //    {
                    //        Properties = character.GetLeiTingLogProperties()
                    //       .Add("num", 1)
                    //    });
                    //}
                    var data = Owner.GetProtoData(character);
                    if (ShowActivityAfterDraw)
                    {
                        character.SendGoogleProto(ClientMethod.ShowActivity, data, CompressType.GZip);
                    }
                    break;
                case 2://十连抽卡
                    if (DailyLimit > 0 && record.TodayDrawCardTime + 10 > DailyLimit)
                    {
                        character.ShowErrorTipsMessage("今日抽卡已达到限额");
                        return;
                    }
                    bool isFree0 = false;
                    //if (VirtualCurrencyType == 1 && character.SilverPoolFreeTenTimesStatus <= DateTimeExtension.Now.ToTimestamp())
                    //{
                    //    character.SilverPoolFreeTenTimesStatus = DateTimeExtension.Now.AddHours(72).ToTimestamp();
                    //    character.Save();
                    //    isFree0 = true;
                    //}
                    if (VirtualCurrencyType == 1)
                    {
                        logaction = currency_name + logaction;
                        if (character.SilverPoolFreeTenTimesStatus <= DateTimeExtension.Now.ToTimestamp())
                        {
                            character.SilverPoolFreeTenTimesStatus = DateTimeExtension.Now.AddHours(72).ToTimestamp();
                            character.Save();
                            isFree0 = true;
                            logaction = "免费" + logaction;
                        }
                    }
                    else
                    {
                        logaction = currency_name + logaction;
                    }
                    logaction = logaction + "十连";
                    if (!CutDrawItems(character, 10, logaction, out ta_log_records, isFree0)) return;
                    //var pack1 = new HighLightAwardSpecs();
                    //pack1.Cate = HighLightAwardCategory.Gacha;

                    var draw_res1 = new List<AwardSpecData>();
                    if (!character.DrewCoinTime && VirtualCurrencyType == 1)
                    {
                        character.DrewCoinTime = true;
                        character.Save();
                        var awardres = character.GetSystemAward(1956, "首次" + logaction, merge: false);

                        for (int i = 0; i < 10; i++)
                        {
                            ta_log_records[i].DrawCumulativePoint = 0;
                            ta_log_records[i].DrawNo = "0";
                            ta_log_records[i].DrawType = "首次免费十连";
                            ta_log_records[i].DrawCumulativePoint = 0;
                            ta_log_records[i].ItemName = awardres[i].Name == "" ? I18nManager.SceneObjectName(awardres[i].Type) : awardres[i].Name;
                            ta_log_records[i].ItemId = awardres[i].Type.ToTaLogID();
                        }
                        foreach (var spec in awardres)
                        {
                            draw_res1.Add(spec.ProtoData);
                        }
                        character.DrawCardTimes += 10;
                        record.TodayDrawCardTime += 10;
                        record.Save();
                        character.CheckUpdateAction(character, UserAction.抽卡, VirtualCurrencyType == 0 ? 3 : VirtualCurrencyType, value: 10);
                    }
                    else
                    {
                        for (int i = 0; i < 10; i++)
                        {
                            DrawCardTalogRecord tt_record;
                            draw_res1.Add(DrewCard(character, logaction, ta_log_records[i], out tt_record));
                            ta_log_records[i] = tt_record;
                        }
                    }
                    var extra_award_spec1 = new List<AwardSpec>();
                    if (ExtraAward > 0)
                    {
                        for (int i = 0; i < 10; i++)
                        {
                            extra_award_spec1.AddRange(character.GetSystemAward(ExtraAward, logaction));
                        }
                    }
                    ta_log_records.ForEach(record =>
                    {
                        record.Num = 10;
                    });
                    //pack1.Awards.ForEach(a =>
                    //{
                    //    if (a.Category == ObjectSpecCategory.Object)
                    //    {
                    //        character.CheckToShowGalleryProcessPanel(GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(a.Type).GetGalleryId(), ClientNotificationMode.New);
                    //    }
                    //});
                    //pack1.ExtraAwards.AddRange(extra_award_spec1.Select(e => e.ProtoData));

                    //draw_res.Add(DrewCard(character, logaction));
                    character.SendHighLightAwards(draw_res1, HighLightAwardCategory.Gacha, extra_award_spec1.Select(e => e.ProtoData).ToList());

                    //character.SendGoogleProto(ClientMethod.HighLightAwardSpecs, pack1);
                    //if (VirtualCurrencyType == 1)
                    //{
                    //    character.AddProperty(1019, 10, logaction);
                    //}
                    //else
                    //{
                    //    character.AddProperty(1019, 30, logaction);
                    //}
                    //if (isFree0)
                    //{
                    //    //character.SendGoogleProto(ClientMethod.ShowActivity, data);
                    //    Logger.TA.WriteJson(new LeiTingLogEvent(character.ID.ToString(), character.Name, character.ThisLoginIP, "free_gacha")
                    //    {
                    //        Properties = character.GetLeiTingLogProperties()
                    //       .Add("num", 10)
                    //    });
                    //}
                    var data0 = Owner.GetProtoData(character);
                    if (ShowActivityAfterDraw)
                    {
                        character.SendGoogleProto(ClientMethod.ShowActivity, data0, CompressType.GZip);
                    }
                    break;
                case 3://进入样板房
                    var scene_type = GameApplication.DataManager.FindValue<SceneType>(TemplateSceneId);
                    if (scene_type == null)
                    {
                        Logger.Error.Write("Enter Model House: cannot find Target Scene Type:" + TemplateSceneId);
                        return;
                    }
                    var templateHouseDimension = GameApplication.SceneManager.FirstDimension(p => p.Type == DimensionType.CardPoolTemp);
                    var dimensionLevel = templateHouseDimension.GetLevel(scene_type.ID);
                    if (dimensionLevel == null)
                    {
                        Logger.Error.Write("Enter Model House: Cannot find dimensionLevel: " + scene_type.ID);
                        return;
                    }
                    if (!templateHouseDimension.Enter(character.SceneCharacter, dimensionLevel))
                    {
                        Logger.Error.Write("Cannot enter model house: " + TemplateSceneId);
                        return;
                    }
                    break;
                case 4://指定次数抽卡(仅用于仅道具池)
                    var times = ProtoSerializer.Deserialize<Int32Message>(bytes).Value;
                    if (VirtualCurrencyType > 0)
                    {
                        return;
                    }
                    if (DailyLimit > 0 && record.TodayDrawCardTime + times > DailyLimit)
                    {
                        character.ShowErrorTipsMessage("今日抽卡已达到限额");
                        return;
                    }
                    logaction = currency_name + logaction;
                    logaction = logaction + times + "连抽";
                    if (!CutDrawItems(character, times, logaction, out ta_log_records, false)) return;
                    //var pack2 = new HighLightAwardSpecs();
                    var draw_res2 = new List<AwardSpecData>();
                    //pack2.Cate = HighLightAwardCategory.Gacha;
                    for (int i = 0; i < times; i++)
                    {
                        DrawCardTalogRecord st_record;
                        draw_res2.Add(DrewCard(character, logaction, ta_log_records[i], out st_record));
                        ta_log_records[i] = st_record;
                    }

                    ta_log_records.ForEach(record =>
                    {
                        record.Num = times;
                    });
                    //pack2.Awards.ForEach(a =>
                    //{
                    //    if (a.Category == ObjectSpecCategory.Object)
                    //    {
                    //        character.CheckToShowGalleryProcessPanel(GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(a.Type).GetGalleryId(), ClientNotificationMode.New);
                    //    }
                    //});
                    //character.AddProperty(1019, 3 * times, logaction);
                    var extra_award_spec2 = new List<AwardSpec>();
                    if (ExtraAward > 0)
                    {
                        for (int i = 0; i < times; i++)
                        {

                            extra_award_spec2.AddRange(character.GetSystemAward(ExtraAward, logaction));
                        }
                    }
                    character.SendHighLightAwards(draw_res2, HighLightAwardCategory.Gacha, extra_award_spec2.Select(e => e.ProtoData).ToList());

                    //pack2.ExtraAwards.AddRange(extra_award_spec2.Select(e => e.ProtoData));
                    //character.SendGoogleProto(ClientMethod.HighLightAwardSpecs, pack2);
                    var data2 = Owner.GetProtoData(character);
                    character.SendGoogleProto(ClientMethod.ShowActivity, data2, CompressType.GZip);
                    break;
                default:
                    character.ShowError(LocalityStrings.argumentOutOfRangeError);
                    return;
            }
            //ta_log_records.ForEach(record =>
            //{
            //    record.ActivityID = ID.ToTaLogID();
            //    record.ActivityName = Name;
            //    record.WriteLog(character);
            //});
            WriteLog(character, ta_log_records);
        }

        private void WriteLog(Character character, List<DrawCardTalogRecord> records)
        {
            if (VirtualCurrencyType == 0 || VirtualCurrencyType == 1)
            {
                return;
            }
            if (Constants.AllowTaLog)
            {
                character.GetLeiTingTrackWriter("draw_card_data")
                .WriteEntry("draw_cost_type", VirtualCurrencyType)
                .WriteEntry("activity_id", ID)
                .WriteEntry("activity_name", Owner.Name)
                .WriteEntry("records", (new JsonArray(records.Select(r => r.GetJsonValue()))).ToString())
                .WriteToTaLogger();
            }
        }

        public string GetActionNameByAction(byte action)
        {
            return "";
        }
        private string GetCurrenyName()
        {
            if (VirtualCurrencyType > 0)
            {
                return I18nManager.VirtualCurrencyName(VirtualCurrencyType);
            }
            else
            {
                return I18nManager.ItemName(TicketType);
            }
        }

        protected virtual bool CutDrawItems(Character character, int drawTimes, string action, out List<DrawCardTalogRecord> res_records, bool isFree = false)
        {
            res_records = new List<DrawCardTalogRecord>();
            for (int i = 0; i < drawTimes; i++)
            {
                res_records.Add(new DrawCardTalogRecord());
            }
            if (isFree)
            {
                res_records.ForEach(record =>
                {
                    record.DrawCostType = "免费";
                    record.DrawCostNum = 0;
                });
                drawTimes = 0;
            }

            var ticketType = TicketType;
            int gotTicketsCount = 0;
            var gotTickets = character.FindEntities<Item>(i => i.Type == ticketType).FirstOrDefault();
            if (gotTickets != null)
            {
                gotTicketsCount = gotTickets.Count;
            }

            if (gotTicketsCount >= drawTimes && character.DeleteEntity(ticketType, drawTimes, action))
            {
                var ticket_conf = GameApplication.DataManager.FindValue<EntityType, ItemType>(ticketType);
                //非免费
                if (drawTimes > 0)
                {
                    res_records.ForEach(record =>
                    {
                        record.DrawCostType = ticket_conf.Name;
                        record.DrawCostNum = 1;
                    });
                }
                return true;
            }
            if (VirtualCurrencyType == 0 && drawTimes > gotTicketsCount)
            {
                return false;
            }
            var currencyToCount = SinglePrice * (drawTimes - gotTicketsCount);
            if (character.CheckVirtualCurrencyCountEnough(VirtualCurrencyType, currencyToCount) && character.DeleteEntity(ticketType, gotTicketsCount, action))
            {
                if (currencyToCount > 0)
                {
                    character.Pay(VirtualCurrencyType, currencyToCount, action);
                }
                var ticket_conf = GameApplication.DataManager.FindValue<EntityType, ItemType>(ticketType);
                var virtual_cur_conf = GameApplication.DataManager.FindValue<VirtualCurrencyType>(VirtualCurrencyType);

                //非免费
                if (drawTimes > 0)
                {
                    for (int i = 0; i < drawTimes; i++)
                    {
                        if (i < gotTicketsCount)
                        {
                            res_records[i].DrawCostType = ticket_conf.Name;
                            res_records[i].DrawCostNum = 1;
                        }
                        else
                        {
                            res_records[i].DrawCostType = virtual_cur_conf.Name;
                            res_records[i].DrawCostNum = SinglePrice;
                        }
                    }
                }
                return true;
            }

            return false;
        }

        public override JsonObject Personal(Character character)
        {
            var json = base.Personal(character);
            json["SilverPoolFreeSingleStatus"] = character.SilverPoolFreeSingleStatus;
            json["SilverPoolFreeTenTimesStatus"] = character.SilverPoolFreeTenTimesStatus;
            var reccord = GetCharacterRecord(character, true, true);
            json[nameof(reccord.TodayDrawCardTime)] = reccord.TodayDrawCardTime;

            return json;
        }

        public override JsonObject Protocol()
        {
            var json = base.Protocol();
            //if (Pools != null)
            //{
            //    json[nameof(Pools)] = new JsonArray(Pools.Select(p => p.ToClientJson()));
            //}
            if (Kakashies != null)
            {
                json[nameof(Kakashies)] = new JsonArray(Kakashies.Select(p => p.ToClientJson()));
            }
            json[nameof(BackGroudPicture)] = BackGroudPicture;
            json[nameof(GachaIconPicture)] = GachaIconPicture;
            json[nameof(TicketType)] = TicketType;
            json[nameof(VirtualCurrencyType)] = VirtualCurrencyType;
            json[nameof(SinglePrice)] = SinglePrice;
            json[nameof(ShowExchangeShop)] = ShowExchangeShop;

            json[nameof(NPCShopID)] = NPCShopID;

            var showdays = new JsonArray();
            ShowDays.ForEach(s => showdays.Add((int)s));
            json[nameof(ShowDays)] = showdays;
            json[nameof(TemplateSceneId)] = TemplateSceneId;
            json["Available"] = CheckAvailable();
            return json;
        }

        public override JsonObject LazyDetailProtocol()
        {
            return base.LazyDetailProtocol();
        }

        private AwardSpecData DrewCard(Character character, string action, DrawCardTalogRecord ta_record, out DrawCardTalogRecord res_ta_record)
        {
            res_ta_record = ta_record;
            var res = new List<long>();
            var randomList = GetRealCardPool(character);
            var record = GetCharacterRecord(character);

            var randomPool = randomList.GetRandomItem();


            foreach (var pool in Pools)
            {
                //没有记录就创建
                if (!record.Record.NoNewTimes.ContainsKey(pool.Id))
                {
                    record.Record.NoNewTimes[pool.Id] = 0;
                }
                //记录次数 抽到就归零
                if (pool.Id == randomPool.Id)
                {
                    if (randomPool.HasABottom)
                    {
                        //鉴定为保底道具
                        res_ta_record.ItemIsGuaranTee = true;
                        if (randomPool.Threshold <= record.Record.NoNewTimes[pool.Id])
                        {
                            //触发保底记录
                            res_ta_record.DrawIsGuaranTee = true;
                        }
                    }
                    res_ta_record.DrawNo = pool.Id.ToString();
                    res_ta_record.DrawType = VirtualCurrencyType == 0 ? "元素卡池" : LimitTimeActivityFlagType.ToString();
                    res_ta_record.DrawCumulativePoint = record.Record.NoNewTimes[pool.Id];
                    record.Record.NoNewTimes[pool.Id] = 0;
                }
                else
                {
                    record.Record.NoNewTimes[pool.Id]++;
                }

            }

            character.DrawCardTimes++;

            record.TodayDrawCardTime++;
            record.Save();
            string rec_type = null;
            if (AwardRecordType != AwardHistoryType.None)
            {
                rec_type = AwardRecordType.ToString();
            }

            //给奖励
            if (randomPool.Type == DrewCardPoolType.道具)
            {
                var award = character.GetSystemAward(randomPool.ItemAwardId, action, "", randomPool.ItemAwardId, 1, null, AwardProbabilityMode.Global, merge: false, recType:rec_type);
                character.CheckUpdateAction(character, UserAction.抽卡, VirtualCurrencyType == 0 ? 3 : VirtualCurrencyType);
                res_ta_record.ItemName = award[0].Name == "" ? I18nManager.SceneObjectName(award[0].Type) : award[0].Name;
                res_ta_record.ItemId = award[0].Type.ToTaLogID();
                return award[0].ProtoData;
            }
            var randomAward = randomPool.Awards.Random();

            //保new重新计算
            bool isRepeat = false;
            if (randomPool.Type == DrewCardPoolType.家具)
            {
                isRepeat = character.FindEntities<SceneObject>(s => s.Type == randomAward.Type).Count > 0;
            }
            else if (randomPool.Type == DrewCardPoolType.装备)
            {
                isRepeat = character.FindEntities<Equip>(s => s.Type == randomAward.Type).Count > 0;
            }

            if (isRepeat)
            {
                if (randomPool.NextNewTime > 0)
                    record.Record.NextNewTimes[randomPool.Id] = randomPool.NextNewTime;
            }
            else
            {
                if (record.Record.NextNewTimes.ContainsKey(randomPool.Id))
                    record.Record.NextNewTimes[randomPool.Id]--;
            }


            character.CheckUpdateAction(character, UserAction.抽卡, VirtualCurrencyType == 0 ? 3 : VirtualCurrencyType);

            res.Add(randomAward.Type);

            //character.CreateEntity(randomAward, 1, null, action, "" + Owner.ID + ":" + randomPool.Id);
            character.GetAwards(randomAward, action, "" + Owner.ID + ":" + randomPool.Id,
                processMode: ClientNotificationMode.Ignore, recType: rec_type);
            res_ta_record.ItemName = randomAward.Spec.Name;
            res_ta_record.ItemId = randomAward.Spec.Type.ToTaLogID();
            return randomAward.Spec.ProtoData;
        }

        //对于现有的纯随机池子进行改造
        private RandomList<DrewCardType> GetRealCardPool(Character character)
        {
            var res = new RandomList<DrewCardType>();
            var record = GetCharacterRecord(character, true, true);

            foreach (var pool in Pools)
            {
                var tempPool = new DrewCardType();
                if (pool.Type == DrewCardPoolType.装备)
                {
                    tempPool.CopyButItems(pool);
                    //tempPool.Awards.AddRange(pool.Awards.Where(i => GameApplication.DataManager.FindValue<EntityType, EquipType>(i).Gender == character.Gender && character.FindEntity<Equip>(i) == null));
                    foreach (var avaId in pool.Awards)
                    {
                        var equip = GameApplication.DataManager.FindValue<EntityType, EquipType>(avaId.Type);
                        if (equip.Gender != Gender.无 && equip.Gender != character.Gender /*&& character.FindEntities<Equip>(s => s.Type == avaId.Type).Count == 0*/)
                        {
                            continue;
                        }
                        if (record.Record.NextNewTimes.ContainsKey(pool.Id) && record.Record.NextNewTimes[pool.Id] > 0 && character.FindEntities<Equip>(e => e.Type == avaId.Type).Count > 0)
                        {
                            continue;
                        }

                        tempPool.Awards.Add(avaId);
                        //tempPool.Awards.Add(avaId);
                    }
                    //foreach (var equip in pool.Awards)
                    //{
                    //    if (character.FindEntity<Equip>(equip) == null)
                    //    {
                    //        tempPool.Awards.Add(equip);
                    //    }
                    //}
                }

                if (pool.Type == DrewCardPoolType.家具)
                {
                    tempPool.CopyButItems(pool);
                    //无new或者有new且抽取完全，都会拿到整个池子，否则拿到的是new
                    if (record.Record.NextNewTimes.ContainsKey(pool.Id) && record.Record.NextNewTimes[pool.Id] > 0)
                    {
                        //if (pool.Type == DrewCardPoolType.家具)
                        //{
                        //    tempPool.Awards.AddRange(pool.Awards.Where(i => character.FindEntities<SceneObject>(s => s.Type == i.Type).Count == 0));
                        //}
                        tempPool.Awards.AddRange(pool.Awards.Where(i => character.FindEntities<SceneObject>(s => s.Type == i.Type).Count == 0));
                        if (tempPool.Awards.Count == 0)
                        {
                            tempPool.Awards.AddRange(pool.Awards);
                        }
                    }
                    else
                    {
                        tempPool.Awards.AddRange(pool.Awards);
                    }
                }
                if (pool.Type == DrewCardPoolType.道具)
                {
                    tempPool.CopyButItems(pool);
                }

                //概率自增
                if (pool.SelfIncrease)
                {
                    var drewTime = record.Record.NoNewTimes.ContainsKey(pool.Id) ? record.Record.NoNewTimes[pool.Id] : 0;
                    tempPool.Weight += tempPool.Weight * (drewTime / (tempPool.Threshold > drewTime ? tempPool.Threshold - drewTime : 1));
                }

                if (pool.Type == DrewCardPoolType.道具 || tempPool.Awards.Count != 0)
                {
                    res.Add(tempPool);
                }

                //保底
                if (pool.HasABottom && record.Record.NoNewTimes.ContainsKey(pool.Id) && record.Record.NoNewTimes[pool.Id] >= pool.Threshold /*&& tempPool.Awards.Count > 0*/)
                {
                    res.Clear();
                    res.Add(tempPool);
                    return res;
                }
            }

            return res;
        }

        public override bool Validate(out string msg)
        {
            var poolIds = new List<long>();
            foreach (var pool in Pools)
            {
                if (poolIds.Contains(pool.Id))
                {
                    msg = "奖池id重复！";
                    return false;
                }
                poolIds.Add(pool.Id);
                foreach (var award in pool.Awards)
                {
                    switch (pool.Type)
                    {
                        case DrewCardPoolType.家具:
                            if (GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(award.Type) == null)
                            {
                                msg = "目标家具找不到,id为" + award.Type + ", 奖池为" + pool.Id;
                                return false;
                            }
                            break;
                        case DrewCardPoolType.装备:
                            if (GameApplication.DataManager.FindValue<EntityType, EquipType>(award.Type) == null)
                            {
                                msg = "目标avatar找不到,id为" + award.Type + ", 奖池为" + pool.Id;
                                return false;
                            }
                            break;
                        case DrewCardPoolType.道具:
                            if (GameApplication.DataManager.FindValue<SystemAwardType>(pool.ItemAwardId) == null)
                            {
                                msg = "目标systemaward找不到, id为" + pool.ItemAwardId + ", 奖池为" + pool.Id;
                                return false;
                            }
                            break;
                    }
                }
                //if (pool.NextNewTime >= pool.Awards.Count)
                //{
                //    msg = "目标奖池数量不足！id为" + pool.Id;
                //    return false;
                //}
            }
            if (Kakashies != null)
            {
                foreach (var kakashi in Kakashies)
                {
                    foreach (var equip in kakashi.Equips)
                    {
                        if (GameApplication.DataManager.FindValue<EntityType, EquipType>(equip.Type) == null)
                        {
                            msg = "稻草人配置不正确！" + equip.Type;
                            return false;
                        }
                    }
                }
            }
            if (GetCurrenyName() == "")
            {
                msg = "抽奖货币配置有误！";
                return false;
            }
            if (VirtualCurrencyType > 0 && SinglePrice <= 0)
            {
                msg = "单抽价格为0";
                return false;
            }
            //msg = "检测完毕";

            var gallerysets = new Dictionary<long, int>();//字典存储卡池中每个图鉴套系家具个数
            var sceneobjectIds = new HashSet<long>();//存储出现过的家具id
            foreach (var gacha in Pools)//检查家具卡池是否存在图鉴套系缺失以及是否有家具重复
            {
                if (gacha.Type != DrewCardPoolType.家具) continue;//只检查家具卡池
                foreach (var award in gacha.Awards)
                {
                    var id = award.Type;
                    if (sceneobjectIds.Contains(id))//是否有家具重复
                    {
                        msg = "【奖池：" + Name + ")存在重复的家具（家具id" + id + ")】";
                        return false;
                    }
                    sceneobjectIds.Add(id);

                    var Gallery = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(id).GallerySet;

                    if (!gallerysets.ContainsKey(Gallery))
                    {
                        gallerysets[Gallery] = 1; //初始数量为1
                    }
                    else
                    {
                        gallerysets[Gallery] += 1;// 更新该图鉴套系数量
                    }
                }

            }
            var lostgalleryset = new List<long>();//存储缺失的图鉴套系id

            Func<long, List<SceneObjectType>> findSceneObjectList = (set) =>
            {
                if (this.VirtualCurrencyType == 1)
                {
                    // 银币卡池的话，只需要配置四星和五星的家具
                    return GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(a => a.GallerySet == set && a.Quality >= Quality.四星);
                }
                return GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(a => a.GallerySet == set);
            };

            foreach (var set in gallerysets)//检查卡池存在的图鉴套系数量是否与该图鉴套系家具总数相同
            {
                var count = findSceneObjectList(set.Key).Count;
                if (set.Key != 0 && set.Value != count)//数量不匹配
                {
                    lostgalleryset.Add(set.Key);
                }

            }
            if (lostgalleryset.Count != 0 && roamActivityType != RoamActivityType.Element)
            {
                var errorMessage = new StringBuilder();
                int i = 0;
                foreach (var set in lostgalleryset)
                {
                    ++i;
                    var completeidset = findSceneObjectList(set).Select(b => b.ID).ToList();//获取某一GallerySet的所有家具id

                    errorMessage.Append(i).Append("、缺失GallerySet为 ").Append(set).Append(" 的图鉴套系家具：");
                    foreach (var id in completeidset)
                    {
                        if (!sceneobjectIds.Contains(id))
                        {
                            var sceneobjectname = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(id).Name;
                            errorMessage.Append(sceneobjectname).Append("(ID:").Append(id).Append(")；");
                        }
                    }
                }
                msg = "【奖池：《" + Name + "》存在家具图鉴套系缺失 " + errorMessage.ToString() + "】";
                return false;
            }

            return base.Validate(out msg);
        }

        //public override void FromJson(JsonObject jsonObj)
        //{
        //    base.FromJson(jsonObj);
        //    //AddExtraShopItems();
        //}

        public void AddExtraShopItems()
        {
            if(roamActivityType == RoamActivityType.Element && Pools.Count == 0)
            {
                InitializeElementGacha();
            }
            lock (ShopItems)
            {
                foreach (var gacha in Pools)
                {
                    if (gacha.Type == DrewCardPoolType.道具) continue;//只加家具和装备
                    if(gacha.Probability == 0) continue;//权重为0不加入商店
                    foreach (var award in gacha.Awards)
                    {
                        var id = award.Type;
                        ShopItemType shopItem = ShopItems.Find(s => s.Awards.Exists(a => a.Type == id));
                        if (shopItem == null)
                        {
                            shopItem = new ShopItemType();
                            ShopItems.Add(shopItem);
                            shopItem.ShopItemID = id;
                            shopItem.Awards.Add(award);
                        }
                        shopItem.Costs.Clear();
                        if (gacha.Type == DrewCardPoolType.家具)
                        {
                            var sot = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(id);
                            switch (roamActivityType)
                            {
                                case RoamActivityType.Sliver:
                                case RoamActivityType.Diamond:
                                case RoamActivityType.RedDiamond:
                                    {
                                        shopItem.Costs.Add(new ObjectAward(ItemNeedRecord, Constants.DrawCardShop.SceneObjectPrice[sot.Quality]));
                                        if (Constants.DrawCardShop.SceneObjectLimit.ContainsKey(sot.Quality))
                                        {
                                            shopItem.LimitBuy = true;
                                            shopItem.LimitBuyTimes = Constants.DrawCardShop.SceneObjectLimit[sot.Quality];
                                        }
                                        break;
                                    }
                                case RoamActivityType.Element:
                                    {
                                        shopItem.Costs.Add(new ObjectAward(ItemNeedRecord, Constants.DrawCardShop.ElementSceneObjectPrice[sot.Quality]));
                                        //元素卡池家具无限购
                                        //if (Constants.DrawCardShop.SceneObjectLimit.ContainsKey(sot.Quality))
                                        //{
                                        shopItem.LimitBuy = false;
                                        //    shopItem.LimitBuyTimes = Constants.DrawCardShop.SceneObjectLimit[sot.Quality];
                                        //}
                                        break;
                                    }
                            }
                            shopItem.Index = 10 * (int)sot.Quality;
                        }
                        else
                        {
                            var et = GameApplication.DataManager.FindValue<EntityType, EquipType>(id);
                            int price = 0;

                            switch (roamActivityType)
                            {
                                case RoamActivityType.Sliver:
                                case RoamActivityType.Diamond:
                                case RoamActivityType.RedDiamond:
                                    {
                                        switch (et.Quality)
                                        {
                                            case Quality.五星:
                                                if (Constants.DrawCardShop.star5EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.star5EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.star5EquipPrice.Last().Value;
                                                }
                                                break;
                                            case Quality.四星:
                                                if (Constants.DrawCardShop.star4EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.star4EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.star4EquipPrice.Last().Value;
                                                }
                                                break;
                                            case Quality.三星:
                                                if (Constants.DrawCardShop.star3EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.star3EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.star3EquipPrice.Last().Value;
                                                }
                                                break;
                                        }
                                        break;
                                    }
                                case RoamActivityType.Element:
                                    {
                                        switch (et.Quality)
                                        {
                                            case Quality.五星:
                                                if (Constants.DrawCardShop.ElementStar5EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.ElementStar5EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.ElementStar5EquipPrice.Last().Value;
                                                }
                                                break;
                                            case Quality.四星:
                                                if (Constants.DrawCardShop.ElementStar4EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.ElementStar4EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.ElementStar4EquipPrice.Last().Value;
                                                }
                                                break;
                                            case Quality.三星:
                                                if (Constants.DrawCardShop.ElementStar3EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                                {
                                                    price = Constants.DrawCardShop.ElementStar3EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                                }
                                                else
                                                {
                                                    price = Constants.DrawCardShop.ElementStar3EquipPrice.Last().Value;
                                                }
                                                break;
                                        }
                                        break;
                                    }
                            }
                            shopItem.Costs.Add(new ObjectAward(ItemNeedRecord, price));
                            shopItem.LimitBuy = true;
                            shopItem.LimitBuyTimes = 1;
                            shopItem.Index = 10 * (int)et.Quality;
                        }
                    }
                }
            }
        }


        void InitializeElementGacha()
        {
            var gachaItems = GameApplication.DataManager.FindValue<OrdinaryGachaType>(og => og.TicketType == TicketType && og.Available);
            foreach (var gachaItem in gachaItems)
            {
                var award = gachaItem.Awards.FirstOrDefault() as ObjectAward;
                var quality = award.TypeData.Quality;
                if (gachaItem.InOrdinaryGacha)
                {                    
                    var pool = Pools.Find(p => p.Id == (long)quality);
                    if(pool == null)
                    {
                        pool = new DrewCardType()
                        {
                            Id = (long)quality,
                            Threshold = OrdinaryGachaType.ElementGachaThresholdDic[quality],
                            Weight = OrdinaryGachaType.ElementGachaWeightDic[quality],
                            Type = DrewCardPoolType.家具,
                        };
                        Pools.Add(pool);
                    }
                    pool.Awards.AddRange(gachaItem.Awards.Select(a => a as ObjectAward));
                }
                else
                {
                    //不加入卡池的直接进商店
                    var shopItem = new ShopItemType();
                    ShopItems.Add(shopItem);
                    shopItem.ShopItemID = gachaItem.ID;
                    shopItem.Awards.AddRange(gachaItem.Awards.Select(a => a as ObjectAward));
                    int price = 0;
                    if (award.TypeData is EquipType et)
                    {
                        switch (quality)
                        {
                            case Quality.五星:
                                if (Constants.DrawCardShop.ElementStar5EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                {
                                    price = Constants.DrawCardShop.ElementStar5EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                }
                                else
                                {
                                    price = Constants.DrawCardShop.ElementStar5EquipPrice.Last().Value;
                                }
                                break;
                            case Quality.四星:
                                if (Constants.DrawCardShop.ElementStar4EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                {
                                    price = Constants.DrawCardShop.ElementStar4EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                }
                                else
                                {
                                    price = Constants.DrawCardShop.ElementStar4EquipPrice.Last().Value;
                                }
                                break;
                            case Quality.三星:
                                if (Constants.DrawCardShop.ElementStar3EquipPrice.Any(e => e.Key.Contains(et.Part)))
                                {
                                    price = Constants.DrawCardShop.ElementStar3EquipPrice.FirstOrDefault(e => e.Key.Contains(et.Part)).Value;
                                }
                                else
                                {
                                    price = Constants.DrawCardShop.ElementStar3EquipPrice.Last().Value;
                                }
                                break;
                        }
                    }
                    else if(award.TypeData is ItemType)
                    {
                        price = Constants.DrawCardShop.ElementItemPrice[quality];
                    }
                    else
                    {
                        price = Constants.DrawCardShop.ElementSceneObjectPrice[quality];
                    }
                    shopItem.Costs.Add(new ObjectAward(ItemNeedRecord, price));
                    shopItem.LimitBuy = gachaItem.LimitBuy;
                    shopItem.LimitBuyTimes = gachaItem.MaxCanBuy;
                    shopItem.Index = gachaItem.Index;
                    shopItem.NeedPreCost = gachaItem.TotalConsume;
                }
            }
        }

        private RoamActivityType _roamActivityType;
        public RoamActivityType roamActivityType
        {
            get
            {
                if (_roamActivityType == 0)
                {
                    switch (VirtualCurrencyType)
                    {
                        case 0://元素池不能消耗货币购买票
                            _roamActivityType = RoamActivityType.Element;
                            break;
                        case 1://银币池
                            _roamActivityType = RoamActivityType.Sliver;
                            break;
                        case 2://金币池
                            _roamActivityType = RoamActivityType.Diamond;
                            break;
                        case 100://金钻池
                            _roamActivityType = RoamActivityType.RedDiamond;
                            break;
                        case 5://友情卡池
                            _roamActivityType = RoamActivityType.Friend;
                            break;
                        default:
                            Logger.Error.Write("消耗这种货币的奖池没有设计  VirtualCurrencyType:" + VirtualCurrencyType.ToString());
                            break;
                    }
                }
                return _roamActivityType;
            }
        }

        protected override void OnActivityStart()
        {
            base.OnActivityStart();
            //if(ShopItems.Count == 0) 
            //AddExtraShopItems();
        }

        public class DrawCardTalogRecord
        {
            //抽卡次数
            public int Num;
            //是否保底
            public bool DrawIsGuaranTee = false;
            //是否保底道具
            public bool ItemIsGuaranTee = false;
            //抽卡奖励
            public string ItemId;
            //抽卡奖励名字
            public string ItemName;
            //累积次数记录
            public int DrawCumulativePoint;
            //抽卡类型
            public string DrawType;
            //卡池id
            public string DrawNo;
            //消耗道具类型
            public string DrawCostType;
            //消耗道具个数
            public int DrawCostNum;
            //活动id
            public string ActivityID;
            //活动名称
            public string ActivityName;

            //public void WriteLog(Character character)
            //{
            //    if (Constants.AllowTaLog)
            //    {
            //        character.GetLeiTingTrackWriter("draw_card")
            //        .WriteEntry("draw_cost_type", DrawCostType)
            //        .WriteEntry("draw_cost_num", DrawCostNum)
            //        .WriteEntry("num", Num)
            //        .WriteEntry("draw_is_guarantee", DrawIsGuaranTee)
            //        .WriteEntry("item_is_guarantee", ItemIsGuaranTee)
            //        .WriteEntry("draw_cumulative_point", DrawCumulativePoint)
            //        .WriteEntry("draw_type", DrawType)
            //        .WriteEntry("activity_id", ActivityID)
            //        .WriteEntry("activity_name", ActivityName)
            //        .WriteEntry("draw_no", DrawNo)
            //        .WriteEntry("item_id", ItemId)
            //        .WriteEntry("item_name", ItemName).WriteToTaLogger();
            //    }
            //}

            public JsonObject GetJsonValue()
            {
                var res = new JsonObject();
                res[nameof(ItemId)] = ItemId;
                res[nameof(DrawIsGuaranTee)] = DrawIsGuaranTee;
                res[nameof(DrawCumulativePoint)] = DrawCumulativePoint;
                res[nameof(DrawNo)] = DrawNo;
                return res;
            }
        }
    }

    [WebClass("抽卡配置项")]
    public class DrewCardType : JsonFormatObject, IProbabilityItem
    {
        [JsonMember]
        [WebMember("标识")]
        public long Id { get; set; }

        [JsonMember]
        [WebMember("阈值")]
        public int Threshold { get; set; }

        [JsonMember]
        [WebMember("保New次数")]
        public int NextNewTime { get; set; }

        [JsonMember]
        [WebMember("权重")]
        public int Weight { get; set; }

        [JsonMember]
        [WebMember("是否概率自增")]
        public bool SelfIncrease { get; set; }

        //[JsonMember(FormatType = typeof(JsonListFormat<long>))]
        //[WebMember("物品列表")]
        //public List<long> Items { get; set; }
        /// <summary>
        /// 奖励集
        /// </summary>
        [WebMember("奖励集")]
        [JsonMember(FormatType = typeof(JsonListFormat<ObjectAward>))]
        public List<ObjectAward> Awards { get; set; }

        [JsonMember]
        [WebMember("奖池类型")]
        public DrewCardPoolType Type { get; set; }

        [JsonMember]
        [WebMember("奖励id(仅在道具池启用)")]
        public long ItemAwardId { get; set; }

        public bool HasABottom => Threshold > 0;

        public int Probability
        {
            get
            {
                return Weight;
            }
        }

        public DrewCardType()
        {
            Awards = new List<ObjectAward>();
        }

        public void CopyButItems(DrewCardType another)
        {
            Id = another.Id;
            Threshold = another.Threshold;
            NextNewTime = another.NextNewTime;
            Weight = another.Weight;
            SelfIncrease = another.SelfIncrease;
            Type = another.Type;
            ItemAwardId = another.ItemAwardId;
        }

        private JsonValue _toClientJson = null;

        public JsonValue ToClientJson(bool showItemPool)
        {
            if (_toClientJson != null)
            {
                return _toClientJson;
            }
            var resJson = new JsonObject();
            var awards = new JsonArray();
            if (Type != DrewCardPoolType.道具)
            {
                foreach (var item in Awards)
                {
                    awards.Add(item.Spec.ProtoData.ToJson());
                }
            }
            else if (showItemPool)
            {
                foreach (var award_group in GameApplication.DataManager.FindValue<SystemAwardType>(ItemAwardId).Awards.OfType<AwardGroup>())
                {
                    foreach (var award in award_group.Awards)
                    {
                        if (award is FilterAward fa)
                        {
                            fa.InitAwardViews(ItemAwardId);
                            if (DataCache.Instance.FilterAwardViews.TryGetValue(ItemAwardId, out var checked_list))
                            {
                                foreach (var et in checked_list)
                                {
                                    var spec = new AwardSpecData() { Type = et.ID, Value = 1, Category = ObjectSpecCategory.Object };
                                    awards.Add(spec.ToJson());
                                }
                            }
                        }
                        else if(award is ObjectAward oa)
                        {
                            awards.Add(oa.Spec.ProtoData.ToJson());
                        }
                        else if(award is RandomGroupAward rga)
                        {
                            awards.AddRange(rga.AwardsContent().Select(ac => ac.ProtoData.ToJson()));
                        }
                    }
                }
            }
            resJson["Weight"] = Weight;
            resJson["awards"] = awards;
            _toClientJson = resJson;
            return resJson;
        }

        public virtual void GetAwardSpecFromId(long awardId, out JsonArray ja)
        {
            ja = new JsonArray();
            foreach (var award_group in GameApplication.DataManager.FindValue<SystemAwardType>(ItemAwardId).Awards.OfType<AwardGroup>())
            {
                foreach (var award in award_group.Awards)
                {
                    if (award is FilterAward fa)
                    {
                        fa.InitAwardViews(ItemAwardId);
                        if (DataCache.Instance.FilterAwardViews.TryGetValue(ItemAwardId, out var checked_list))
                        {
                            foreach (var et in checked_list)
                            {
                                var spec = new AwardSpecData() { Type = et.ID, Value = 1, Category = ObjectSpecCategory.Object };
                                ja.Add(spec.ToJson());
                            }
                        }
                    }
                }
            }
        }
    }

    [WebClass("稻草人配置项")]
    public class KakashiType : JsonFormatObject
    {
        [WebMember("服装集")]
        [JsonMember(FormatType = typeof(JsonListFormat<ObjectAward>))]
        public List<ObjectAward> Equips { get; set; } = new List<ObjectAward>();

        [WebMember("性别")]
        [JsonMember]
        public Gender Gender { get; set; }

        [WebMember("名字")]
        [JsonMember]
        public string Name { get; set; }
        [WebMember("组别")]
        [JsonMember]
        public int Group { get; set; }

        [WebMember("商店页签")]
        [JsonMember]
        public int ShopIndex { get; set; }

        [WebMember("稀有度")]
        [JsonMember]
        public Quality Quality { get; set; }

        [WebMember("是否往期")]
        [JsonMember]
        public bool IsOldSuit { set; get; }

        [WebMember("欧欧盒页签")]
        [JsonMember]
        public int LuckyBoxTag { set; get; }

        [JsonMember]
        public bool ForceName { set; get; }

        public string LuckyBoxSuit = "";
        public string LuckyBoxSuffix = "";
        public long LuckyBoxID;

        public JsonValue ToClientJson()
        {
            var resJson = new JsonObject();
            var equipIds = new JsonArray();

            foreach (var item in Equips)
            {
                equipIds.Add(item.Type);
            }
            resJson["ID"] = ID;
            resJson["EquipIds"] = equipIds;
            resJson["Gender"] = (int)Gender;
            resJson["Name"] = Name;
            resJson["Group"] = Group;
            resJson["ShopIndex"] = ShopIndex;
            resJson["Quality"] = (int)Quality;
            resJson["IsOldSuit"] = IsOldSuit;
            resJson["LuckyBoxTag"] = LuckyBoxTag;
            resJson["ForceName"] = ForceName;
            return resJson;
        }
    }

    [EntityTable(DbGroup.Entity)]
    public class DrewCardRecord : SpecialShopRecord
    {

        private DrewCardModel _record = new DrewCardModel();
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDataFormat<DrewCardModel>))]
        public DrewCardModel Record
        {
            get
            {
                return _record;
            }
            set
            {
                _record = value;
            }
        }

        private int _todayDrawCardTime;
        [TableColumn]
        public int TodayDrawCardTime
        {
            set
            {
                _todayDrawCardTime = value;
            }
            get
            {
                if (DateTimeExtension.PassedDays(DateTimeExtension.Now, LastRefreshTime) > 0)
                {
                    LastRefreshTime = DateTimeExtension.Now;
                    _todayDrawCardTime = 0;
                }
                return _todayDrawCardTime;
            }
        }

        [TableColumn]
        public DateTime LastRefreshTime { set; get; } = DateTimeExtension.Now;
    }

    public class DrewCardModel : JsonFormatObject
    {
        private Dictionary<long, int> _noNewTimes = new Dictionary<long, int>();
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDictionaryFormat<long, int>))]
        public Dictionary<long, int> NoNewTimes
        {
            get
            {
                return _noNewTimes;
            }
            set
            {
                _noNewTimes = value;
            }
        }


        private Dictionary<long, int> _formatType = new Dictionary<long, int>();
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDictionaryFormat<long, int>))]
        public Dictionary<long, int> NextNewTimes
        {
            get
            {
                return _formatType;
            }
            set
            {
                _formatType = value;
            }
        }
    }

    /// <summary>
    /// 把奖池根据消耗的货币和票分为三种类型
    /// </summary>
    public enum RoamActivityType
    {
        /// <summary>
        /// 默认值，奖池类型初始化后为None的话就bug了
        /// </summary>
        None = 0,
        /// <summary>
        /// 银币池 消耗票typeid:200105
        /// </summary>
        Sliver = 1,
        /// <summary>
        ///  金币池
        /// </summary>
        Diamond = 2,
        /// <summary>
        ///  金钻池
        /// </summary>
        RedDiamond = 3,
        /// <summary>
        /// 元素池，只能消耗道具抽卡
        /// </summary>
        Element = 4,
        /// <summary>
        /// 友情卡池
        /// </summary>
        Friend = 5
    }
}
