using HelpBase;
using HelpBase.Json;
using HelpBase.Linq;
using HelpBase.Web;
using System.Json;

namespace GameModel.Activities
{
    [WebClass("春节全屏倒计时活动")]
    public class SpringFestivalCountdownActivity : ActivityWithRecord<SpringFestivalCountdownRecord>
    {
        [WebMember("新春时间")]
        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime SpringFestivalTime { get; set; }

        TimeAction SecondTimeAction;

        public override JsonObject Protocol()
        {
            var json = base.Protocol();
            json[nameof(SpringFestivalTime)] = SpringFestivalTime.ToTimestamp();
            return json;
        }

        protected override void OnTimeChecked()
        {
            base.OnTimeChecked();
            if (DateTimeExtension.Now > SpringFestivalTime) return;
            TimerManager.Release(ref SecondTimeAction);
            if (SpringFestivalTime - DateTimeExtension.Now <= TimeSpan.FromSeconds(30))
            {
                SecondTimeAction = new TimeAction(1000, OnSecondTimeCheck);
                TimerManager.AddToTimer(SecondTimeAction);
            }
        }

        private void OnSecondTimeCheck()
        {
            if (SpringFestivalTime - DateTimeExtension.Now < TimeSpan.FromSeconds(10))
            {
                TimerManager.Release(ref SecondTimeAction);

                GameApplication.UserManager.Onlines.ForEach(p =>
                {
                    try
                    {
                        SendInteraction(p, 0, null);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error.Write(ex.Message + ex.StackTrace);
                        throw;
                    }
                });
            }
        }
    }

    public class SpringFestivalCountdownRecord : ActivityFunctionRecord
    {
        [JsonMember]
        public bool IsOver { set; get; }
    }
}
