using DataBase;
using GameModel.Rankings;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Activities
{
    [WebClass("演奏游戏活动")]
    public class PicaMusicGameActivity : MiniGameWithRankingActivity<PicaMusicGameActivityCharacterRecord, MusicGameLevel>
    {
        [WebMember("关卡配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<MusicGameLevel>))]
        public override List<MusicGameLevel> GameLevels { get; set; }

        [WebMember("乐器")]
        [JsonMember]
        public string RequiredEquipTag { get; set; }

        public override MiniGameCodeType MiniGameCode => MiniGameCodeType.MusicGame;

        public override GameRankingRule RankingRule => GameRankingRule.升序;


        public override JsonObject Protocol()
        {
            var res = base.Protocol();
            res[nameof(RequiredEquipTag)] = RequiredEquipTag;
            return res;
        }

        public override bool Validate(out string message)
        {
            StringBuilder sb = new StringBuilder();
            foreach (var gamelevel in GameLevels)
            {
                if (gamelevel.MusicScore == null) continue;
                foreach(var score in gamelevel.MusicScore)
                {
                    var notes = score.Split(' ');
                    if (notes.Any(n => !int.TryParse(n, out var i)))
                    {
                        sb.AppendLine($"关卡{gamelevel.GameId}, 谱子{gamelevel.MusicScore.IndexOf(score)}配置错误");
                    }
                }
            }
            if(sb.Length > 1)
            {
                message = sb.ToString();
                return false;
            }
            return base.Validate(out message);
        }
    }

    [EntityTable(DbGroup.Entity)]
    public class PicaMusicGameActivityCharacterRecord : RankingMiniGameActivityPlayerRecord
    {

    }

    public class MusicGameLevel : GameLevelBase
    {
        [WebMember("谱子合集")]
        [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public List<string> MusicScore { get; set; }

        [WebMember("是否乱序")]
        [JsonMember]
        public bool DoShuffle { get; set; }

        [WebMember("目标谱子数")]
        [JsonMember]
        public int TargetScoreNum { get; set; }

        [WebMember("是否有排行榜")]
        [JsonMember]
        public bool NeedRanking { get; set; }

        [WebMember("背景音乐bundle")]
        [JsonMember]
        public string BgmBundle { get; set; }

        [WebMember("背景音乐")]
        [JsonMember]
        public string Bgm { get; set; }

        public override bool IsUnlimited => NeedRanking;

        public override JsonObject ToClient()
        {
            var res = base.ToClient();
            res[nameof(DoShuffle)] = DoShuffle;
            res[nameof(MusicScore)] = new JsonArray(MusicScore.Select(fod => (JsonValue)fod));
            res[nameof(TargetScoreNum)] = TargetScoreNum;
            res[nameof(NeedRanking)] = NeedRanking;
            res[nameof(Bgm)] = Bgm;
            res[nameof(BgmBundle)] = BgmBundle;
            return res;
        }
    }
}
