using System.Collections.Generic;
using System.Json;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;

namespace GameModel.Activities
{
    [WebClass("奖励N倍活动")]
    public class AwardTimesMoreActivity : EmptyActivity
    {
        [WebMember("倍数")]
        [JsonMember]
        public int AwardMoreTimes { set; get; }

        [WebMember("活动时间段")]
        [JsonMember(FormatType = typeof(JsonListFormat<TimeRegion>))]
        public override List<TimeRegion> TimeRegions { get; set; }

        [WebMember("活动地点")]
        [JsonMember(FormatType = typeof(JsonListFormat<MoreTimesPlace>))]
        public List<MoreTimesPlace> Places { get; set; }

        [WebMember("活动翻倍道具")]
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> TimesIds { get; set; }


        public override JsonObject Protocol()
        {
            var res = base.Protocol(); ;

            res[nameof(AwardMoreTimes)] = AwardMoreTimes;

            if (Places != null)
            {
                var place = new JsonArray();
                Places.ForEach(p => place.Add((long)p));
                res[nameof(Places)] = place;
            }
            //if (TimesIds != null)
            //{
            //    var ids = new JsonArray();
            //    TimesIds.ForEach(p => ids.Add(p));
            //    res[nameof(TimesIds)] = TimesIds;
            //}


            return res;
        }

    }
    public enum MoreTimesPlace
    {
        农场 = 1002,
        矿洞 = 2010,
        渔场 = 2015,
        牧场 = 2018,
        森林 = 2032,
    }
}
