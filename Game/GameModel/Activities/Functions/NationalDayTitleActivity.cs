using HelpBase.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataBase;
using GameModel.Award;
using HelpBase.Json;
using System.Json;
using GameModel.Activities.Components;

namespace GameModel.Activities
{
    [WebClass("国庆节标题活动")]
    public class NationalDayTitleActivity : EmptyActivity
    {
        //[TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
        //public List<AwardBase> FirstDaySigninAward { set; get; } = new List<AwardBase>();

        //[TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
        //public List<AwardBase> LastDaySigninAward { set; get; } = new List<AwardBase>();

        [TableColumn]
        [WebMember("篝火活动url")]
        public string BonefireActUrl { set; get; } = "";

        [TableColumn]
        [WebMember("双倍碎片活动url")]
        public string DoublePicesActUrl { set; get; } = "";

        public override JsonObject Protocol()
        {
            var res = base.Protocol();

            var component = GetComponent<ActivityMailComponent>();
            if (component != null)
            {
                var first_day_awards = component.MailSettings.FirstOrDefault().Awards;
                var last_day_awards = component.MailSettings.LastOrDefault().Awards;

                if (first_day_awards != null)
                {
                    res["FirstDaySigninAward"] = new JsonArray(first_day_awards.Select(a => a.Spec.ProtoData.ToJson()));
                }

                if (last_day_awards != null)
                {
                    res["LastDaySigninAward"] = new JsonArray(last_day_awards.Select(a => a.Spec.ProtoData.ToJson()));
                }
            }

            res[nameof(BonefireActUrl)] = BonefireActUrl;
            res[nameof(DoublePicesActUrl)] = DoublePicesActUrl;

            return res;
        }
    }
}
