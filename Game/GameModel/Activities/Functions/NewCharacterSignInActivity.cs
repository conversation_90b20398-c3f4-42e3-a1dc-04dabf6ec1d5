using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using DataBase;
using GameModel.Award;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel.Activities
{
    [WebClass("新建角色七日签到活动")]
    public class NewCharacterSignInActivity : ActivityFunction<NewCharacterSignInActivityRecord>, IInteractionActivity
    {
        private int? maxDays;

        public int MaxDays => maxDays ??= DayOptions.Max(p => p.Day);

        [WebMember("日配置集合")]
        [JsonMember(FormatType = typeof(JsonListFormat<DayOption>))]
        public List<DayOption> DayOptions { get; set; }

        public override JsonObject Personal(Character character)
        {
            var json = base.Personal(character);
            var record = GetCharacterRecord(character, true);
            if (record != null)
            {
                json[nameof(record.Days)] = record.Days;
                json[nameof(record.LastSignTime)] = record.LastSignTime.ToTimestamp();
            }
            return json;
        }

        public override int GetAttention(Character character, CharacterActivityRecord record)
        {
            var value = base.GetAttention(character, record);
            var rr = GetCharacterRecord(character, true);
            if (rr == null || rr.LastSignTime.Date != DateTimeExtension.Now.Date)
            {
                value++;
            }
            return value;
        }

        public override void FromJson(JsonObject jsonObj)
        {
            base.FromJson(jsonObj);
            maxDays = null;
        }

        public override bool HasShow(Character character)
        {
            var record = GetCharacterRecord(character, true);
            return base.HasShow(character) && (record == null || record.Days < MaxDays);
        }

        public override JsonObject Protocol()
        {
            var json = base.Protocol();
            if (DayOptions != null)
            {
                json[nameof(DayOptions)] = new JsonArray(DayOptions.Select(p => p.ProtoData));
            }

            return json;
        }

        public void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID, byte[] bytes)
        {
            if (Owner.Phase != ActivityPhase.开启)
            {
                character.ShowError(LocalityStrings.activityNotOpen);
                return;
            }
            var now = DateTimeExtension.Now;
            var record = GetCharacterRecord(character, true, true);
            if (record.LastSignTime.Date == now.Date)
            {
                character.ShowError(LocalityStrings.repetitiveOperation);
                return;
            }
            var next = record.Days + 1;
            var option = DayOptions.Find(p => p.Day == next);
            if (option == null)
            {
                character.ShowError(LocalityStrings.unknownError);
                return;
            }

            record.Days = next;
            record.LastSignTime = now;
            record.Save();

            var result = character.GetAwards(option.Awards, "新用户登录活动", null, option.Day);
            character.User.ShowAwardResult(result);
            ShowUpdateRecord(character, record);
            character.CheckUpdateActivity(Owner);
        }
        public string GetActionNameByAction(byte action)
        {
            return "领取奖励";
        }

        [WebClass("日配置")]
        public sealed class DayOption : JsonFormatObject
        {
            [JsonMember]
            [WebMember("天数")]
            public int Day { get; set; }

            [JsonMember]
            [WebMember("名称")]
            public string Name { get; set; }

            [JsonMember]
            [WebMember("数量")]
            public int Count { get; set; }

            [JsonMember]
            [WebMember("图片资源包")]
            public string ImageBundle { get; set; }

            [JsonMember]
            [WebMember("图片资源名")]
            public string Image { get; set; }

            [WebMember("奖励集")]
            [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
            public JsonDynamicList<AwardBase> Awards { get; set; }

            public JsonObject ProtoData
            {
                get 
                {
                    var json = new JsonObject();
                    json[nameof(Day)] = Day;
                    json[nameof(Name)] = Name;
                    json[nameof(Count)] = Count;
                    json[nameof(ImageBundle)] = ImageBundle;
                    json[nameof(Image)] = Image;
                    json[nameof(Awards)] = new JsonArray(Awards.Select(p => p.Spec.ProtoData.ToJson()));
                    return json;
                }
            }
        }
    }

    [EntityTable(DbGroup.Entity)]
    public class NewCharacterSignInActivityRecord : ActivityCharacterRecord
    {
        [TableColumn]
        public int Days { get; set; }

        [TableColumn]
        public DateTime LastSignTime { get; set; }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            LastSignTime = DateTimeExtension.MinTime;
        }
    }
}
