using GameFramework.Linq;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Activities
{
    [WebClass("寒假矿洞活动")]
    public class NewMineTitleActivity : ActivityFunction, IHaveMissionActivity
    {
        [WebMember("活动触发值")]
        [JsonMember]
        public long MissionGroupType { set; get; }

        private List<int> _missionIndexes;
        public List<int> MissionIndexes
        {
            get
            {
                return _missionIndexes;
            }
        }
        public override JsonObject Personal(Character character)
        {
            var res = base.Personal(character);
            var missions = GetMissions(character).Items;

            res["HasMissionReddot"] = missions.Any(i => i.Status == MissionStatus.可提交);
            res["CompMissionCount"] = missions.Where(m => m.Status == MissionStatus.已完成 && GameApplication.DataManager.FindValue<MissionType>(m.Type).Index != 1101).Count();

            //var exps = GameApplication.ActivityManager.FirstActivity<ExploreLevelsActivity>().ExploreLevels;
            //var last_comp_chapter = exps.LastOrDefault(level => character.EpisodeDatas.Any(p => p.Type == level.ExploreLevel && p.PassedTime > 0));
            //res["NowAtChapter"] = last_comp_chapter == null ? 1 : exps.IndexOf(last_comp_chapter) + 1;

            return res;
        }

        public override void Initialize(Activity activity)
        {
            base.Initialize(activity);

            if (_missionIndexes == null)
            {
                _missionIndexes = new List<int>();
                GameApplication.DataManager.FindValue<MissionGroupType>(mgt => mgt.Trigger.Action == UserAction.活动任务触发器 && mgt.Trigger.Type == MissionGroupType).ForEach(mgt => _missionIndexes.Add(mgt.MissionIndex));
            }
        }
        public UserAction2 Action => UserAction2.跨日 | UserAction2.初始化活动任务 | UserAction2.登录 | UserAction2.完成活动任务 | UserAction2.可提交活动任务;

        public MissionsResponse GetMissions(Character user)
        {
            if(!user.MissionInitable || !user.IsOnline)
            {
                return new MissionsResponse();
            }
            var res = user.PicaActivityMissionGroup.GetMissionResponse();

            user.PicaActivityMissionGroup.Missions.Values.ForEach(mission =>
            {
                if (MissionIndexes.Contains(mission.Template.Index))
                {
                    res.Items.Add(mission.ProtoData);
                }
            });

            return res;
        }

        public void Trigger(Character source, UserAction2 action, object param)
        {
            if (action == UserAction2.完成活动任务 || action == UserAction2.可提交活动任务)
            {
                UpdateToClient(source);
            }
            else
            {
                source.CheckUpdateMissionGroup(source, 0, UserAction.活动任务触发器, MissionGroupType, DateTimeExtension.Now.PassedDays(StartDate) + 1, true);
            }
        }
    }
}
