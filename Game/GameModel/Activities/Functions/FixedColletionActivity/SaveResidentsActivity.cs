using GameModel.Award;
using GameModel.Utility;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;
using System.Json;

namespace GameModel.Activities
{
    [WebClass("拯救疯狂居民活动")]
    public class SaveResidentsActivity : PicaFixedColletionActivityBase<SaveResidentsActivityRecord, SaveResidentsCharacterRecord>
    {
        [WebMember("常驻开启")]
        [JsonMember]
        public bool AllwaysOpenAct { get; set; }

        [WebMember("场景配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<IDProbability>))]
        public List<IDProbability> Scenes { get; set; }

        [WebMember("珍德药剂攻击力")]
        [JsonMember]
        public int Item1Attack { get; set; }

        [WebMember("珍德药剂冷却")]
        [JsonMember]
        public float Item1CD { get; set; }

        [WebMember("珍德药丸攻击力")]
        [JsonMember]
        public int Item2Attack { get; set; }

        [WebMember("珍德药丸冷却")]
        [JsonMember]
        public float Item2CD { get; set; }

        [WebMember("奖励配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<SaveResidentsAward>))]
        public List<SaveResidentsAward> Awards { set; get; }

        [WebMember("每轮可获得奖励次数")]
        [JsonMember]
        public int CanGetAwardTimes { get; set; }


        public override int RebirthTime { get; set; } = 200;

        public override int MaxNumber { get; set; } = 1;

        public override int MaxCharacterLifecycleCount { get; set; }

        public override string MaxCountMessage { get; set; }



        public override bool AllwaysOpen => true;

        protected override void OnActivityStart()
        {
            var actRc = GetActivityRecord(true);
            actRc.CurrentSceneId = Scenes.GetRandomItem().ID;
            base.OnActivityStart();
        }

        protected override void OnActivityOver(bool forcible)
        {
            base.OnActivityOver(forcible);
        }

        public override JsonObject Protocol()
        {
            var json = base.Protocol();
            json[nameof(Item1Attack)] = Item1Attack;
            json[nameof(Item2Attack)] = Item2Attack;
            json[nameof(Item1CD)] = Item1CD;
            json[nameof(Item2CD)] = Item2CD;
            return json;
        }

        public override int GetTotalCount(Scene scene, long id)
        {
            var actRc = GetActivityRecord(true);
            if (actRc.CurrentSceneId == scene.Type)
            {
                return Total;
            }
            return 0;
        }


        public void Settlement(Scene scene, long sceneObjectId, int maxHp)
        {
            List<Character> characters = new List<Character>();
            foreach (var sceneCharacter in scene.FindArea(0).Players.Values)
            {
                var rc = GetCharacterRecord(sceneCharacter.Owner, false, false);
                if (rc == null || rc.SceneObjectId != sceneObjectId || rc.Damage == 0) continue;
                if (CanGetAwardTimes > 0 && rc.GetAwardTimes >= CanGetAwardTimes) continue;

                characters.Add(sceneCharacter.Owner);
                
                var ratio = rc.Damage / (float)maxHp * 100;
                var awardSetting = Awards.FirstOrDefault(p => p.StartRange <= ratio && ratio < p.EndRange);
                if (awardSetting == null) continue;

                rc.GetAwardTimes++;
                var content = string.Format(awardSetting.MailContent, Math.Round(ratio, 2).ToString("F2") + "%");
                sceneCharacter.Owner.AddMail(awardSetting.MailTitle, content, awardSetting.Awards, true, DateTimeExtension.Now.AddDays(3), MailType.Award, Name + "奖励");
            }
        }
    }

    public class SaveResidentsActivityRecord : PicaFixedColletionActivityRecordBase
    {
        public long CurrentSceneId { get; set; }
    }

    public sealed class SaveResidentsCharacterRecord : PicaFixedColletionCharacterRecordBase
    {
        public long SceneObjectId { get; set; }

        public int Damage { get; set; }

        public int GetAwardTimes { get; set; }

        public void AddDamage(long soId, int damage)
        {
            if (SceneObjectId != soId)
            {
                SceneObjectId = soId;
                Damage = 0;
            }
            Damage += damage;
        }
    }

    [WebClass("奖励配置")]
    public sealed class SaveResidentsAward : JsonFormatObject
    {
        [JsonMember]
        [WebMember("起始百分比范围")]
        public float StartRange { set; get; }

        [JsonMember]
        [WebMember("结束百分比范围")]
        public int EndRange { set; get; }

        [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
        [WebMember("奖励")]
        public JsonDynamicList<AwardBase> Awards { set; get; }

        [JsonMember]
        [WebMember("邮件标题")]
        public string MailTitle { set; get; }

        [JsonMember]
        [WebMember("邮件内容{0}为伤害百分比")]
        public string MailContent { set; get; }
    }
}
