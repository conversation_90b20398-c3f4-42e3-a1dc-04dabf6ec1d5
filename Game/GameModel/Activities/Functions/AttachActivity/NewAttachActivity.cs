using DataBase;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel.Activities
{
    [WebClass("新附身活动")]
    public class NewAttachActivity : ActivityWithRecordAndSpecialShop<NewAttachActivityRecord, NewAttachActivityCharacterRecord>, IInteractionActivity, IPersonalityTimeRegionActivity, INeedEarlyEndFunction
    {
        [WebMember("活动时间段")]
        [JsonMember(FormatType = typeof(JsonListFormat<TimeRegion>))]
        public List<TimeRegion> PersnalityTimeRegions { get; set; }

        [WebMember("附身设置")]
        [JsonMember(FormatType = typeof(JsonListFormat<NewAttachSetting>))]
        public List<NewAttachSetting> AttachSettings { get; set; }

        [JsonMember]
        [WebMember("附身系统提示", Default = "[{0}] 被{1}附身了！")]
        public string AttachSystemMessage { get; set; }

        [WebMember("功能结束时间")]
        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime FunctionEndDate { get; set; } = DateTime.MaxValue;
        /// <summary>
        /// 找到所有正在小屋中的玩家
        /// </summary>
        /// <returns></returns>
        List<Character> FindCharactersOnRoom()
        {
            var conformOnlines = new List<Character>();
            foreach (var user in GameApplication.UserManager.Onlines)
            {
                try
                {
                    if (user.SceneCharacter.CurrentScene?.TypeData.DimensionType != DimensionType.PicaRoom) continue;
                    if (user.Lv < MinCharacterLv) continue;
                    if (user.IsAttach()) continue;
                    conformOnlines.Add(user);
                }
                catch (Exception e)
                {
                    Logger.Error.Write($"{nameof(PicaAttachActivity)} : {nameof(FindCharactersOnRoom)} : {e.Message + e.StackTrace}");
                }

            }
            return conformOnlines;
        }

        /// <summary>
        /// 开始附身
        /// </summary>
        void ChangeRole(Character character, long id)
        {
            //var RC = GetCharacterRecord(character, true, true);
            //RC.IsAttach = true;
            //RC.AttachId = id;
            //RC.Save();
            //UpdateToClient(character);
            int minutes = 0;
            foreach (TimeRegion tr in PersnalityTimeRegions)
            {
                if (tr.IsInRegion(DateTimeExtension.Now))
                {
                    minutes = Math.Clamp((tr.EndTime - DateTimeExtension.Now.TimeOfDay).Minutes + 2, 1, 60);
                    break;
                }
            }

            var attachType = GameApplication.DataManager.FindValue<AttachType>(id);
            character.OnAttach(id, minutes);
            // 倒计时
            //ShowAttachCountDown(character);
            GameApplication.ServiceManager.SystemMessage(string.Format(AttachSystemMessage, character.Name, attachType.Name), SpeakGroup.系统, SpeakSubGroup.聊天框);
        }

        public override List<TimeRegion> GetTimeRegionsForClient()
        {
            return null;
        }

        protected override void OnActivityStart()
        {
            //var onlines = GameApplication.UserManager.Onlines;
            //if (onlines.Count == 0)
            //{
            //    Logger.Error.Write("服务器没有在线人员，无法开启附身活动");
            //    return;
            //}

            //foreach (var setting in AttachSettings)
            //{
            //    var conformOnlines = FindCharactersOnRoom();
            //    Logger.ImportantRecord.Write($"{nameof(PicaAttachActivity)} Number of players matched : {conformOnlines.Count}");

            //    if (setting.AttachCount.Gender != Gender.无)
            //    {
            //        conformOnlines = conformOnlines.Where(p => p.Gender == setting.AttachCount.Gender).ToList();
            //    }
            //    if (conformOnlines.Count <= 0) continue;


            //    var attachCount = (int)Math.Floor(setting.AttachCount.Proportion * conformOnlines.Count);
            //    attachCount = Math.Max(Math.Min(attachCount, setting.AttachCount.MaxCount), setting.AttachCount.MixCount);
            //    Logger.ImportantRecord.Write($"{nameof(PicaAttachActivity)} AttachCount {attachCount}");

            //    var attachs = new List<Character>();
            //    if (conformOnlines.Count > 0)
            //    {
            //        attachs = conformOnlines.GetRandomList(attachCount);
            //    }
            //    foreach (var player in attachs)
            //    {
            //        ChangeRole(player, setting.AttachId);
            //    }

            //    var RC = GetActivityRecord(true);
            //    RC.AttachCharacterId.AddRange(attachs.Select(p => p.ID).ToList());
            //    RC.Save();
            //}

            // 更新活动交互技能
            //conformOnlines.ForEach(character =>
            //{
            //    character.UpdateToClient(p =>
            //    {
            //        p.SocialIcons = new OwnedSocialIconData();
            //        p.SocialIcons.Ids.AddRange(character.GetSocialIcons(character.SceneCharacter.Scene.Dimension.Type));
            //    });
            //});


        }

        protected override void OnActivityOver(bool forcible)
        {
            ////解除当前附身玩家状态
            //var RC = GetActivityRecord(false);
            //if (RC != null)
            //    RC.ResetAttach();

            // 更新活动交互技能
            //var conformOnlines = FindCharactersOnRoom();
            //conformOnlines.ForEach(character =>
            //{
            //    character.UpdateToClient(p =>
            //    {
            //        p.SocialIcons = new OwnedSocialIconData();
            //        p.SocialIcons.Ids.AddRange(character.GetSocialIcons(character.SceneCharacter.Scene.Dimension.Type));
            //    });
            //});
            //GameApplication.ServiceManager.SystemMessage(EndSystemMessage, SpeakGroup.系统, SpeakSubGroup.聊天框);
        }


        public void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID, byte[] bytes)
        {

        }

        public string GetActionNameByAction(byte action)
        {
            throw new NotImplementedException();
        }

        public List<long> GetAttachAllSkills()
        {
            List<long> skills = new List<long>();
            foreach (var setting in AttachSettings)
            {
                var config = GameApplication.DataManager.FindValue<AttachType>(setting.AttachId);
                skills.AddRange(config.SelfSkillTypes);
                skills.AddRange(config.BySkillTypes);
                skills.AddRange(config.ToSkillTypes);
            }
            return skills;
        }

        protected override void OnTimeChecked()
        {
            base.OnTimeChecked();
            OnPersonalityTimeCheck();
        }

        public void OnPersonalityTimeStart()
        {
            var onlines = GameApplication.UserManager.Onlines;
            if (onlines.Count == 0)
            {
                Logger.Error.Write("服务器没有在线人员，无法开启附身活动");
                return;
            }

            foreach (var setting in AttachSettings)
            {
                var conformOnlines = FindCharactersOnRoom();
                Logger.ImportantRecord.Write($"{nameof(PicaAttachActivity)} Number of players matched : {conformOnlines.Count}");

                if (setting.AttachCount.Gender != Gender.无)
                {
                    conformOnlines = conformOnlines.Where(p => p.Gender == setting.AttachCount.Gender).ToList();
                }
                if (conformOnlines.Count <= 0) continue;


                var attachCount = (int)Math.Floor(setting.AttachCount.Proportion * conformOnlines.Count);
                attachCount = Math.Max(Math.Min(attachCount, setting.AttachCount.MaxCount), setting.AttachCount.MixCount);
                Logger.ImportantRecord.Write($"{nameof(PicaAttachActivity)} AttachCount {attachCount}");

                var attachs = new List<Character>();
                if (conformOnlines.Count > 0)
                {
                    attachs = conformOnlines.GetRandomList(attachCount);
                }
                foreach (var player in attachs)
                {
                    ChangeRole(player, setting.AttachId);
                }

                var RC = GetActivityRecord(true);
                RC.AttachCharacterId.AddRange(attachs.Select(p => p.ID).ToList());
                RC.Save();
            }
        }

        public void OnPersonalityTimeEnd()
        {
            var RC = GetActivityRecord(false);
            if (RC != null)
                RC.ResetAttach();
        }

        public void OnPersonalityTimeCheck()
        {
            if (IsInPersonalActivity())
            {
                if (attachActivityPhase == AttachActivityPhase.已关闭)
                {
                    attachActivityPhase = AttachActivityPhase.开启中;
                    OnPersonalityTimeStart();
                }
            }
            else
            {
                if (attachActivityPhase == AttachActivityPhase.开启中)
                {
                    attachActivityPhase = AttachActivityPhase.已关闭;
                    OnPersonalityTimeEnd();
                }
            }
        }

        private AttachActivityPhase attachActivityPhase = AttachActivityPhase.已关闭;

        private bool IsInPersonalActivity()
        {
            return PersnalityTimeRegions.Any(p => p.IsInRegion(DateTimeExtension.Now));
        }
    }

    public enum AttachActivityPhase
    {
        开启中,
        已关闭
    }

    public class NewAttachSetting : JsonFormatObject
    {
        [JsonMember]
        [WebMember("附身样式", UIType = UIType.AttachSearch)]
        public long AttachId { get; set; }

        /// <summary>
        /// 附身规则
        /// </summary>
        [WebMember("附身规则")]
        [JsonMember(FormatType = typeof(JsonDataFormat<PicaAttachCountSetting>))]
        public PicaAttachCountSetting AttachCount { get; set; }

    }


    public sealed class NewAttachActivityRecord : ActivityFunctionRecord
    {
        private List<long> _attachCharacterId = new List<long>();
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> AttachCharacterId { get { return _attachCharacterId; } set { _attachCharacterId = value; } }

        public int Round { get; set; } = 1;

        public void ResetAttach()
        {
            Round++;
            foreach (var id in AttachCharacterId.ToList())
            {
                RemoveAttach(id);
            }
        }

        public void RemoveAttach(long id)
        {
            if (AttachCharacterId.Contains(id))
            {
                AttachCharacterId.Remove(id);
                var character = GameApplication.DataManager.FindValue<Character>(id);
                if (character != null && character.IsOnline)
                {
                    character.LiftedAttach();
                }
            }
        }
    }


    [EntityTable(DbGroup.Entity)]
    public sealed class NewAttachActivityCharacterRecord : SpecialShopRecord
    {

    }
}
