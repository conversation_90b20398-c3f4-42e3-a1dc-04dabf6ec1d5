using HelpBase.Json;
using HelpBase.Web;
using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proto.GameModel;

namespace GameModel.Activities
{
    [WebClass("分享活动")]
    public class EmptyShareActivity : EmptyActivity
    {

        [WebMember("分享类型")]
        [JsonMember]
        public ShareActivityCategory ShareCategory { get; set; }

        public override JsonObject Protocol()
        {
            var res = base.Protocol();
            res[nameof(ShareCategory)] = (int)ShareCategory;
            return res;
        }

    }
    public class EmptyShareACtivity : EmptyActivity
    {
    }
}
