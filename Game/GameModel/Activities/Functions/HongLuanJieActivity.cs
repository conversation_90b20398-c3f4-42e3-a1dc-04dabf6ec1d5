using System.Collections.Concurrent;
using System.Data;
using System.Json;
using DataBase;
using GameModel.Functions.Items;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using GameModel.Rankings;
using HelpBase;
using HelpBase.Json;
using HelpBase.Linq;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel.Activities;

[WebClass("红鸾结活动")]
public class HongLuanJieActivity : ActivityFunction<HongLuanJieActivityRecord>, IInteractionActivity,
    IActionTrigger<Character, UserAction2>
{
    public static long sentMailTemp = 10028;

    public static int wordsLimit = 60;

    //保证两个player获取到相同的值
    public ConcurrentDictionary<(long, long), HongLuanQian> AllHongLuanQians = null;
    public RandomList<HongLuanQian> HongLuanQianList = null;

    public List<HongLuanQian> TopHongLuanQians = null;

    [WebMember("排行榜显示个数")] [JsonMember] public int RankTopCount { set; get; }

    [WebMember("每日点赞数量上限")] [JsonMember] public int DailyPraiseLimit { set; get; }

    public override JsonObject Personal(Character character)
    {
        var res = base.Personal(character);

        var rec = GetCharacterRecord(character, true, true);
        res["TotalCount"] = rec.MyHongLuanQians.Count();

        return res;
    }

    public void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID,
        byte[] bytes)
    {
        switch (action)
        {
            //用道具
            case 1:
                var use_item_data = ProtoHelp.GetObject<IDInt64Int32StringMessage>(bytes);
                if (use_item_data == null)
                {
                    return;
                }

                UseItem(character, use_item_data.ID, use_item_data.Value1, use_item_data.Msg, use_item_data.Value2);
                break;
            //点赞
            case 2:
                var praise_data = ProtoHelp.GetObject<IDMessage>(bytes);
                if (praise_data == null)
                {
                    return;
                }

                Praise(character, praise_data.Id);
                break;
            //刷新榜单
            case 3:
                var show_panel_data = ProtoHelp.GetObject<BoolMessage>(bytes);
                if (show_panel_data == null)
                {
                    return;
                }

                GetRandomHongLuanQian(character, show_panel_data.Value);
                break;
            //获取个人数据
            case 4:
                var show_personnel_data = ProtoHelp.GetObject<Int32Message>(bytes);
                if (show_personnel_data == null)
                {
                    return;
                }

                GetPersonalData(character, show_personnel_data.Value);
                break;
            //获取目标玩家的数据
            case 5:
                var personnel_word_data = ProtoHelp.GetObject<IDMessage>(bytes);
                if (personnel_word_data == null)
                {
                    return;
                }

                GetTargetPlayerData(character, personnel_word_data.Id);
                break;
        }
    }

    public string GetActionNameByAction(byte action)
    {
        return null;
    }

    protected override void OnActivityStart()
    {
        base.OnActivityStart();
        HongLuanQianList = new RandomList<HongLuanQian>();
        AllHongLuanQians = GameApplication.DataManager.LoadGameObjects<HongLuanQian>(p => p.ID > 0, cache: true).Values
            .ToConcurrentDictionary(p =>
            {
                HongLuanQianList.Add(p);
                return p.GetKey();
            });
        InitTopHongLuanQians();
    }

    public void UseItem(Character character, long targetId, long itemType, string message, int times)
    {
        if (message.Length > wordsLimit)
        {
            character.ShowErrorTipsMessage("您书写的誓愿过长，请斟酌修改~");
            return;
        }

        var strCheckResult = IllegalWordHelper.CheckText(character.User.Account.ThirdPartyInfo, message, character.Name,
            character.LeitingRid, "红鸾结", ExtendType.其他内容, character.ID.ToString(), GameApplication.Service.Config.ID);
        if (strCheckResult == IllegalDetectResult.ServiceNotAvailable)
        {
            character.ShowError(GameModel.PicaLocalityStrings.PKT_ServiceNotAvaiable);
            return;
        }

        if (strCheckResult == IllegalDetectResult.IllegalWord)
        {
            character.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000086);
            return;
        }

        var item = character.FirstEntity<Item>(i => i.Type == itemType);
        if (item == null || item.Count < times)
        {
            character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, I18nManager.ItemName(itemType));
            return;
        }

        var func = item.GetFunction<HongLuanPuItemFunction>();
        if (func == null)
        {
            character.ShowErrorTipsMessage("道具类型不正确!");
            return;
        }

        if (!item.Delete(times, "挂红鸾谱"))
        {
            return;
        }

        OnItemUsed(character, targetId, func.Score, message, item.Type, times);
        if (!string.IsNullOrEmpty(func.ShowAnimeName))
        {
            var tree_so =
                character.SceneCharacter.CurrentArea.Objects.Values.FirstOrDefault(s =>
                    s.Type == Constants.HongLuanJieTreeType);
            if (tree_so != null)
            {
                tree_so.DoAction(func.ShowAnimeName, 1);
            }
        }

        UpdateToClient(character);
        var target_chara = GameApplication.DataManager.FindValue<Character>(targetId);
        if (target_chara == null)
        {
            Logger.Error.Write($"target chara is null in hongluanjie {targetId}");
            return;
        }

        if (!string.IsNullOrEmpty(func.RollMessageContent))
        {
            GameApplication.UserManager.ShowRollMessage(
                I18nManager.I18n(func.RollMessageContent, character.Name, target_chara.Name), func.RollMessageSendTimes,
                interval: func.RollMessageSendInterval, outwardsId: func.RollMessageOutwardsId);
        }

        if (func.ShowEffectId > 0)
        {
            var effect_data = new ShowEffectData();
            effect_data.Type = ShowEffectType.Ui;
            effect_data.Id = func.ShowEffectId;
            effect_data.Time = func.ShowEffectTime;
            effect_data.JoinedCharacters.Add(character.ShortSpecData);
            effect_data.JoinedCharacters.Add(target_chara.ShortSpecData);
            if (func.EffectToAllServer)
            {
                GameApplication.UserManager.BroadCastEffectToPublicScene(effect_data);
            }
            else
            {
                character.SceneCharacter.CurrentScene.Broadcast(ClientMethod.ShowEffect, effect_data);
            }
        }
    }

    public void OnItemUsed(Character character, long targetId, int addScore, string message, long itemType, int times)
    {
        var rec = GetCharacterRecord(character, true, true);
        //谁的id大谁是playerA
        var (player_a_id, player_b_id) = character.ID > targetId ? (character.ID, targetId) : (targetId, character.ID);

        HongLuanJieActivityRecord target_rec = null;
        if (!rec.MyHongLuanQians.TryGetValue(targetId, out var hongluanqian_rec))
        {
            //有数据说明对方已经买过了
            if (AllHongLuanQians.TryGetValue((player_a_id, player_b_id), out var inner_rec))
            {
                rec.HongLuanQianIds.Add(inner_rec.ID);
                rec.MyHongLuanQians.Add(targetId, inner_rec);
                hongluanqian_rec = inner_rec;
            }
            //没有数据就建个新的来
            else
            {
                hongluanqian_rec = RowAdapter.Create<HongLuanQian>();
                if (!AllHongLuanQians.TryAdd((player_a_id, player_b_id), hongluanqian_rec))
                {
                    Logger.Error.Write(Name + $" add value err at {player_a_id} {player_b_id}");
                    return;
                }

                HongLuanQianList.Add(hongluanqian_rec);

                hongluanqian_rec.CreateDate = DateTimeExtension.Now;
                rec.HongLuanQianIds.Add(hongluanqian_rec.ID);
                rec.MyHongLuanQians.Add(targetId, hongluanqian_rec);
                hongluanqian_rec.PlayerAID = player_a_id;
                hongluanqian_rec.PlayerBID = player_b_id;


                var target_chara = GameApplication.DataManager.FindValue<Character>(targetId);
                if (target_chara != null)
                {
                    target_chara.SendMailFromTemplete(sentMailTemp,
                        "赠送红鸾结",
                        contentFormat: p => string.Format(p, character.Name));
                }
            }
        }

        //如果没有信息就赋值
        if (character.ID == player_a_id && string.IsNullOrEmpty(hongluanqian_rec.PlayerASpeak))
        {
            hongluanqian_rec.PlayerASpeak = message;
        }
        else if (string.IsNullOrEmpty(hongluanqian_rec.PlayerBSpeak))
        {
            hongluanqian_rec.PlayerBSpeak = message;
        }

        hongluanqian_rec.TotalScore += addScore * times;
        hongluanqian_rec.LastAddDate = DateTimeExtension.Now;
        if (addScore > hongluanqian_rec.NowShowItemScore)
        {
            hongluanqian_rec.ShowItemType = itemType;
            hongluanqian_rec.NowShowItemScore = addScore;
        }

        hongluanqian_rec.Save();
        rec.Save();
        //更新分数
        OnScoreUpdate(hongluanqian_rec);
    }

    public void GetRandomHongLuanQian(Character character, bool reedTops)
    {
        var show_res = new List<HongLuanQian>();
        if (reedTops)
        {
            show_res.AddRange(TopHongLuanQians);
        }
        else
        {
            show_res.AddRange(HongLuanQianList.GetRandomItems(RankTopCount));
        }

        show_res.AddRange(HongLuanQianList.Randoms(RankTopCount));

        var data_to_send = new HongLuanQianDataListData();
        data_to_send.Data.AddRange(show_res.Select(sr => sr.ProtoData));
        SendInteraction(character, 3, 0, data_to_send);
    }

    public void GetPersonalData(Character character, int page)
    {
        var rec = GetCharacterRecord(character, true, true);

        var data_to_send = new HongLuanQianDataListData();
        data_to_send.Data.AddRange(rec.MyHongLuanQians.Values.OrderByDescending(o => o.CreateDate)
            .Skip(10 * (page - 1 < 0 ? 0 : page - 1)).Take(10).ToList().Select(sr => sr.ProtoData));
        SendInteraction(character, 4, 0, data_to_send);
    }

    public void GetTargetPlayerData(Character character, long targetId)
    {
        var rec = GetCharacterRecord(character, true, true);

        if (!rec.MyHongLuanQians.TryGetValue(targetId, out var hongluanqian_rec))
        {
            SendInteraction(character, 5, 0, null);
            return;
        }

        SendInteraction(character, 5, 0, hongluanqian_rec.ProtoData);
    }

    public void Praise(Character character, long hongluanqianId)
    {
        var rec = GetCharacterRecord(character, true, true);
        if (rec.TodayPraisedIds.Count >= DailyPraiseLimit)
        {
            character.ShowErrorTipsMessage("今日已达点赞上限");
            return;
        }

        if (rec.TodayPraisedIds.Contains(hongluanqianId))
        {
            character.ShowErrorTipsMessage("今天已经点赞过这个红鸾谱了");
            return;
        }

        var item = GameApplication.DataManager.FindValue<HongLuanQian>(hongluanqianId);
        if (item == null)
        {
            character.ShowErrorTipsMessage("找不到目标");
            return;
        }

        rec.TodayPraisedIds.Add(hongluanqianId);

        item.PraiseCount++;
        item.Save();
        rec.Save();
        UpdateToClient(character);

        SendInteraction(character, 2, 0, item.ProtoData);
    }

    private object _score_update_lock = new object();

    public void OnScoreUpdate(HongLuanQian hongluanqian)
    {
        if (TopHongLuanQians == null)
        {
            Logger.Error.Write(Name + $"TopHongLuanQians is null while add score");
            return;
        }

        lock (_score_update_lock)
        {
            if (!TopHongLuanQians.Contains(hongluanqian) && (TopHongLuanQians.Count < RankTopCount ||
                                                             hongluanqian.TotalScore >
                                                             (TopHongLuanQians.LastOrDefault()?.TotalScore ?? 0)))
            {
                TopHongLuanQians.Add(hongluanqian);
            }

            TopHongLuanQians = TopHongLuanQians.OrderByDescending(o => o.TotalScore).ThenBy(o => o.LastAddDate)
                .ToList();
            if (TopHongLuanQians.Count > RankTopCount)
            {
                TopHongLuanQians.RemoveAt(RankTopCount);
            }
        }
    }

    private void InitTopHongLuanQians()
    {
        TopHongLuanQians = AllHongLuanQians.Values.OrderByDescending(o => o.TotalScore).ThenBy(o => o.LastAddDate)
            .Take(RankTopCount).ToList();
    }

    public UserAction2 Action => UserAction2.跨日;

    public void Trigger(Character source, UserAction2 action, object param)
    {
        var rec = GetCharacterRecord(source, true, true);
        rec.TodayPraisedIds.Clear();
        rec.Save();
        UpdateToClient(source);
    }
}

[EntityTable(DbGroup.Entity)]
public class HongLuanJieActivityRecord : ActivityCharacterRecord
{
    [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
    public List<long> HongLuanQianIds { set; get; } = new List<long>();

    [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
    public List<long> TodayPraisedIds { set; get; } = new List<long>();

    private Dictionary<long, HongLuanQian> _myHongLuanQians = null;

    public Dictionary<long, HongLuanQian> MyHongLuanQians
    {
        get
        {
            if (_myHongLuanQians == null)
            {
                _myHongLuanQians = new Dictionary<long, HongLuanQian>();
                foreach (var hongLuanQianId in HongLuanQianIds)
                {
                    var item = GameApplication.DataManager.FindValue<HongLuanQian>(hongLuanQianId);
                    if (item == null)
                    {
                        Logger.Error.Write($"hongluanqian is empty at {hongLuanQianId}");
                        continue;
                    }

                    var target_id = item.PlayerAID == CharacterID ? item.PlayerBID : item.PlayerAID;
                    _myHongLuanQians.Add(target_id, item);
                }
            }

            return _myHongLuanQians;
        }
    }
}

[EntityTable(DbGroup.Entity, typeof(EntityBase))]
public class HongLuanQian : AbstractEntity, IProbabilityItem, ICacheSelf, IProtoObject<HongLuanQianData>
{
    [TableColumn] public long PlayerAID { set; get; }

    private CharacterSpecData _playerASpecData = null;

    public CharacterSpecData PlayerASpecData
    {
        get
        {
            if (_playerASpecData == null)
            {
                _playerASpecData = GameApplication.DataManager.FindValue<Character>(PlayerAID).MiniSpecData;
            }

            return _playerASpecData;
        }
    }

    [TableColumn] public long PlayerBID { set; get; }

    private CharacterSpecData _playerBSpecData = null;

    public CharacterSpecData PlayerBSpecData
    {
        get
        {
            if (_playerBSpecData == null)
            {
                _playerBSpecData = GameApplication.DataManager.FindValue<Character>(PlayerBID).MiniSpecData;
            }

            return _playerBSpecData;
        }
    }

    [TableColumn] public long ShowItemType { set; get; }

    [TableColumn] public int NowShowItemScore { set; get; }

    [TableColumn] public int TotalScore { set; get; }

    [TableColumn] public int PraiseCount { set; get; }

    [TableColumn] public string PlayerASpeak { set; get; }

    [TableColumn] public string PlayerBSpeak { set; get; }

    [TableColumn] public DateTime CreateDate { set; get; }

    [TableColumn] public DateTime LastAddDate { set; get; }

    public (long, long) GetKey()
    {
        return (PlayerAID, PlayerBID);
    }

    public int Probability => TotalScore;

    public HongLuanQianData ProtoData
    {
        get
        {
            var res = new HongLuanQianData();

            res.Id = ID;
            res.PlayerAId = PlayerAID;
            res.PlayerBId = PlayerBID;
            res.PlayerASpeak = PlayerASpeak;
            res.PlayerBSpeak = PlayerBSpeak;
            res.Score = TotalScore;
            res.PraiseCount = PraiseCount;
            res.PlayerASpec = PlayerASpecData;
            res.PlayerBSpec = PlayerBSpecData;
            res.NowShowItemType = ShowItemType;

            return res;
        }
    }

    public JsonObject JsonProto
    {
        get
        {
            var res = new JsonObject();

            res[nameof(ID)] = ID;
            res[nameof(PlayerAID)] = PlayerAID;
            res[nameof(PlayerBID)] = PlayerBID;
            res[nameof(PlayerASpeak)] = PlayerASpeak;
            res[nameof(PlayerBSpeak)] = PlayerBSpeak;
            res[nameof(TotalScore)] = TotalScore;
            res[nameof(PraiseCount)] = PraiseCount;
            res[nameof(PlayerASpecData)] = PlayerASpecData.ToString();
            res[nameof(PlayerBSpecData)] = PlayerBSpecData.ToString();
            res["NowShowItemType"] = ShowItemType;

            return res;
        }
    }
}