using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using GameModel.Award;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel.Activities
{
    [WebClass("角色等级奖励活动")]
    public class CharacterLvAwardActivity : ActivityFunction<CharacterLvAwardActivityRecord>, IInteractionActivity
    {
        [WebMember("等级奖励配置")]
        [JsonMember(FormatType = typeof(JsonListFormat<CharacterLvAward>))]
        public List<CharacterLvAward> Awards { get; set; }

        public override JsonObject Personal(Character character)
        {
            var json = base.Personal(character);
            var record = GetCharacterRecord(character, true);
            if (record != null)
            {
                json[nameof(record.Lvs)] = new JsonArray(record.Lvs.Select(p => new JsonPrimitive(p)));
            }
            return json;
        }

        public override JsonObject Protocol()
        {
            var json = base.Protocol();
            if (Awards != null)
            {
                json[nameof(Awards)] = new JsonArray(Awards.Select(p => p.ProtoData));
            }

            return json;
        }

        public void DoActivityInteraction(Character character, byte action, ActionTargetCategory category, long targetID, byte[] bytes)
        {
            if (Owner.Phase != ActivityPhase.开启)
            {
                character.ShowError(LocalityStrings.activityNotOpen);
                return;
            }
            var needLv = (int)targetID;
            if (character.Lv < needLv)
            {
                character.ShowError(LocalityStrings.operationError);
                return;
            }
            var record = GetCharacterRecord(character, true, true);
            if (record.Lvs.Contains(needLv))
            {
                character.ShowError(LocalityStrings.repetitiveOperation9);
                return;
            }
            var setting = Awards?.Find(p => p.Lv == needLv);
            if (setting == null)
            {
                character.ShowError(LocalityStrings.argumentError);
                return;
            }

            record.Lvs.Add(needLv);
            record.Save();

            setting.GetAwards(this, character);
            ShowUpdateRecord(character);
        }
        public string GetActionNameByAction(byte action)
        {
            return "领取角色等级奖励";
        }


        public class CharacterLvAward : JsonFormatObject
        {
            [JsonMember]
            [WebMember("等级")]
            public int Lv { get; set; }

            [WebMember("奖励")]
            [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
            public JsonDynamicList<AwardBase> Awards { get; set; }

            [WebMember("会员奖励")]
            [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
            public JsonDynamicList<AwardBase> MemberAwards { get; set; }

            public JsonObject ProtoData
            {
                get
                {
                    var json = new JsonObject();
                    json[nameof(Lv)] = Lv;
                    json[nameof(Awards)] = new JsonArray(Awards.Select(p => p.Spec.ProtoData.ToJson()));
                    if (MemberAwards?.Count > 0)
                    {
                        json[nameof(MemberAwards)] = new JsonArray(MemberAwards.Select(p => p.Spec.ProtoData.ToJson()));
                    }
                    return json;
                }
            }

            public void GetAwards(ActivityFunction activity, Character owner)
            {
                var awardGroup = new AwardGroup();
                Awards.ForEach(p => awardGroup.Add(p));
                if (MemberAwards?.Count > 0)
                {
                    MemberAwards.ForEach(p => awardGroup.Add(p));
                }

                var result = owner.GetAwards(awardGroup, "等级奖励活动", null, Lv);
                owner.User.ShowAwardResult(result);
            }
        }
    }
}
