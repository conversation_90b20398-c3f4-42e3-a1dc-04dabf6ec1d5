using GameModel.Award;
using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Activities
{
    public interface IRetroactiveAwardActivity
    {
        JsonDynamicList<AwardBase> GetRetroactiveAward(RetroactiveAwardCharacterRecord record);
        string RetroactiveMailTitle { get; }
        string RetroactiveMailContent { get; }
    }
}
