using GameModel.Award;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Activities.Components
{
    [WebClass("活动邮件组件")]
    public class ActivityMailComponent : ActivityComponent, IActionTrigger<Character, UserAction2>
    {
        public UserAction2 Action => UserAction2.登录 | UserAction2.跨日;
        [WebMember("邮件设置集")]
        [JsonMember(FormatType = typeof(JsonListFormat<ActivityMailSetting>))]
        public List<ActivityMailSetting> MailSettings { get; set; } = new List<ActivityMailSetting>();

        private Dictionary<long, DateTime> DateCheckData = new Dictionary<long, DateTime>();

        public void Trigger(Character source, UserAction2 action, object param)
        {
            if (!DateCheckData.TryGetValue(source.ID, out var check_time))
            {
                check_time = source.LogoutTime;
            }
            var settings_to_check = MailSettings.Where(m => m.SendDate > check_time && m.SendDate <= DateTimeExtension.Now);
            DateCheckData[source.ID] = DateTimeExtension.Now;
            foreach (var mail_setting in settings_to_check)
            {
                var condition_checked = true;
                if (mail_setting.Conditions != null)
                {
                    condition_checked = source.CheckCondition(mail_setting.Conditions);
                }
                if (!condition_checked)
                {
                    return;
                }
                source.AddMail(mail_setting.Title, mail_setting.Content, mail_setting.Awards, true, DateTimeExtension.Now.AddDays(mail_setting.ExpireDate), (mail_setting.Awards == null) ? MailType.Common : MailType.Award, Owner.Name + "邮件发放");
            }
        }
    }

    [WebClass("活动邮件设置")]
    public class ActivityMailSetting : JsonFormatObject
    {
        [WebMember("发送日期")]
        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime SendDate { set; get; }

        [WebMember("标题")]
        [JsonMember]
        public string Title { set; get; }

        [WebMember("内容")]
        [JsonMember]
        public string Content { set; get; }

        [WebMember("过期时长")]
        [JsonMember]
        public int ExpireDate { set; get; }

        [WebMember("邮件奖励(没有就不填)")]
        [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
        public JsonDynamicList<AwardBase> Awards { set; get; }

        [WebMember("接受邮件条件")]
        [JsonMember(FormatType = typeof(DynamicCollectionFormat<ConditionBase, JsonDynamicList<ConditionBase>>))]
        public JsonDynamicList<ConditionBase> Conditions { set; get; }
    }
}
