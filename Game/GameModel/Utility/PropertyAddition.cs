using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    public class PropertyAddition : JsonFormatObject
    {
        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public int Value { get; set; }

        public PropertyAdditionData ProtoData
        {
            get 
            {
                var data = new PropertyAdditionData();
                data.Type = Type;
                data.Value = Value;
                return data;
            }
        }
    }
}
