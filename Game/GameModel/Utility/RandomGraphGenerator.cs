using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Utility
{
    public class RandomGraphGenerator
    {
        private int[] OriginEdges;
        private List<GraphNode> nodes;
        public RandomGraphGenerator(int[] edges)
        {
            OriginEdges = edges;

            nodes = new List<GraphNode>();
            var index = 0;
            foreach (var edge in edges)
            {
                nodes.Add(new GraphNode() { Id = ++index, EdgeCount = edge });
            }
        }

        public List<GraphNode> Generate()
        {
            var random = new Random();
            var tempList = new List<GraphNode>(nodes);

            // 第一步：把只有一条边的绑定到一个随机节点上（需要 > 2) 的
            for (int i = 0; i < nodes.Count; i++)
            {
                if (nodes[i].EdgeCount == 1)
                {
                    // 寻找一个随机节点
                    var targetCandicates = nodes.Where(n => n.EdgeCount > 2).ToList();
                    var target = targetCandicates[random.Next(targetCandicates.Count)];
                    ConnectNodes(nodes[i], target);

                    tempList.Remove(nodes[i]);
                    if (target.EdgeCount == 0) tempList.Remove(target);
                }
            }

            var errorIndex = VerifyFirstRound(tempList);
            if (errorIndex > 0) throw new Exception("Error " + errorIndex);

            // 第二步，剩下的节点，先连成一个环
            // 此时剩下的节点都是 edge >= 2, 所以能保证一出一入
            var tempList2 = new List<GraphNode>(tempList);
            var head = tempList2[random.Next(tempList2.Count)];
            var first = head;
            tempList2.Remove(first);
            while (tempList2.Count > 0)
            {
                var second = tempList2[random.Next(tempList2.Count)];
                ConnectNodes(first, second);
                tempList2.Remove(second);

                first = second;
            }

            ConnectNodes(first, head);

            for (int i = tempList.Count - 1; i >= 0; i--)
            {
                if (tempList[i].EdgeCount == 0) tempList.RemoveAt(i);
            }

            PrintNodes();
            // 完成一个环

            // 第三步，每次选出一个点，然后随机找一个 edge 不为 0 的（可以包含自己）
            while (tempList.Count > 0)
            {
                var node1 = tempList[0];
                var node2 = node1.EdgeCount == 1 ? tempList[random.Next(1, tempList.Count)] : tempList[random.Next(tempList.Count)]; // 不能在只有一条边的时候又连回自己
                ConnectNodes(node1, node2);
                if (node1.EdgeCount <= 0) tempList.Remove(node1);
                if (node2.EdgeCount <= 0) tempList.Remove(node2);
            }

            return nodes;
        }

        public int VerifyFirstRound(List<GraphNode> list)
        {
            for (int i = 0; i < list.Count; i++)
            {
                var node = list[i];
                if (node.EdgeCount < 2) return i;
            }
            return -1;
        }

        public void PrintNodes()
        {
            foreach (var node in nodes)
            {
                Console.WriteLine($"Node {node.Id} _ {node.EdgeCount}: {string.Join(", ", node.Edges)}");
            }
        }

        public bool Verify()
        {
            foreach (var node in nodes)
            {
                if (node.EdgeCount != 0) return false;
                if (node.Edges.Count != OriginEdges[node.Id - 1]) return false;

            }

            return true;
        }

        private void ConnectNodes(GraphNode n1, GraphNode n2)
        {
            if (n1.EdgeCount <= 0) throw new Exception("Error");
            if (n2.EdgeCount <= 0) throw new Exception("Error");

            n1.EdgeCount--;
            n2.EdgeCount--;

            Console.WriteLine($"Connect: {n1.Id} {n2.Id}");

            n1.Edges.Add(n2.Id);
            n2.Edges.Add(n1.Id);
        }
    }

    public class GraphNode
    {
        public int Id { get; set; }
        public int EdgeCount { get; set; }

        public List<int> Edges { get; set; } = new List<int>();
    }
}
