using System.Text;
using DataBase;
using GameModel.Award;
using GameModel.Dimensions;
using GameModel.Functions.Equip.Role;
using GameModel.Managers;
using HelpBase;
using JWT.Builder;
using JWT.Exceptions;
using Proto.GameModel;

namespace GameModel
{
    public static class GameExtension2
    {
        public static void MergeAdd(this List<Addition> addtions, List<Addition> targetAdditions)
        {
            foreach (var a in targetAdditions)
            {
                if (addtions.Any(p => p.Type == a.Type && p.Mode == a.Mode))
                {
                    var ta = addtions.First(p => p.Type == a.Type && p.Mode == a.Mode);
                    a.Value += ta.Value;
                }
                else
                {
                    addtions.Add((Addition)a.Clone());
                }
            }
        }

        public static void MergeAdd(this List<Addition> addtions, Addition a, bool clone = true)
        {
            if (addtions.Any(p => p.Type == a.Type && p.Mode == a.Mode))
            {
                var ta = addtions.First(p => p.Type == a.Type && p.Mode == a.Mode);
                ta.Value += a.Value;
            }
            else
            {
                if (clone)
                {
                    addtions.Add((Addition)a.Clone());
                }
                else
                {
                    addtions.Add(a);
                }
            }
        }

        public static bool Compare(this long value, long targetValue, CompareType ct)
        {
            if (ct == CompareType.大于)
            {
                return value > targetValue;
            }
            else if (ct == CompareType.大于等于)
            {
                return value >= targetValue;
            }
            else if (ct == CompareType.等于)
            {
                return value == targetValue;
            }
            else if (ct == CompareType.小于)
            {
                return value < targetValue;
            }
            else if (ct == CompareType.小于等于)
            {
                return value <= targetValue;
            }
            return true;
        }

        public static bool Compare(this int value, int targetValue, CompareType ct)
        {
            if (ct == CompareType.大于)
            {
                return value > targetValue;
            }
            else if (ct == CompareType.大于等于)
            {
                return value >= targetValue;
            }
            else if (ct == CompareType.等于)
            {
                return value == targetValue;
            }
            else if (ct == CompareType.小于)
            {
                return value < targetValue;
            }
            else if (ct == CompareType.小于等于)
            {
                return value <= targetValue;
            }
            return true;
        }

        public static string Description(this AwardResult ar)
        {
            var ospec = "";
            foreach (var ard in ar)
            {
                if (ard.Type > 0)
                {
                    var td = GameApplication.DataManager.FindValueNoConstraint<EntityType>(ard.Type);
                    if (td != null)
                    {
                        ospec += td.Name + "x" + ard.Value + ",";
                    }
                    else
                    {
                        ospec += ard.Type + "x" + ard.Value + ",";
                    }
                }
                else
                {
                    ospec += AwardResult.GetPropertyShowName(ard.Name) + "x" + ard.Value + ",";
                }
            }
            ospec = ospec.TrimEnd(',');
            return ospec;
        }

        public static void SendToSceneService(this IArea scene, SceneManagerClientMethod opcode, IProtoObject data = null)
        {
            //if (scene == null)
            //    throw new ArgumentNullException(nameof(scene));

            if (scene is Area local)
            {
                local.SendToSceneService(opcode, data);
            }
        }

        public static void SendToPanel(this User user, PanelCode panel, long targetID, ClientMethod opcode, IProtoObject data = null)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            if (user.Character?.IsOnline == true)
            {
                if (user.Character.Panel == panel)
                {
                    user.SendGoogleProto(opcode, data);
                }
            }
        }

        public static LocalityString GetName(this RelationGroup relationGroup)
        {
            switch (relationGroup)
            {
                case RelationGroup.Attention:
                    return LocalityStrings.attentionName;
                case RelationGroup.BlackList:
                    return LocalityStrings.blackListName;
                case RelationGroup.Friend:
                    return LocalityStrings.friendName;
                case RelationGroup.Recommend:
                    return LocalityStrings.recommendName;
                default:
                    throw new ArgumentException("unknown group: " + relationGroup.ToString());
            }
        }

        public static string ToString(this AwardBase award, string format)
        {
            if (award == null)
                throw new ArgumentNullException(nameof(award));
            if (format == null)
                throw new ArgumentNullException(nameof(format));

            switch (award)
            {
                case PropertyAward pa:
                    return pa.PropertyName;
                case ObjectAward oa:
                    return string.Format(format, oa.TypeData?.Name, oa.Count);
                case VirtualCurrencyAward vca:
                    return string.Format(format, vca.TypeData?.Name, vca.Value);
                case VirtualObjectAward voa:
                    return string.Format(format, voa.TypeData?.ID, voa.Count);
                default:
                    throw new InvalidOperationException("unknown award: " + award.GetType());
            }
        }

        public static T CreateScene<T>(this DimensionLevel source, Character owner, long? key = null)
            where T : Scene
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            return (T)source.CreateScene(owner, key);
        }

        public static T GetScene<T>(this DimensionLevel source, long key, bool loadIfNotExists = false)
            where T : Scene
        {
            return (T)source.GetScene(key, loadIfNotExists);
        }

        public static T GetScene<T>(this SceneManager source, long key, bool loadIfNotExists = false)
            where T : Scene
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            return (T)source.GetScene(key, loadIfNotExists);
        }

        public static bool Ignore(this ClientNotificationMode mode)
        {
            return mode == ClientNotificationMode.Ignore;
        }

        public static bool TryDecode(this JwtBuilder source, string token, out string decode)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));
            if (token == null)
                throw new ArgumentNullException(nameof(token));
            if (token == string.Empty)
                throw new ArgumentException(nameof(token));

            try
            {
                decode = source.Decode(token);
                return true;
            }
            catch
            {
                decode = null;
                return false;
            }
        }

        public static bool Validate(this JwtBuilder source, string token)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));
            if (token == null)
                throw new ArgumentNullException(nameof(token));
            if (token == string.Empty)
                throw new ArgumentException(nameof(token));

            try
            {
                source.Decode(token);
                return true;
            }
            catch (SignatureVerificationException)
            {
                return false;
            }
        }

        public static string GetLanguageString(this EntityType source, string language)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            var entityLanguage = GameApplication.DataManager.FindValue<EntityLanguage>(source.ID);
            var stringLanguage = entityLanguage?.Name.Find(p => p.Language == language);
            if (stringLanguage != null) return stringLanguage.Value;

            return source.Name;
        }

        public static string GetLanguageString(this VirtualObjectType source, string language)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            var entityLanguage = GameApplication.DataManager.FindValue<VirtualObjectLanguage>(source.ID);
            var stringLanguage = entityLanguage?.Name.Find(p => p.Language == language);
            if (stringLanguage != null) return stringLanguage.Value;

            return source.ID.ToString();
        }

        public static string GetLanguageString(this VirtualCurrencyType source, string language)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            var entityLanguage = GameApplication.DataManager.FindValue<VirtualCurrencyLanguage>(source.ID);
            var stringLanguage = entityLanguage?.Name.Find(p => p.Language == language);
            if (stringLanguage != null) return stringLanguage.Value;

            return source.Name;
        }

        public static MailLanguage GetOrDefault(this List<MailLanguage> source, string language)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            var mailLanguage = source.Find(p => p.Language.Equals(language, StringComparison.OrdinalIgnoreCase));
            if (mailLanguage == null) mailLanguage = source.Where(p => p.IsDefault).Random();
            if (mailLanguage == null) mailLanguage = source.Random();
            return mailLanguage;
        }

        public static void Initialize(this Account account, AccountData data)
        {
            if (account == null)
                throw new ArgumentNullException(nameof(account));
            if (data == null)
                throw new ArgumentNullException(nameof(data));

            account.Data = data;
            account.Password = data.Password;
            account.IsAdult = data.IsAdult;
            account.NickName = data.NickName;
            if (GameApplication.Instance.GameConfig.IsTest)
            {
                account.AdminType = GameApplication.Instance.GameConfig.TestAdminType;
            }
            if (!string.IsNullOrEmpty(data.Platform))
            {
                account.Platform = data.Platform;
            }
            else
            {
                account.Platform = GameApplication.Instance.GameConfig.Platform;
            }
        }

        public static TimeRegionData ProtoData(this TimeRegion source)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));

            var data = new TimeRegionData();
            data.StartTime = source.StartTime.ToString();
            data.EndTime = source.EndTime.ToString();
            if (source.DayOfWeeks?.Count > 0)
            {
                data.DayOfWeeks.AddRange(source.DayOfWeeks.Select(p => (byte)p));
            }
            return data;
        }
    }
}