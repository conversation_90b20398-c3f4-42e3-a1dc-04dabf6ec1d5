using HelpBase.Linq;
using HelpBase.Threading;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataBase;
using Proto.GameModel;

namespace GameModel
{
    public class OrganizationManager : ManagerBase
    {
        public ConcurrentDictionary<long, Organization> Organizations = new ConcurrentDictionary<long, Organization>();
        public override int Priority => 1100;

        public override void Initialize()
        {
            base.Initialize();
            Organizations = GameApplication.DataManager.LoadGameObjects<Organization>(p => !p.Deleted, cache: false).Values.ToConcurrentDictionary(p => p.ID);
        }

        public Organization TryGetOrganization(long id)
        {
            if (Organizations.TryGetValue(id, out var res))
            {
                return res;
            }
            return null;
        }

        public Organization TryRemoveOrganization(long id)
        {
            if (Organizations.TryRemove(id, out var res))
            {
                return res;
            }
            return null;
        }

        public Organization CreateOrganization(Character character, string name, string declaration, OrganizationTag tag, int cost, long organizationId)
        {
            var organization = RowAdapter.Create<Organization>(organizationId);
            organization.Name = name;
            organization.Declaration = declaration;
            organization.Tag = tag;
            organization.BuildStatus = OrganizationBuildStatus.创建中;
            organization.BuildCost = cost;
            organization.OrganizationTitle = new OrganizationTitle()
            {
                StrTitle = "",
                BackGround = DataCache.Instance.OrganizationTitleConfigCache.DefaultBackground,
                Frame = DataCache.Instance.OrganizationTitleConfigCache.DefaultFrame
            };
            if (GameApplication.SilenceManager.CheckSilenceSetting(SilenceCategory.Organization))
            {
                organization.Name = "家族" + (organizationId % 10000000000);
                organization.Declaration = "注意看，这里是一个互帮互助、团结向上的家族！家族里都是友善的热心居民，大家都是好朋友~";

                character.SendMailFromTemplete(GameConstants.Ceremony.MailTemplate.OrganizationCreateSilence,
                    "家族文本关停补偿",
                    contentFormat: p => string.Format(p, character.Name));
            }
            organization.CreateSuccess();
            organization.Initialize();
            organization.Save();


            var agent = character.OrganizationAgent;
            organization.Leader = agent;
            organization.Join(agent, OrganizationPosition.族长);

            Organizations.TryAdd(organization.ID, organization);

            return organization;
        }

        public override void GoDay()
        {
            base.GoDay();
            foreach (var organization_pair in Organizations)
            {
                try
                {
                    organization_pair.Value.GoDay();
                }
                catch (Exception ex)
                {
                    Logger.Fatel.Write($"organization go day err , id = {organization_pair.Key} at {ex.Message} {ex.StackTrace}");
                }
            }
        }

        public override void GoWeek()
        {
            base.GoWeek();
            foreach (var organization_pair in Organizations)
            {
                try
                {
                    organization_pair.Value.GoWeek();
                }
                catch (Exception ex)
                {
                    Logger.Fatel.Write($"organization go week err , id = {organization_pair.Key} at {ex.Message} {ex.StackTrace}");
                }
            }
        }

        public bool IsInOrganizationWar(Character character, bool showTips = true)
        {
            if (GameApplication.OrganizationWarManager.InOrganizationWar() || GameApplication.OrganizationKitchenManager.InOrganizationWar())
            {
                if (showTips)
                {
                    character.ShowErrorTipsMessage("PKT_Organization0025");
                }
                return true;
            }
            return false;
        }
    }
}
