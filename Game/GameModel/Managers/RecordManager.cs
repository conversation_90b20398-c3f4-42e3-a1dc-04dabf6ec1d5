using DataBase;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel
{
    public enum ElkEvent
    {
        PicaLogin,
        PlaceSceneObject,
        RemoveObject,
        ValueChangeLog,
        RewardGeneration,
        PicaChat
    }

    public class RecordManager : ManagerBase
    {
        private static readonly Dictionary<string, AccountActionRecord> accountLoginRecords = new Dictionary<string, AccountActionRecord>();

        public override int Priority => 1000;

        private static List<DayStateRecord> dayStateRecordCache;

        public static List<DayStateRecord> DayStateRecordCache
        {
            get { return dayStateRecordCache; }
            set { dayStateRecordCache = value; }
        }

        public static AccountActionRecord GetAccountLoginRecord(string account)
        {
            lock (accountLoginRecords)
            {
                if (!accountLoginRecords.TryGetValue(account, out AccountActionRecord record))
                {
                    record = new AccountActionRecord() { Name = account };
                    accountLoginRecords.Add(account, record);
                }

                return record;
            }
        }

        public override void Initialize()
        {
            base.Initialize();
            minuteStateRecord = NewMinuteStateRecord();
        }

        public override void GoDay()
        {
            base.GoDay();
            if (RecordManager.DayStateRecordCache != null && RecordManager.DayStateRecordCache.All(p => p.Date != DayStateRecord.Date))
            {
                RecordManager.DayStateRecordCache.Insert(0, DayStateRecord);
            }
            dayStateRecord = NewDayStateRecord();
            if (RecordManager.DayStateRecordCache != null)
            {
                RecordManager.DayStateRecordCache.Insert(0, DayStateRecord);
            }
        }

        public override void GoMinute()
        {
            if (DateTimeExtension.Now.Minute % 5 == 0)
            {
                minuteStateRecord = NewMinuteStateRecord();
            }
        }

        private static DayStateRecord dayStateRecord;

        public static DayStateRecord DayStateRecord
        {
            get
            {
                if (dayStateRecord == null)
                {
                    var tr = GameApplication.DataManager.LoadObject<DayStateRecord>(GameApplication.DataManager.AccountDBManager,
                        p => p.ServiceID == GameApplication.Service.Config.ID && p.Date == DateTimeExtension.Now.ToNumberOnDayFormat());
                    if (tr != null)
                    {
                        if (tr.Count >= 1)
                        {
                            dayStateRecord = tr.Values.OrderByDescending(p => p.ID).First();
                        }
                        else
                        {
                            dayStateRecord = NewDayStateRecord();
                        }
                    }
                }
                return dayStateRecord;
            }
        }

        private static MinuteStateRecord minuteStateRecord = null;

        public static MinuteStateRecord MinuteStateRecord
        {
            get
            {
                return minuteStateRecord;
            }
        }

        private static DayStateRecord NewDayStateRecord()
        {
            var now = DateTimeExtension.Now;
            var count = GameApplication.UserManager.Onlines.Count;
            var dsr = RowAdapter.Create<DayStateRecord>(cache: false);
            dsr.UniqueIP = GameApplication.Service.TodayUniqueIP;
            dsr.MaxOnline = count;
            dsr.MinOnline = count;
            dsr.MaxOnlineTime = now;
            dsr.MinOnlineTime = now;
            dsr.ServiceID = GameApplication.Service.Config.ID;
            dsr.Date = now.ToNumberOnDayFormat();
            dsr.UserLogin(null);
            dsr.Save();
            return dsr;
        }

        private static MinuteStateRecord NewMinuteStateRecord()
        {
            var now = DateTimeExtension.Now;
            var msr = RowAdapter.Create<MinuteStateRecord>(cache: false);
            msr.Date = now.AddMinutes(-(now.Minute % 5));
            msr.UniqueIP = GameApplication.Service.TodayUniqueIP;
            msr.Onlines = GameApplication.UserManager.Onlines.Count;
            msr.Connections = GameApplication.Service.ConnectionsCount;
            return msr;
        }

        public static void OnUserLogin(Character user)
        {
            var dayStateRecord = DayStateRecord;
            var count = GameApplication.UserManager.Onlines.Count;
            if (dayStateRecord.MaxOnline < count)
            {
                dayStateRecord.MaxOnline = count;
                dayStateRecord.MaxOnlineTime = DateTimeExtension.Now;
            }
            if (dayStateRecord.MinOnline > count)
            {
                dayStateRecord.MinOnline = count;
                dayStateRecord.MinOnlineTime = DateTimeExtension.Now;
            }
            if (DateTimeExtension.Now.ToNumberOnDayFormat() == dayStateRecord.Date)
            {
                dayStateRecord.UserLogin(user);
                dayStateRecord.UniqueIP = GameApplication.Service.TodayUniqueIP;
            }

            dayStateRecord.LoginTimes++;
            dayStateRecord.Save();

            MinuteStateRecord.LoginTimes++;
            MinuteStateRecord.UniqueIP = GameApplication.Service.TodayUniqueIP;
            MinuteStateRecord.Onlines = count;
            MinuteStateRecord.Connections = GameApplication.Service.ConnectionsCount;
            MinuteStateRecord.UniqueIP = GameApplication.Service.TodayUniqueIP;
        }

        public static void OnCreateCharacter()
        {
            DayStateRecord.NewCharacter++;
            MinuteStateRecord.NewCharacter++;
        }

        /// <summary>
        /// 添加登录登出记录
        /// </summary>
        public static void AddLogOutRecord(User user, DateTime loginTime, int thisOnlineTime)
        {
            LoginRecord lr = RowAdapter.Create<LoginRecord>(cache: false);
            lr.AccountName = user.Account.Name;
            lr.DeviceType = user.Account.DeviceType;
            lr.DeviceID = user.Account.DeviceID;
            lr.DeviceName = user.Account.DeviceName;
            lr.DeviceModel = user.Account.DeviceModel;
            lr.DeviceDetail = user.Account.Data.DeviceDetail;
            lr.Platform = user.Account.Platform;
            lr.InviteChannel = user.Account.Data.InviteChannel??"";

            lr.CharacterID = user.ID;
            lr.CharacterName = user.Name;
            lr.OnlineSecond = thisOnlineTime;
            lr.LoginTime = loginTime;
            lr.LogoutTime = DateTimeExtension.Now;
            lr.IP = user.ClientIP;

            lr.LogoutLevel = user.Character.Lv;
            lr.Save();
        }

        public static void AddValueObjectRecord(Character character, string action, string param, long? paramID, string name, double changeValue, double finalValue, long valueID = 0, string? extra = null)
        {
            //if ((int)character.AdminType >= 1)
            //{
            //    return;
            //}

            //目前所有log都用TA日志记录 这里产生record较多 所以先不记录 --2023/6/20 -zzh
            //ValueObjectRecord pcr = RowAdapter.Create<ValueObjectRecord>(cache: false);
            //pcr.Action = action;
            //pcr.Name = name;
            //pcr.Param = param;
            //pcr.ParamID = paramID??0;
            //pcr.ChangedValue = changeValue;
            //pcr.FinalValue = finalValue;
            //pcr.Owner = character.ID;
            //pcr.ValueID = valueID;
            //if (string.IsNullOrEmpty(action))
            //{
            //    var sns = StackInfo.GetStackNamesString();
            //    Logger.ConfigError.Write("AddValueObjectRecord时候Action为空[{0}][{1}][{2}]", name, changeValue, sns);
            //}
        }

        public static void AddObjectRecord(Character character, string action, string param, long paramID, EntityBase eb, int changeValue, int finalValue)
        {
            //if (character == null)
            //    throw new ArgumentNullException(nameof(character));

            //var pcr = RowAdapter.Create<ValueObjectRecord>(cache: false);
            //pcr.Action = action;
            //pcr.Param = param;
            //pcr.ParamID = paramID;
            //pcr.Owner = character.ID;
            //pcr.Name = eb.LogDisplayName;
            //pcr.ValueID = eb.ID;
            //pcr.ChangedValue = changeValue;
            //pcr.FinalValue = finalValue;
            //pcr.Type = eb.Type;
            //pcr.Save();
        }

        public static void AddCharacterRecord(Character character, Account account)
        {
            var cr = RowAdapter.Create<CharacterRecord>(cache: false, isTemping: true);
            cr.Gender = character.Gender;
            cr.AccountName = account.Name;
            cr.CharacterID = character.ID;
            cr.CharacterName = character.Name;
            cr.CreateDate = DateTimeExtension.Now;
            cr.Platform = account.Platform;
            cr.ServiceID = GameApplication.Service.Config.ID;
            cr.Character = character;
            cr.SaveToDB();
        }

        public static void AddCommandRecord(Character character, string command, string param, double cost,int commandID,DateTime clientTime)
        {
            CommandRecord cr = RowAdapter.Create<CommandRecord>(cache: false);
            cr.Command = command;
            cr.Param = param;
            cr.Cost = cost;
            cr.Date = DateTime.Now;
            cr.Owner = character == null ? 0 : character.ID;
            cr.CommandID = commandID;
            cr.ClientTime = clientTime;
            //cr.SaveToDB();
        }

        static void AddPlaceRecord(Area area,long objectID,PlaceActionCode code, IProtoObject po)
        {
            byte[] data = null;
            if (po != null)
            {
                data = ProtoSerializer.Serialize(po);
            }
            PlaceRecord pr = RowAdapter.Create<PlaceRecord>(cache: false);
            pr.Date = DateTime.Now;
            pr.AreaID = area.ID;
            pr.AreaType = area.Type;
            pr.ObjectID = objectID;
            pr.ActionCode = code;

            pr.Data = data;
            pr.Save();
            //cr.SaveToDB();
        }

        static bool CanAddPlaceRecord(Area area)
        {
            if(area.IsTemped)
            {
                return false;
            }
            if(area.Scene?.TypeData.DimensionType==DimensionType.MiniGamePublish
                ||area.Scene?.TypeData.DimensionType == DimensionType.Puzzle
                ||area.Scene?.TypeData.DimensionType == DimensionType.Adventure)
            {
                return false;
            }
            return true;
        }

        public static void AddNewObjectRecord(Area area,SceneObject so)
        {
            if(!CanAddPlaceRecord(area))
            {
                return;
            }
            PlaceObjectRecordData pord = new PlaceObjectRecordData();
            pord.Type = so.Type;
            pord.TransformData = so.Transform.ProtoData;

            AddPlaceRecord(area, so.ID, PlaceActionCode.AddObject, pord);
        }

        public static void AddRemoveObjectRecord(Area area, SceneObject so)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
 
            AddPlaceRecord(area, so.ID, PlaceActionCode.RemoveObject, null);
        }

        public static void AddTransformObjectRecord(Area area, SceneObject so)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
            PlaceObjectRecordData pord = new PlaceObjectRecordData();
            pord.Type = so.Type;
            pord.TransformData = so.Transform.ProtoData;
            AddPlaceRecord(area, so.ID, PlaceActionCode.TransformObject, pord);
        }

        public static void AddNewBlockRecord(Area area, List<BlockObjectData> bodList)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
            BlockObjectListData bold = new BlockObjectListData();
            bold.Datas.AddRange(bodList);
            AddPlaceRecord(area, 0, PlaceActionCode.AddBlocks, bold);
        }

        public static void AddUpdateBlockRecord(Area area, List<BlockObjectData> bodList)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
            BlockObjectListData bold = new BlockObjectListData();
            bold.Datas.AddRange(bodList);
            AddPlaceRecord(area, 0, PlaceActionCode.UpdateBlocks, bold);
        }

        public static void AddTransformBlockRecord(Area area, List<TransformBlockObjectData> tbods)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
            TransformBlockObjectListData tbold = new TransformBlockObjectListData();
            tbold.Datas.AddRange(tbods);
            AddPlaceRecord(area, 0, PlaceActionCode.TransformBlocks, tbold);
        }

        public static void AddRemoveBlockRecord(Area area, List<IntVector3Data> bodList)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
            IntVector3ListData bold = new IntVector3ListData();
            bold.Datas.AddRange(bodList);
            AddPlaceRecord(area, 0, PlaceActionCode.RemoveBlocks, bold);
        }

        public static void AddClearBlockRecord(Area area)
        {
            if (!CanAddPlaceRecord(area))
            {
                return;
            }
             
            AddPlaceRecord(area, 0, PlaceActionCode.ClearBlocks, null);
        }

        /// <summary>
        /// 备用方法 缺少SpeakGroup和SpeakType
        /// </summary>
        public static void AddSpeakRecord(Character character,SpeakGroup group,SpeakType type, long groupID, string message, string url = null)
        {
            SpeakRecord sr = RowAdapter.Create<SpeakRecord>(cache: false);
            sr.OwnerID = character.ID;
            sr.OwnerName = character.Name;
            sr.SpeakGroup = group;
            sr.SpeakType = type;
            sr.GroupID = groupID;
            sr.Message = DBHelp.CheckStringLength(message, 200);
            sr.Url = DBHelp.CheckStringLength(url, 200);
            sr.SpeakTime = DateTimeExtension.Now;
        }

        public static void AddSpeakRecord(SpeakData sd,long groupID=0)
        {
            //SpeakRecord sr = RowAdapter.Create<SpeakRecord>(cache: false);
            //sr.OwnerID = sd.Character.Id;
            //sr.OwnerName = sd.Character.Name;
            //sr.SpeakGroup = sd.Group;
            //sr.GroupID = groupID;
            //sr.SpeakType = sd.Type;
            //sr.Message = DBHelp.CheckStringLength(sd.Message, 200);
            //sr.Url = DBHelp.CheckStringLength(sd.Url, 200);
            //sr.SpeakTime = DateTimeExtension.Now;

            //var character = GameApplication.DataManager.FindValue<Character>(sd.Character.Id);
        }

        public static PayRecord AddPayRecord(Character character, decimal dollar, string orderNumber, string productId, long shopType = 0, bool isVirtual = false)
        {
            if (character == null)
                throw new ArgumentNullException(nameof(character));

            var record = RowAdapter.Create<PayRecord>(cache: false, autoSaving: false);
            record.Dollar = dollar;
            record.AccountName = character.AccountName;
            record.CharacterID = character.ID;
            record.CharacterName = character.Name;
            record.Date = DateTimeExtension.Now;
            record.OrderNumber = orderNumber;
            record.ShopType = shopType;
            record.DeviceID = character.LoginDeviceID;
            record.DeviceName = character.LoginDeviceName;
            record.DeviceModel = character.LoginDeviceModel;
            record.Lv = character.Lv;
            record.PayTimes = character.PayTimes;
            record.IsVirtual = isVirtual;
            record.IP = character.ThisLoginIP;
            record.ProductId = productId;
            record.ServiceID = GameApplication.Service.Config.ID;
            if (character.User != null && character.User.Account != null && !string.IsNullOrEmpty(character.User.Account.Platform))
            {
                record.Platform = character.User.Account.Platform;
            }
            if (!string.IsNullOrEmpty(character.Platform))
            {
                record.Platform = character.Platform;
            }
            else
            {
                record.Platform = GameApplication.Instance.GameConfig.Platform;
            }
            if (!record.SaveToDB(false))
            {
                return null;
            }
            return record;
        }

        public static void AddLvChangeRecord(string source, long key, long ownerID, int changeValue, int finalValue)
        {
            var lcr = RowAdapter.Create<LvChangeRecord>(cache: false);
            lcr.CreateSuccess();
            lcr.Source = source;
            lcr.SourceID = key;
            lcr.OwnerID = ownerID;
            lcr.ChangeValue = changeValue;
            lcr.FinalValue = finalValue;
            lcr.Initialize();
            lcr.Save();
        }

        public static ButtonRecord AddUIButtonRecord(Character character, string action, string param, long paramID)
        {
            var br = RowAdapter.Create<ButtonRecord>(cache: false);
            br.Action = action;
            br.Param = param;
            br.ParamID = paramID;
            br.Owner = character.ID;
            //br.DayClickTimes = 1;
            br.CurrentLv = character.Lv;
            br.CurrentVipLv = character.MemberLv;
            return br;
        }
    }

    public class AccountActionRecord
    {
        private string name = "";

        [JsonMember]
        public string Name
        {
            get { return name; }
            set { name = value; }
        }

        [JsonMember]
        public int WrongTimes
        {
            get;
            set;
        }

        [JsonMember]
        public int RightTimes
        {
            get;
            set;
        }

        private DateTime lastWrongTime = DateTime.MinValue;

        [JsonMember(FormatType =typeof(UTCDateTimeFormat))]
public DateTime LastWrongTime
        {
            get { return lastWrongTime; }
            set { lastWrongTime = value; }
        }

        private bool isNew = false;

        [JsonMember]
        public bool IsNew
        {
            get { return isNew; }
            set { isNew = value; }
        }
    }
}