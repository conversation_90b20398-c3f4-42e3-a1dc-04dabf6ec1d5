using System;
using System.Collections.Generic;
using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    [Serializable]
    public abstract class ActorType : EntityType
    {
        /// <summary>
        /// 性别
        /// </summary>
        [TableColumn(ShowName = "性别", Description = "该类型NPC初始性别")]
        public Gender Gender { get; set; }

        [TableColumn]
        public long MaleAvatar { get; set; }

        [TableColumn]
        public long FemaleAvatar { get; set; }

        [TableColumn(DBType = "nvarchar(500)")]
        public string IconBundle { get; set; }

        [TableColumn]
        public string MaleIcon { get; set; }

        [TableColumn]
        public string FemaleIcon { get; set; }

        [TableColumn]
        public Element Element { get; set; }

        /// <summary>
        /// 移动速度
        /// </summary>
        [TableColumn]
        public double MoveSpeed { get; set; } = 5;

        /// <summary>
        /// 跳跃速度
        /// </summary>
        [TableColumn]
        public double JumpSpeed { get; set; } = 5;

        /// <summary>
        /// 跳跃次数
        /// </summary>
        [TableColumn]
        public int JumpTimes { get; set; } = 1;


        public long GetAvatarType(Gender gender)
        {
            return gender == Gender.女 ? FemaleAvatar : MaleAvatar;
        }
    }
}