using DataBase;
using GameModel.Managers;
using HelpBase.Json;
using HelpBase.Linq;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    partial class Character
    {
        private CharacterCloseFriendData _closeFriendDatas;
        public CharacterCloseFriendData CloseFriendDatas
        {
            get
            {
                if (_closeFriendDatas == null)
                {
                    _closeFriendDatas = GameApplication.DataManager.FindOrLoadGameObject<CharacterCloseFriendData>(ID);
                    if (_closeFriendDatas == null)
                    {
                        _closeFriendDatas = CreateDefaultCloseFriendData();
                    }
                }

                return _closeFriendDatas;
            }
        }

        public CloseFriendGroup GetCloseFriendGroup(long targetId)
        {
            if (CloseFriendDatas.CloseFriendBonds.TryGetValue(targetId, out var closeFriendBond))
            {
                return closeFriendBond.Group;
            }
            return CloseFriendGroup.None;
        }

        public void AcceptCloseFriendApply(long targetId, CloseFriendGroup group)
        {
            if (!NpcShopItemRecordDic.Any(n => n.Type == Constants.CloseFriendTreeType))
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0010"));
                return;
            }
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            if (!RelationAgent.CheckIntimacy(target, Constants.IntimacyForCloseFriend))
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0001"));
                return;
            }
            if (CloseFriendDatas.CloseFriendDateCD.TryGetValue(targetId, out var dateTime) && DateTimeExtension.Now < dateTime.Datetime)
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0003"));
                return;
            }
            if (CloseFriendDatas.CloseFriendBonds.Count >= Constants.CloseFriendLimit)
            {
                ShowErrorTipsMessage("您的密友数量已满");
                return;
            }
            if (target.CloseFriendDatas.CloseFriendBonds.Count >= Constants.CloseFriendLimit)
            {
                ShowErrorTipsMessage("对方的密友数量已满");
                return;
            }
            CreateCloseFriendBond(target, group);
            target.CreateCloseFriendBond(this, group);
            target.CloseFriendDatas.Attention = true; 
            target.UpdateToClient(c => c.CloseFriendAttention = CloseFriendDatas.Attention);
            ShowCloseFriendDatas();
            SendGoogleProto(ClientMethod.AcceptCloseFriendSuc, new CloseFriendData() {  CharacterSpec = target.MiniSpecData, Group = group});
            var apply_str = Constants.CloseFriendApplyStrs.Random();
            PrivateSpeak(targetId, SpeakSubGroup.聊天框, I18nManager.I18n(apply_str), needLog: false);
            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("close_friend_operate")
                    .WriteStringEntry("target_player_id", target.ID)
                    .WriteEntry("target_player_name", target.Name)
                    .WriteEntry("close_friend_group", I18nManager.CloseFriendGroup(group))
                    .WriteEntry("operate_type", "接收密友申请")
                    .WriteToTaLogger();
            }
        }
        public void DeleteCloseFriend(long targetId)
        {
            CloseFriendDatas.CloseFriendDateCD[targetId] = new CloseFriendCDData() { TargetID = targetId, Datetime = DateTimeExtension.Now.AddHours(24) };
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            target.CloseFriendDatas.CloseFriendDateCD[ID] = new CloseFriendCDData() { TargetID = ID, Datetime = DateTimeExtension.Now.AddHours(24) };
            var bond = RemoveCloseFriendBond(target);
            if (bond == null)
            {
                Logger.Error.Write("close friend bond is null at" + ID + " " + targetId);
            }
            var target_bond = target.RemoveCloseFriendBond(this);
            if (target_bond == null)
            {
                Logger.Error.Write("target close friend bond is null at" + ID + " " + targetId);
            }
            ShowCloseFriendDatas();
            CloseFriendDatas.Save();
            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("close_friend_operate")
                    .WriteStringEntry("target_player_id", target.ID)
                    .WriteEntry("target_player_name", target.Name)
                    .WriteEntry("close_friend_group", I18nManager.CloseFriendGroup(bond.Group))
                    .WriteEntry("operate_type", "删除密友")
                    .WriteToTaLogger();
            }
        }

        public void ExchangeCloseFriendIndex(int fromIndex, int toIndex)
        {
            var from_close_friend = CloseFriendDatas.CloseFriendBonds.Values.FirstOrDefault(c => c.Index == fromIndex);
            var to_close_friend = CloseFriendDatas.CloseFriendBonds.Values.FirstOrDefault(c => c.Index == toIndex);

            if (from_close_friend == null)
            {
                return;
            }
            from_close_friend.Index = toIndex;
            if (to_close_friend != null)//交换位置
            {
                to_close_friend.Index = fromIndex;
            }

            CloseFriendDatas.Save();
            ShowCloseFriendDatas();
        }

        public bool CreateCloseFriendBond(Character target, CloseFriendGroup group)
        {
            lock (CloseFriendDatas)
            {
                if (CloseFriendDatas.CloseFriendBonds.TryAdd(target.ID, new CloseFriendBond() { TargetID = target.ID, Group = group, CreateDate = DateTimeExtension.Now, Index = GetFirstAvailableCloseFriendSlut() }))
                {
                    List<int> indexes_to_remove = new List<int>();
                    for (int i = 0; i < CloseFriendDatas.CloseFriendApplyDatas.Count; i++)
                    {
                        if (CloseFriendDatas.CloseFriendApplyDatas[i].TargetID == target.ID)
                        {
                            indexes_to_remove.Insert(0, i);
                        }
                    }
                    if (indexes_to_remove.Count() > 0)
                    {
                        foreach (var index in indexes_to_remove)
                        {
                            CloseFriendDatas.CloseFriendApplyDatas.RemoveAt(index);
                        }
                    }
                    CloseFriendDatas.Save();
                    ShowCloseFriendDatas();
                    return true;
                }
                return false;
            }
        }

        private int GetFirstAvailableCloseFriendSlut()
        {
            return Enumerable.Range(1, 7).FirstOrDefault(i => !CloseFriendDatas.CloseFriendBonds.Any(cfb => cfb.Value.Index == i));
        }

        private CloseFriendBond RemoveCloseFriendBond(Character target)
        {
            lock (CloseFriendDatas.CloseFriendBonds)
            {
                if (CloseFriendDatas.CloseFriendBonds.TryRemove(target.ID, out var res))
                {
                    CloseFriendDatas.Save();
                    ShowCloseFriendDatas();
                    return res;
                }
                return null;
            }
        }


        public void ApplyForCloseFriend(long targetId, CloseFriendGroup group)
        {
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            if (!RelationAgent.CheckIntimacy(target, Constants.IntimacyForCloseFriend))
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0001"));
                return;
            }
            if (CloseFriendDatas.CloseFriendDateCD.TryGetValue(targetId, out var dateTime) && DateTimeExtension.Now < dateTime.Datetime)
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0003"));
                return;
            }
            if (target.CloseFriendDatas.CloseFriendBonds.Any(cfa => cfa.Value.TargetID == ID))
            {
                ShowErrorTipsMessage(I18nManager.I18n("PKT_CloseFriend0004"));
                return;
            }
            if (target.CloseFriendDatas.CloseFriendApplyDatas.Any(cfa => cfa.TargetID == ID && cfa.Group == group))
            {
                return;
            }
            lock (target.CloseFriendDatas.CloseFriendApplyDatas)
            {
                target.CloseFriendDatas.CloseFriendApplyDatas.Insert(0, new CloseFriendApplyData() { Group = group, TargetID = ID });
                target.CloseFriendDatas.Attention = true;
                target.UpdateToClient(c => c.CloseFriendAttention = target.CloseFriendDatas.Attention);
                target.SendGoogleProto(ClientMethod.ReceiveNewCloseFriendApply, new CloseFriendApplyProtoData() { FriendData = new CloseFriendData() { CharacterSpec = ShortSpecData, Group = group }, Intimacy = RelationAgent.GetRelation(target.ID).Intimacy });
                target.CloseFriendDatas.Save();
                //target.ShowCloseFriendDatas();
            }
            PrivateSpeak(targetId, SpeakSubGroup.聊天框, I18nManager.I18n("PKT_CloseFriend0006"), needLog: false);
            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("close_friend_operate")
                    .WriteStringEntry("target_player_id", target.ID)
                    .WriteEntry("target_player_name", target.Name)
                    .WriteEntry("close_friend_group", I18nManager.CloseFriendGroup(group))
                    .WriteEntry("operate_type", "发起密友申请")
                    .WriteToTaLogger();
            }
        }

        public void IgnoreCloseFriendApply(long targetId, CloseFriendGroup group)
        {
            lock (CloseFriendDatas.CloseFriendApplyDatas)
            {
                var item_to_remove = CloseFriendDatas.CloseFriendApplyDatas.FirstOrDefault(cfa => cfa.TargetID == targetId && cfa.Group == group);
                if (item_to_remove == null)
                {
                    return;
                }
                CloseFriendDatas.CloseFriendApplyDatas.Remove(item_to_remove);
                CloseFriendDatas.Save();
                ShowCloseFriendDatas();
            }
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("close_friend_operate")
                    .WriteStringEntry("target_player_id", targetId)
                    .WriteEntry("target_player_name", target.Name)
                    .WriteEntry("close_friend_group", I18nManager.CloseFriendGroup(group))
                    .WriteEntry("operate_type", "拒绝密友申请")
                    .WriteToTaLogger();
            }
        }

        public void ShowCloseFriendDatas()
        {
            if (CloseFriendDatas.Attention)
            {
                CloseFriendDatas.Attention = false;
                UpdateToClient(c => c.CloseFriendAttention = CloseFriendDatas.Attention);
                CloseFriendDatas.Save();
            }
            SendGoogleProto(ClientMethod.ShowGetCloseFriendPanel, CloseFriendDatas.ProtoData);
        }


        public void EquipCloseFriendsSubTitle(long targetId, long background)
        {
            bool idCorrect = CloseFriendDatas.CloseFriendBonds.TryGetValue(targetId, out var bond);
            bool backgroundCorrect = background != 0;
            if (backgroundCorrect)
            {
                var bgComp = ActorComponents.Values.FirstOrDefault(a => a.Type == background);
                var bgConf = GameApplication.DataManager.FindValue<ActorComponentType>(background);
                if (bgConf == null)
                {
                    Logger.Error.Write("装备ac时配置出错, id:" + background);
                    backgroundCorrect = false;
                }
                if (bgComp == null && !(bgConf.ThresholdCategory == ActorComponentThresholdCategory.默认解锁))
                {
                    backgroundCorrect = false;
                }
            }
            if (idCorrect)
            {
                EnterTitleData.EnterTitlePrefixID = 0;
                EnterTitleData.EnterTitlePrefixCharacter = bond.TargetID;
                EnterTitleData.EnterTitleSuffixID = 0;
                EnterTitleData.IsCloseFriend = true;
                EnterTitleData.IsOrganization = false;
            }
            if (backgroundCorrect)
            {
                EnterTitleData.EnterTiltleBackground = background;
            }
            EnterTitleData.Save();
            CheckUpdateAction(this, UserAction.更换Component, (long)(ActorComponentGroup.入场称号后缀));
            SendGoogleProto(ClientMethod.GetEnterTitleResponse, GetEnterTitle());
            UpdateSceneActorData();
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            if (Constants.AllowTaLog)
            {
                var targetName = target == null ? "" : target.Name;
                var bondGroup = bond == null ? CloseFriendGroup.None : bond.Group;
                GetLeiTingTrackWriter("close_friend_operate")
                    .WriteStringEntry("target_player_id", targetId)
                    .WriteEntry("target_player_name", targetName)
                    .WriteEntry("close_friend_group", I18nManager.CloseFriendGroup(bondGroup))
                    .WriteEntry("operate_type", "装备密友头衔")
                    .WriteToTaLogger();
            }
        }

        private CharacterCloseFriendData CreateDefaultCloseFriendData()
        {
            var res = RowAdapter.Create<CharacterCloseFriendData>(ID);
            res.Owner = this;
            return res;
        }
    }

    [EntityTable(DbGroup.Entity)]
    public class CharacterCloseFriendData : AbstractEntity, IProtoObject<CloseFriendPanelData>
    {
        private Character _owner;
        public Character Owner
        {
            get
            {
                if (_owner == null)
                {
                    _owner = GameApplication.DataManager.FindValue<Character>(ID);
                }
                return _owner;
            }
            set
            {
                _owner = value;
            }
        }

        [TableColumn]
        public bool Attention { set; get; }

        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonDictionaryFormat<long, CloseFriendBond>))]
        public Dictionary<long, CloseFriendBond> CloseFriendBonds { get; set; } = new Dictionary<long, CloseFriendBond>();

        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonDictionaryFormat<long, CloseFriendCDData>))]
        public Dictionary<long, CloseFriendCDData> CloseFriendDateCD { get; set; } = new Dictionary<long, CloseFriendCDData>();

        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonListFormat<CloseFriendApplyData>))]
        public List<CloseFriendApplyData> CloseFriendApplyDatas { set; get; } = new List<CloseFriendApplyData>();

        public void Goday()
        {
            var need_save = false;
            lock (CloseFriendDateCD)
            {
                List<long> keys_to_remove = new List<long>();
                foreach (var pair in CloseFriendDateCD)
                {
                    if (pair.Value.Datetime < DateTimeExtension.Now)
                    {
                        keys_to_remove.Add(pair.Key);
                        need_save = true;
                    }
                }
                keys_to_remove.ForEach(k => CloseFriendDateCD.TryRemove(k, out _));
            }
            if (need_save)
            {
                Save();
            }
        }

        public CloseFriendPanelData ProtoData
        {
            get
            {
                var res = new CloseFriendPanelData();

                CloseFriendBonds.Values.ForEach(gfb => res.CloseFriendBonds.Add(new CloseFriendBondData() { FriendData = new CloseFriendData() { CharacterSpec = gfb.Target.ShortSpecData, Group = gfb.Group }, Index = gfb.Index }));
                CloseFriendApplyDatas.ForEach(gfd => res.CloseFriendApplyDatas.Add(new CloseFriendApplyProtoData() { FriendData = new CloseFriendData() { CharacterSpec = gfd.Target.ShortSpecData, Group = gfd.Group }, Intimacy = Owner.RelationAgent.GetRelation(gfd.TargetID)?.Intimacy ?? 0 }));

                return res;
            }
        }
    }

    public class CloseFriendApplyData : JsonFormatObject
    {
        private Character target;
        [JsonMember]
        public long TargetID { set; get; }

        public Character Target
        {
            get => target ??= GameApplication.DataManager.FindValue<Character>(TargetID) ?? throw new InvalidOperationException($"{nameof(Bond)}:{ID} {nameof(TargetID)}:{TargetID} not found");
            set => target = value;
        }

        [JsonMember]
        public CloseFriendGroup Group { set; get; }
    }
    public class CloseFriendCDData : JsonFormatObject
    {
        private Character target;
        [JsonMember]
        public long TargetID { set; get; }

        public Character Target
        {
            get => target ??= GameApplication.DataManager.FindValue<Character>(TargetID) ?? throw new InvalidOperationException($"{nameof(Bond)}:{ID} {nameof(TargetID)}:{TargetID} not found");
            set => target = value;
        }

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime Datetime { set; get; }
    }

    public class CloseFriendBond : JsonFormatObject
    {
        private Character target;
        [JsonMember]
        public long TargetID { set; get; }

        public Character Target
        {
            get => target ??= GameApplication.DataManager.FindValue<Character>(TargetID) ?? throw new InvalidOperationException($"{nameof(Bond)}:{ID} {nameof(TargetID)}:{TargetID} not found");
            set => target = value;
        }

        [JsonMember]
        public CloseFriendGroup Group { set; get; }

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime CreateDate { get; set; }

        [JsonMember]
        public int Index { set; get; }
    }
}
