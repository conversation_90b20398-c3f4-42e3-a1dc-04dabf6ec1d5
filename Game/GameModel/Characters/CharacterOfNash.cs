using DataBase;
using HelpBase.Collections.Concurrent;
using HelpBase.Linq;
using HelpBase.Threading;

namespace GameModel
{
    public partial class Character 
    {
        #region 充值

        private ConvenientDictionary<long, PaymentRecord> paymentRecords;

        /// <summary>
        /// 充值记录集合
        /// </summary>
        public ConvenientDictionary<long, PaymentRecord> PaymentRecords
        {
            get => paymentRecords ?? RecyclableLazyInitializer.EnsureInitialized(ref paymentRecords, this, () =>
            {
                var dictionary = GameApplication.DataManager.LoadGameObjects<PaymentRecord>(p => p.OwnerID == ID, cache: false);
                dictionary.Values.ForEach(p => p.Owner = this);
                return dictionary.Values.ToConvenientDictionary(p => p.Type);
            });
        }

        /// <summary>
        /// 获取指定充值模板的充值记录
        /// </summary>
        public PaymentRecord GetPaymentRecord(PaymentType template)
        {
            if (template == null)
                throw new ArgumentNullException(nameof(template));

            if (!PaymentRecords.TryGetValue(template.ID, out PaymentRecord value))
            {
                value = RowAdapter.Create<PaymentRecord>();
                value.OwnerID = ID;
                value.Type = template.ID;

                value.Owner = this;
                value.Template = template;
                value.CreateSuccess();
                value.Initialize();
                value.Save();
                PaymentRecords[value.Type] = value;
            }

            return value;
        }
        #endregion
    }
}
