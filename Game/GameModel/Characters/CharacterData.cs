using System.Collections.Concurrent;
using System.Json;
using System.Runtime.Serialization;
using DataBase;
using GameModel.Activities;
using GameModel.Award;
using GameModel.Dimensions;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel
{
    [DataContract]
    public partial class Character
    {
        private int charm;
        private int collectionPoint;
        private int prestigeLv;
        private int prestigeExp;

        [DataMember]
        [WebMember("账号", Group = "Basic")]
        [TableColumn(ShowName = "账号名字", DBType = "nvarchar(500)")]
        public string AccountName { get; set; } = "";

        [TableColumn]
        public bool FirstloginTimeLog { get; set; } = true;

        public bool AutoCompMission { get; set; } = false;

        [Obsolete("TableColumn 可以移除")]
        [TableColumn]
        public bool EquipInited { get; set; } = false;

        [TableColumn(DBType = "nvarchar(500)")]
        public string CreateDeviceID { get; set; } = "";

        private string _loginDeviceID = "";

        /// <summary>
        /// 上次登陆的device
        /// </summary>
        [TableColumn(DBType = "nvarchar(500)")]
        public string LoginDeviceID
        {
            get { return _loginDeviceID; }
            set { _loginDeviceID = value; }
        }

        private string _loginDeviceName = "";

        /// <summary>
        /// 上次登陆的device
        /// </summary>
        [TableColumn(DBType = "nvarchar(500)")]
        public string LoginDeviceName
        {
            get { return _loginDeviceName; }
            set { _loginDeviceName = value; }
        }

        [TableColumn(DBType = "nvarchar(500)")]
        public string LoginDeviceModel { get; set; } = "";

        private string _platformNickName = "";

        public DateTime NextExChangeCodeTime;

        /// <summary>
        /// 平台昵称
        /// </summary>
        [Obsolete("可以全部删除")]
        [TableColumn(DBType = "nvarchar(500)")]
        public string PlatformNickName
        {
            get { return _platformNickName; }
            set { _platformNickName = value; }
        }

        [TableColumn(DBType = "nvarchar(200)")]
        public string Platform { get; set; }

        [TableColumn(DBType = "nvarchar(100)")]
        public string ThirdPartyUserId { get; set; }

        public string LeitingRid => ID.ToString();

        [TableColumn]
        public int GetRookieChargePackLevel { get; set; }

        [TableColumn]
        public int TotalLoginDate { get; set; }

        [TableColumn]
        public int FirstChargeBagLoginDate { get; set; }

        [TableColumn]
        public int ReachPrestigeAwardLevel { set; get; }

        public int FirstChargeBagTotalDate => TotalLoginDate - FirstChargeBagLoginDate + 1;

        /// <summary>
        /// 管理员权限
        /// </summary>
        private AdminType adminType = AdminType.普通;

        [TableColumn(ShowName = "管理员权限")]
        [WebMember("管理权限", Group = "Basic", IsReadOnly = false)]
        public AdminType AdminType
        {
            get { return adminType; }
            set
            {
                if (this.adminType != value)
                {
                    var oldvalue = adminType;
                    adminType = value;
                    if (this.SaveUsage != DataBase.SaveUsage.NoSave)
                    {
                        OnAdminTypeChanged(oldvalue, value);
                    }
                }
            }
        }

        /// <summary>
        /// 用户类型
        /// </summary>
        [Obsolete("可以全部删除")]
        [TableColumn]
        public int MemberType { get; set; }

        [TableColumn]
        public int TotalOnlineSecond { set; get; }

        /// <summary>
        /// 解锁房间数量
        /// </summary>
        [Obsolete("可以全部删除")]
        [TableColumn]
        public int UnlockRoomCount { get; set; }

        /// <summary>
        /// 用户评分，用于标记此用户是否可疑
        /// </summary>
        [Obsolete("可以全部删除")]
        [TableColumn]
        public int MemberScore { get; set; }

        [Obsolete("可以全部删除")]
        [TableColumn]
        public DateTime MemberScoreUpdateTime { get; set; } = new DateTime(2000, 1, 1);

        [TableColumn]
        public int TurnoverCulPoint { set; get; }
        [TableColumn]
        public int TurnoverPoint { set; get; }
        [TableColumn]
        public bool SharedToday { set; get; }

        private ActionTime actionTime;

        [WebMember("行为次数", IsReadOnly = false)]
        [TableColumn(DBType = "varchar(max)", IsDelayLoad = true, FormatType = typeof(JsonDataFormat<ActionTime>))]
        public ActionTime ActionTime
        {
            get
            {
                if (actionTime == null)
                {
                    actionTime = GameApplication.DataManager.LoadGameValue<Character, ActionTime>(p => p.ActionTime, p => p.ID == ID, new JsonDataFormat<ActionTime>());
                    if (actionTime == null)
                    {
                        CreateDefaultActionTime();
                    }
                    else
                    {
                        actionTime.SetOwner(this);
                    }

                }
                return actionTime;
            }
            set { actionTime = value; }
        }

        private ExternalAction _externalAction;
        [WebMember("额外行为", IsReadOnly = false)]
        [TableColumn(DBType = "varchar(max)", IsDelayLoad = true, FormatType = typeof(JsonDataFormat<ExternalAction>))]
        public ExternalAction ExternalAction
        {
            get
            {
                if (_externalAction == null)
                {
                    _externalAction = GameApplication.DataManager.LoadGameValue<Character, ExternalAction>(p => p.ExternalAction, p => p.ID == ID, new JsonDataFormat<ExternalAction>());
                    if (_externalAction == null)
                    {
                        CreateDefaultExternalAction();
                    }
                    else
                    {
                        _externalAction.SetOwner(this);
                    }

                }
                return _externalAction;
            }
            set { _externalAction = value; }
        }

        private void CreateDefaultActionTime()
        {
            actionTime = new ActionTime();
            actionTime.CreateSuccess();
            actionTime.SetOwner(this);
        }
        private void CreateDefaultExternalAction()
        {
            _externalAction = new ExternalAction();
            _externalAction.CreateSuccess();
            _externalAction.SetOwner(this);
        }

        private CharacterSetting _setting = new CharacterSetting();

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDataFormat<CharacterSetting>))]
        public CharacterSetting Setting
        {
            get { return _setting; }
            set { _setting = value; }
        }

        [TableColumn]
        public DateTime BannedTradingTime { set; get; }

        public bool CanTrade()
        {
            return BannedTradingTime < DateTimeExtension.Now;
        }

        /// <summary>
        /// 创建角色的服务器
        /// </summary>
        [TableColumn]
        public long BirthService { get; set; }

        [TableColumn]
        public long LastFollwedDate { set; get; }

        [TableColumn]
        public long LastNewFriendDate { set; get; }

        #region Actor

        [TableColumn]
        public long RoleType { get; set; } = 10001;

        [DataMember]
        [TableColumn(DBType = "nvarchar(500)", ShowName = "名字", ShowIndex = 3)]
        public string Name { get; set; }

        [DataMember]
        [TableColumn()]
        [WebMember("性别", Group = "Basic", Category = WebMemberCategory.Condition)]
        public Gender Gender { get; set; }

        private int _lv;
        private int exp;

        [DataMember]
        [TableColumn(ShowName = "等级")]
        [WebMember("等级", Category = WebMemberCategory.Condition)]
        public int Lv
        {
            get
            {
                return _lv;
            }
            set
            {
                if (_lv != value)
                {
                    int oldlv = _lv;
                    _lv = value;
                    if (_lv >= MaxLv)
                    {
                        _lv = MaxLv;
                    }
                    if (SaveUsage != SaveUsage.NoSave)
                    {
                        OnLvChanged(oldlv, _lv, 0);
                    }
                }
            }
        }

        [DataMember]
        [TableColumn(ShowName = "经验")]
        [WebMember("经验", Group = "Fortune", IsReadOnly = false, Category = WebMemberCategory.Award | WebMemberCategory.Condition)]
        public int Exp
        {
            get
            {
                return exp;
            }
            set
            {
                if (Lv >= MaxLv)
                {
                    exp = 0;
                }
                else if (exp != value)
                {
                    int oldvalue = exp;
                    if (SaveUsage != SaveUsage.NoSave)
                    {
                        var addval = value - exp;
                        exp += addval;
                        OnExpChanged(oldvalue, exp);
                    }
                    else
                    {
                        exp = value;
                    }
                }
            }
        }

        public int MaxLv => GameConfig.BaseConfig.MaxCharacterLv;

        public int MaxExp => LvSetting.Exp;

        [TableColumn(DBDefaultValue = DateTimeExtension.DbDefaultMinTime)]
        public DateTime LastUpdateLvTime { get; set; } = DateTimeExtension.MinTime;

        private ExpSetting lvSetting;
        private ExpSetting nextLvSetting;

        public ExpSetting LvSetting
        {
            get => lvSetting ??= GameApplication.DataManager.FindValue<ExpSetting>(Lv);
        }

        public ExpSetting NextLvSetting
        {
            get
            {
                var nextLv = Lv + 1;
                if (nextLv > GameConfig.BaseConfig.MaxCharacterLv)
                    return null;

                return nextLvSetting ??= GameApplication.DataManager.FindValue<ExpSetting>(nextLv);
            }
        }

        [TableColumn(ShowName = "创建时间")]
        public virtual DateTime CreateDate { get; set; }

        #endregion Actor

        [TableColumn]
        public DateTime LastDayUpdateTime { get; set; }

        [TableColumn]
        public DateTime LastDayActorComponentUpdateTime { get; set; } = DateTimeExtension.Now;

        [TableColumn]
        [WebMember("虚弱结束时间", Group = "Battle", FormatType = typeof(UTCDateTimeFormat))]
        public DateTime WeakEndTime { get; set; } = DateTimeExtension.MinTime;

        [TableColumn]
        public bool WeddingTagStatus { set; get; }

        private DateTime stopSpeakDate = DateTimeExtension.MinTime;
        [WebMember("禁言到期时间", FormatType = typeof(UTCDateTimeFormat))]
        [TableColumn(DBDefaultValue = DateTimeExtension.DbDefaultMinTime)]
        public DateTime StopSpeakDate
        {
            get
            {
                return stopSpeakDate;
            }
            set
            {
                if (SaveUsage != SaveUsage.NoSave)
                {
                    Logger.Error.Write("设置了禁言时间" + $" {Name} : {AccountName}" + Environment.StackTrace);
                }
                stopSpeakDate = value;
            }
        }

        [WebMember("上次每周刷新时间", FormatType = typeof(UTCDateTimeFormat))]
        [TableColumn(DBDefaultValue = DateTimeExtension.DbDefaultMinTime)]
        public DateTime LastWeekRefreshDate { get; set; } = DateTimeExtension.Now.AddDays(-7);


        /// <summary>
        /// 禁言但是玩家聊天会单发给自己
        /// </summary>
        [WebMember("伪禁言到期时间", FormatType = typeof(UTCDateTimeFormat))]
        [TableColumn(DBDefaultValue = DateTimeExtension.DbDefaultMinTime)]
        public DateTime StopSpeakButSendSelfDate { get; set; } = DateTimeExtension.MinTime;

        [TableColumn(DBDefaultValue = DateTimeExtension.DbDefaultMinTime)]
        public DateTime StopChangeAreaNameTime { get; set; } = DateTimeExtension.MinTime;

        public DateTime NextWorlSpeakTime;

        public static int WorlSpeakTimeCD = 60;

        [TableColumn]
        public long TempEquipSet { get; set; }

        //private int _threadTestValue;
        //[TableColumn]
        //public int ThreadTestValue
        //{
        //    set
        //    {
        //        _threadTestValue = value;
        //    }
        //    get
        //    {
        //        if (_threadTestValue == 0)
        //        {
        //            foreach (var conf in GameApplication.DataManager.FindValue<ActorPropertyType>(a => true))
        //            {
        //                _threadTestValue += GetProperty(conf.ID).Value;
        //            }
        //        }

        //        return _threadTestValue;
        //    }
        //}


        [TableColumn]
        public DateTime TempEquipExpiredTime { get; set; }
        /// <summary>
        /// 默认场景标识
        /// </summary>
        private long _defaultSceneID { get; set; }
        [TableColumn]
        public long DefaultSceneID
        {
            get => _defaultSceneID;
            set
            {
                if (SaveUsage != SaveUsage.NoSave) // == NoSave 就是指正在初始化内存
                {
                    //var oldScene = GameApplication.SceneManager.GetScene(_defaultSceneID, true);
                    //var newScene = GameApplication.SceneManager.GetScene(value, true);
                    //if (oldScene is PicaRoomScene) GameApplication.RankingManager.Trigger(oldScene, CharacterRoomRankingAction.DefaultRoomCancel);
                    //if (newScene is PicaRoomScene) GameApplication.RankingManager.Trigger(newScene, CharacterRoomRankingAction.DefaultRoomSet);
                    UpdateToClient(c => c.DefaultSceneID = value);
                }
                _defaultSceneID = value;
            }
        }

        public long DefaultRoomType { get; set; }

        /// <summary>
        /// 默认庭舍场景标识
        /// </summary>
        private long _defaultGardenSceneID { get; set; }
        [TableColumn]
        public long DefaultGardenSceneID
        {
            get => _defaultGardenSceneID;
            set { _defaultGardenSceneID = value; }
        }

        /// <summary>
        /// 语言
        /// </summary>
        [TableColumn(DBType = "varchar(5)")]
        public string Language { get; set; }


        /// <summary>
        /// 新手引导
        /// </summary>
        private List<PicaGuide> _newGuide;
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDataFormat<PicaGuide>))]
        public List<PicaGuide> GuideDataList_V2
        {
            get
            {
                if (_newGuide == null)
                {
                    _newGuide = new List<PicaGuide>();
                    var gIDs = new GuideID();
                    foreach (var g in Enum.GetValues(gIDs.GetType()))
                    {
                        _newGuide.Add(new PicaGuide() { GuideID = (GuideID)g, Step = Constants.GuideOpen ? 0 : 100 });
                    }
                }
                return _newGuide;
            }
            set
            {
                _newGuide = value;
            }
        }

        public PicaGuide FindGuide(GuideID gid)
        {
            PicaGuide guide = GuideDataList_V2.FirstOrDefault(g => g.GuideID == gid);
            if (guide == null)
            {
                guide = new PicaGuide() { GuideID = gid, Step = Constants.GuideOpen ? 0 : 100 };
                GuideDataList_V2.Add(guide);
            }
            return guide;
        }

        public long GuideStep(GuideID gid)
        {
            var guide = FindGuide(gid);
            if (guide == null) return 0;
            return guide.Step;
        }

        /// <summary>
        /// 魅力值
        /// </summary>
        [TableColumn]
        public int Charm
        {
            get => charm;
            set => charm = value;
        }

        [TableColumn]
        public bool CheckedInternalTestingReward { get; set; }

        [TableColumn]
        public string ChannelID { set; get; }

        //[TableColumn]
        //public bool NoAcceptingConfessions { set; get; }

        //public string LogoutSign => CryptoHelp.MD5(ChannelID + AccountName + GameApplication.Instance.GameConfig.LeitingSginKey + User.Account.CreateDate.ToString("yyyy-MM-dd HH:mm:ss"));

        public string CreateDataStr => CreateDate.ToString("yyyy-MM-dd HH:mm:ss");

        ///// <summary>
        ///// 声望等级
        ///// </summary>
        //[TableColumn]
        //[WebMember("声望等级", Group = "Fortune", IsReadOnly = false, Category = WebMemberCategory.Condition)]
        //public int PrestigeLv
        //{
        //    get => prestigeLv;
        //    set
        //    {
        //        var oldValue = prestigeLv;
        //        prestigeLv = value;
        //        if (SaveUsage != SaveUsage.NoSave)
        //        {
        //            OnPrestigeLvChanged(oldValue, prestigeLv);
        //        }
        //    }
        //}

        ///// <summary>
        ///// 声望经验
        ///// </summary>
        //[TableColumn]
        //[WebMember("声望经验", Group = "Fortune", IsReadOnly = false, Category = WebMemberCategory.Award)]
        //public int PrestigeExp
        //{
        //    get => prestigeExp;
        //    set
        //    {
        //        var oldValue = prestigeExp;
        //        prestigeExp = value;
        //        if (SaveUsage != SaveUsage.NoSave)
        //        {
        //            OnPrestigeExpChanged(oldValue, prestigeExp);
        //        }
        //    }
        //}

        public void UpdateSpeakFrameToSceneService()
        {
            var req = new SceneAreaIDInt64Message();
            req.SceneID = SceneCharacter.CurrentSceneID;
            req.AreaID = SceneCharacter.CurrentAreaID;
            req.ID = ID;
            req.Value = GetEquipedComponentId(ActorComponentGroup.聊天气泡);
            SceneCharacter.CurrentArea?.SendToSceneService(SceneManagerClientMethod.ChangeSceneActorSpeakFrame, req);
        }

        public void UpdateSceneActorData()
        {
            if (!SceneCharacter.Observer)
            {
                SceneCharacter.CurrentArea?.SendToSceneService(SceneManagerClientMethod.ChangeSceneActorData, SceneCharacter.Data);
            }
        }

        //public long GetSpeakFrameType()
        //{
        //    if (SpeakFrameType == 0)
        //    {
        //        var def_conf = GameApplication.DataManager.FindValue<ActorComponentType>(a => a.Group == ActorComponentGroup.聊天气泡 && a.Default).FirstOrDefault();
        //        return def_conf == null ? 0 : def_conf.ID;
        //    }

        //    return SpeakFrameType;
        //}
        //public long GetActorBackgroundType()
        //{
        //    if (ActorBackgroundType == 0)
        //    {
        //        var def_conf = GameApplication.DataManager.FindValue<ActorComponentType>(a => a.Group == ActorComponentGroup.人物背景 && a.Default).FirstOrDefault();
        //        return def_conf == null ? 0 : def_conf.ID;
        //    }

        //    return ActorBackgroundType;
        //}

        public int GetTotalOnlineSecond()
        {
            return TotalOnlineSecond + (int)(DateTimeExtension.Now - ThisloginTime).TotalSeconds;
        }

        public RokkieMissionCompStatus GetRokkieMissionCompStatus()
        {
            var res = new RokkieMissionCompStatus();
            res.GotAwards.AddRange(ListDatas.RokkieMissionAwardTakenList);
            res.GotGroupAward = GotRokkieMissionAward;

            return res;
        }

        /// <summary>
        /// 重置人生点数
        /// </summary>
        //public void ResetLifePoint()
        //{
        //    //人生点数 = a*能力值 + b*成就点数
        //    var capability = BasicInfo.Properties.Sum(p => p.Basic);
        //    var achievement = GetProperty(GameUtility.AchievementPointType).Final;
        //    var value = (int)Math.Floor(capability * GameConfig.BaseConfig.CapabilityRatio + achievement * GameConfig.BaseConfig.AchievementRatio);
        //    if (LifePoint != value)
        //    {
        //        LifePoint = value;
        //        Save();
        //        UpdateToClient(p => p.LifePoint = LifePoint);
        //        GameApplication.RankingManager.Trigger(this, CharacterRankingAction.LifePointChanged, value);
        //    }
        //}

        #region 月卡

        /// <summary>
        /// 月卡签到的累计天数
        /// </summary>
        [TableColumn]
        public short MonthCardSignInDays { get; set; }

        /// <summary>
        /// 最后一次签到的时间
        /// </summary>
        [TableColumn]
        public DateTime LastMonthCardSignInTime { get; set; }

        /// <summary>
        /// 月卡结束时间
        /// </summary>
        [TableColumn]
        public DateTime MonthCardEndTime { get; set; }
        #endregion

        ///// <summary>
        ///// NFT TokenID
        ///// </summary>
        //[TableColumn]
        //public long TokenID { get; set; }

        ///// <summary>
        ///// 钱包地址
        ///// </summary>
        //[TableColumn(DBType = "varchar(max)")]
        //public string PublicAddress { get; set; }

        ///// <summary>
        ///// 合约地址
        ///// </summary>
        //[TableColumn(DBType = "varchar(max)")]
        //public string ContractAddress { get; set; }

        /// <summary>
        /// 个性化签名
        /// </summary>
        [TableColumn(DBType = "nvarchar(max)")]
        public string Personalization { get; set; }
        /// <summary>
        /// 修复家具数量
        /// </summary>
        [TableColumn]
        public int TotalUnfrozenCount { get; set; }

        /// <summary>
        /// 抽卡数量
        /// </summary>
        [TableColumn]
        public int DrawCardTimes { get; set; }

        /// <summary>
        /// 是否抽过第一次银币十连
        /// </summary>
        [TableColumn]
        public bool DrewCoinTime { get; set; }

        /// <summary>
        /// 免费银币单抽状态
        /// </summary>
        [TableColumn]
        public long SilverPoolFreeSingleStatus { get; set; }

        /// <summary>
        /// 免费银币十连状态
        /// </summary>
        [TableColumn]
        public long SilverPoolFreeTenTimesStatus { get; set; }

        /// <summary>
        /// 购买体力数量
        /// </summary>
        [TableColumn]
        public int BuyEnergyTimes { get; set; }

        private CharacterRoom _roomData;
        public CharacterRoom RoomData
        {
            get
            {
                if (_roomData == null)
                {
                    _roomData = GameApplication.DataManager.FindOrLoadGameObject<CharacterRoom>(ID);
                    if (_roomData == null)
                    {
                        _roomData = CreateDefaultCharacterRoom();
                    }

                    _roomData.Owner = this;
                }
                return _roomData;

            }
        }

        private CharacterRoom CreateDefaultCharacterRoom()
        {
            return RowAdapter.Create<CharacterRoom, CharacterRoom>(ID);
        }

        private CharacterListDatas _listDatas;
        public CharacterListDatas ListDatas
        {
            get
            {
                if (_listDatas == null)
                {
                    _listDatas = GameApplication.DataManager.FindOrLoadGameObject<CharacterListDatas>(ID);
                    if (_listDatas == null)
                    {
                        CreateDefaultListData();
                    }

                    _listDatas.Init(this);
                }
                return _listDatas;

            }
        }

        private void CreateDefaultListData()
        {
            _listDatas = RowAdapter.Create<CharacterListDatas, CharacterListDatas>(ID);
            _listDatas.Owner = this;
        }

        public long[] TipOffLimit = { DateTimeExtension.NowTimeStamp / 60, 0 };
        public int TipOffLimitCount = 5;

        public void IncTipOff()
        {
            var now = DateTimeExtension.NowTimeStamp / 60;
            if (now == TipOffLimit[0])
            {
                TipOffLimit[1]++;
            }
            else
            {
                TipOffLimit[0] = now;
                TipOffLimit[1] = 0;
            }
        }

        public bool HitTipOffLimit()
        {
            var now = DateTimeExtension.NowTimeStamp / 60;
            return now == TipOffLimit[0] && TipOffLimit[1] > TipOffLimitCount;
        }

        public bool BadgeNeedFresh = true;

        /// <summary>
        /// 这个是渠道统计的标签，雷霆 SDK 用。
        /// </summary>
        [TableColumn]
        public string Media { get; set; }

        //暂存家具升级数据
        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonDictionaryFormat<long, int>))]
        public Dictionary<long, int> LvUpSceneObjectLvRecord { set; get; } = new Dictionary<long, int>();

        public class CharactoerInfoExtend
        {
            public class RoomInfo
            {
                public string id { get; set; }

                public string name { get; set; }

                public string statusDesc { get; set; }
            }

            public List<RoomInfo> sceneList { get; set; }

            public override string ToString()
            {
                return Newtonsoft.Json.JsonConvert.SerializeObject(this);
            }
        }

        private List<PicaRoomSceneSpec> myRoomSpecs;
        public List<PicaRoomSceneSpec> MyRoomSpecs
        {
            get
            {
                if (myRoomSpecs == null)
                {
                    myRoomSpecs = GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(r => r.OwnerID == ID && !r.Deleted).ToList();
                }

                return myRoomSpecs;
            }
        }
        private List<PicaGardenSceneSpec> myGardenSpecs;
        public List<PicaGardenSceneSpec> MyGardenSpecs
        {
            get
            {
                if (myGardenSpecs == null)
                {
                    myGardenSpecs = GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(r => r.OwnerID == ID && !r.Deleted).ToList();
                }

                return myGardenSpecs;
            }
        }

        public void RefreshRoomSpecs()
        {
            myRoomSpecs = GameApplication.SceneManager.FindAllSpec<PicaRoomSceneSpec>(r => r.OwnerID == ID && !r.Deleted).ToList();
            myGardenSpecs = GameApplication.SceneManager.FindAllSpec<PicaGardenSceneSpec>(r => r.OwnerID == ID && !r.Deleted).ToList();
        }

        public void OnTurnoverChanged(int value)
        {
            TurnoverCulPoint += value;
            var times = TurnoverCulPoint / 2000;
            if (times >= 1)
            {
                TurnoverCulPoint = TurnoverCulPoint % 2000;
                TurnoverPoint += times;
                CheckUpdateAction(this, UserAction.更新交易分数, value: TurnoverPoint, mode: MissionValueTriggerCategory.Assignment);
                GameApplication.RankingManager.Trigger(this, CharacterRankingAction.职业好评分更新);
            }
            Save();
        }

        public int GetGameSeasonLevel()
        {
            var act = GameApplication.ActivityManager.FirstActivity<GameSeasonActivity>();
            if (act == null)
            {
                return 0;
            }

            var record = act.GetCharacterRecord(this, true);
            if (record == null)
            {
                return 1;
            }

            return record.Level;
        }

        public JsonObject CharacterInfo
        {
            get
            {
                var json = new JsonObject();
                json["zoneId"] = GameApplication.Service.Config.ID;
                json["account"] = ThirdPartyUserId;
                json["name"] = Name;
                json["rid"] = ID.ToString();
                json["level"] = Lv.ToString();
                json["charge"] = PayMoney / 100;
                json["vip"] = CrownLevel > 0;
                json["online"] = IsOnline;
                json["block"] = StopLoginDate > DateTimeExtension.Now;
                json["unBlockTime"] = StopLoginDate.ToString("yyyy-MM-dd HH:mm:ss");
                json["silence"] = StopSpeakDate > DateTimeExtension.Now;
                json["unSilenceTime"] = StopSpeakDate.ToString("yyyy-MM-dd HH:mm:ss");
                json["ID"] = ID;
                json["Gender"] = Gender.ToString();
                json["MemberLv"] = MemberLv;
                json["AccountName"] = AccountName;
                json["AdminType"] = AdminType.ToString();
                json["PayMoney"] = PayMoney;
                json["PayTimes"] = PayTimes;
                //json["GameIdentity"] = GameIdentity.ToString();
                //json["StopLoginDate"] = StopLoginDate.ToString("yyyy-MM-dd HH:mm:ss");
                //json["StopSpeakDate"] = StopSpeakDate.ToString("yyyy-MM-dd HH:mm:ss");
                json["unFakeSilenceTime"] = StopSpeakButSendSelfDate.ToString("yyyy-MM-dd HH:mm:ss");
                json["fakeSilence"] = StopSpeakButSendSelfDate > DateTimeExtension.Now;
                json["lastLoginTime"] = ThisloginTime.ToString("yyyy-MM-dd HH:mm:ss");
                json["registerTime"] = CreateDate.ToString("yyyy-MM-dd HH:mm:ss");
                json["channelNo"] = /*User.Account.ThirdPartyInfo.ChannelId ?? "";*/ChannelID;
                json["extend"] = User.Account.ThirdPartyInfo.ChannelId ?? "";


                var extend = new JsonObject();
                var room_list = new JsonArray();
                foreach (var scene_spec in this.Scenes)
                {
                    var roomObj = GameApplication.DataManager.FindOrLoadGameObject<Scene, PicaRoomScene>(scene_spec.ID);
                    if (roomObj == null)
                    {
                        roomObj = GameApplication.DataManager.FindOrLoadGameObject<Scene, PicaGardenScene>(scene_spec.ID);
                    }
                    if (roomObj != null)
                    {
                        var room_json = new JsonObject();
                        room_json["name"] = string.IsNullOrWhiteSpace(roomObj.Name) ? Name + "的小屋" : roomObj.Name;
                        room_json["id"] = roomObj.ID.ToString();
                        room_json["statusDesc"] = roomObj.Frozen ? "禁止访问" : "正常访问";
                        room_list.Add(room_json);
                    }
                }
                extend["sceneList"] = room_list;
                json["extend"] = extend;
                return json;
            }

        }

        public LeiTingLogProperties GetLeiTingLogProperties()
        {
            var res = new LeiTingLogProperties();
            var info = User.Account.ThirdPartyInfo;
            res.Add("server_id", BirthService);
            res.Add("server_name", GameApplication.Service.GetBirthServiceName(BirthService));
            res.Add("new_server_id", GameApplication.Service.Config.ID);
            res.Add("new_server_name", GameApplication.Service.Config.Name);
            res.Add("channel", info.ChannelId);
            //res.Add("imei", info.TALogIMEI);
            //res.Add("termin_info", info.TALogTerminInfo);
            //res.Add("os_version", info.TALogOSVer);
            res.Add("account", User.Account.Name);
            res.Add("level", Lv);
            res.Add("role_name", Name);
            //res.Add("ver", info.ClientVer);
            res.Add("media", info.Media);
            //res.Add("hunger", GetProperty(1001).Value);
            //res.Add("strength", GetProperty(1000).Value);
            //res.Add("job", CurrentProfessionId == 0 ? "无" : I18nManager.ProfessionName(CurrentProfessionId));
            res.Add("coin", GetVirtualCurrency(1, true).Value);
            res.Add("diamond", GetVirtualCurrency(2, true).Value);
            res.Add("total_charge", TaPayMoneyRound);
            res.Add("gold_diamond", GetVirtualCurrency(100, true).Value);
            res.Add("unique_device_id", info.TALogAndroidId);
            res.Add("os_version", info.LoginOSVer);
            //res.Add("device_id", new LeitingLogDeviceInfo(info.TALogAndroidId, info.LoginOSVer));
            return res;
        }

        private void WriteLeiTingEventProperties(LeiTingEventWriter writer)
        {
            var info = User.Account.ThirdPartyInfo;
            writer.WriteEntry("server_id", BirthService)
                .WriteEntry("server_name", GameApplication.Service.GetBirthServiceName(BirthService))
                .WriteEntry("new_server_id", GameApplication.Service.Config.ID)
                .WriteEntry("new_server_name", GameApplication.Service.Config.Name)
                //.WriteEntry("imei", info.TALogIMEI)
                //.WriteEntry("termin_info", info.TALogTerminInfo)
                //.WriteEntry("os_version", info.TALogOSVer)
                .WriteEntry("account", this.AccountName)
                .WriteEntry("level", Lv)
                .WriteEntry("role_name", Name)
                .WriteEntry("ver", info.ClientVer)
                //.WriteEntry("job", CurrentProfessionId == 0 ? "无" : I18nManager.ProfessionName(CurrentProfessionId))
                .WriteEntry("coin", GetVirtualCurrency(1, true).Value)
                .WriteEntry("diamond", GetVirtualCurrency(2, true).Value)
                .WriteEntry("total_charge", TaPayMoneyRound)
                .WriteEntry("total_voucher", PayVoucher / 100)
                .WriteEntry("member_type", CrownLevel);
                //.WriteEntry("expiration_time", CrownLeftDay)
            //.WriteObject("device_id")
            //    .WriteEntry("androidId", info.TALogAndroidId)
            //    .WriteEntry("osVersion", info.LoginOSVer)
            //    .WriteEntry("iMEI", info.TALogIMEI)
            //    .WriteEntry("mac", info.TALogMac)
            //    .WriteEntry("oaId", info.TALogOaId)
            //    .WriteEntry("idfa", info.TALogIdfa)
            if (IsOnline)
            {
                writer
                //.WriteEntry("hunger", GetProperty(1001).Value)
                //.WriteEntry("strength", GetProperty(1000).Value)
                .WriteStringEntry("current_scene_id", SceneCharacter == null ? 0 : (SceneCharacter.CurrentScene == null ? 0 : SceneCharacter.CurrentScene.ID));
            }
            if (User.Account.ThirdPartyInfo.Inited)
            {
                writer
                .WriteEntry("media", info.Media)
                .WriteEntry("channel", info.ChannelId);
            }
        }

        public LeiTingEventWriter GetLeiTingTrackWriter(string eventname)
        {
            var writer = LeiTingEventWriter.Track(ID, Name, User.Account?.Data?.DistinctId, ThisLoginIP, eventname)
                .WriteProperties();
            WriteLeiTingEventProperties(writer);
            return writer;
        }

        public LeiTingEventWriter GetLeiTingUserSetWriter()
        {
            var writer = LeiTingEventWriter.User(ID, User?.Account?.Data?.DistinctId, LeiTingUserEventType.Set)
                .WriteProperties();
            WriteLeiTingEventProperties(writer);
            return writer;
        }

        public LeiTingEventWriter GetLeiTingUserSetOnceWriter()
        {
            var writer = LeiTingEventWriter.User(ID, User?.Account?.Data?.DistinctId, LeiTingUserEventType.SetOnce)
                .WriteProperties();
            WriteLeiTingEventProperties(writer);
            return writer;
        }

        public void ReportWorkToTa(bool start, ProfessionType pro_conf, bool inGuide)
        {
            Logger.Supplement.Write($"{ID} 打工, 开始: {start}, prof_id {pro_conf.ID}, prof_name: {I18nManager.ProfessionName(pro_conf.ID)}, " +
                $"lv: {GetProfession(pro_conf.ID).Lv}, in guide: {inGuide}, play_times: {TodayWorkTime}");

            //if (Constants.AllowTaLog)
            //{
            //    GetLeiTingTrackWriter(start ? "start_work" : "end_work")
            //    .WriteEntry("profession_id", pro_conf.ID)
            //    .WriteEntry("profession_name", I18nManager.ProfessionName(pro_conf.ID))
            //    .WriteEntry("profession_level", GetProfession(pro_conf.ID).Lv)
            //    .WriteRawEntry("work_type", inGuide ? "新手培训" : "职业打工")
            //    .WriteEntry("play_times", TodayWorkTime).WriteToTaLogger();
            //}
        }

        public void ReportActorComponentToTa(string title, ActorComponentType conf, string action = "更改设置")
        {

            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("actor_or_room_component")
                    .WriteEntry("component_id", conf.ID)
                    .WriteEntry("component_name", conf.Name)
                    .WriteEntry("action", action)
                    .WriteEntry("action_type", title + "玩家外观").WriteToTaLogger();
            }
        }

        public void ReportRoomComponentToTa(string title, RoomComponentsType conf, string action = "更改设置")
        {

            if (Constants.AllowTaLog)
            {
                GetLeiTingTrackWriter("actor_or_room_component")
                .WriteEntry("component_id", conf.ID)
                .WriteEntry("component_name", conf.Name)
                .WriteEntry("action", action)
                .WriteEntry("action_type", title + "房间外观").WriteToTaLogger();
            }
        }

        public void ReportEditRoomToTa(JsonObject placeJson, JsonObject backBagJson, bool isOrganiztionScene = false)
        {
            string event_name = isOrganiztionScene ? "edit_organization_scene" : "edit_room";
            if (Constants.AllowTaLog)
            {
                //GetLeiTingTrackWriter(event_name)
                //    .WriteStringEntry("room_id", SceneCharacter.CurrentSceneID)
                //    .WriteStringEntry("scene_id", SceneCharacter.CurrentScene.Type)
                //    .WriteEntry("room_level", ((PicaRoomScene)(SceneCharacter.CurrentScene)).RoomLv)
                //    .WriteEntry("put_item_data", placeJson.ToString())
                //    .WriteEntry("back_item_data", backBagJson.ToString())
                //    .WriteToTaLogger();
                var ta_data_to_write = $"room_id {SceneCharacter.CurrentSceneID} scene_id {SceneCharacter.CurrentScene.Type} put_item_data {placeJson.ToString()} back_item_data {backBagJson.ToString()}";
                Logger.Supplement.Write($"{ID} {Name} {event_name} data {ta_data_to_write}");
            }
        }

        public void ReportMineToTaByAwardRes(AwardResult reward)
        {
            var mineScene = SceneCharacter.CurrentScene;
            //if (reward.Count == 0)
            //{
            //    if (Constants.AllowTaLog)
            //    {
            //        GetLeiTingTrackWriter("pick_mine")
            //            .WriteRawEntry("mine_name", "沙子")
            //            .WriteEntry("scene_name", mineScene.Name)
            //            .WriteEntry("treasure", "0")
            //            .WriteEntry("mine_cave_type", "normal")
            //            .WriteToTaLogger();
            //    }
            //}
            if (reward.Count > 0)
            {
                var mine_items = "";
                var need_rec = false;
                foreach (var item in reward)
                {
                    mine_items += I18nManager.ItemName(item.Type).Replace("-", "") + "*" + item.Value + ",";
                    if (!need_rec && GameApplication.DataManager.FindValue<EntityType, ItemType>(item.Type)?.Quality >= Quality.五星)
                    {
                        need_rec = true;
                    }
                }
                mine_items.Remove(mine_items.Length - 1);
                Logger.Supplement.Write($"{ID} pick_mine data {mine_items}");
                if (Constants.AllowTaLog && need_rec)
                {
                    //GetLeiTingTrackWriter("pick_mine")
                    //    .WriteEntry("mine_name", mine_items)
                    //    .WriteEntry("scene_name", mineScene.Name)
                    //    .WriteEntry("treasure", "0")
                    //    .WriteEntry("mine_cave_type", "normal")
                    //    .WriteToTaLogger();
                }
            }
        }

        /// <summary>
        /// 只有在角色创建的时候初始化，避免加载服务器
        /// </summary>
        public void CreateDefaultDataLists()
        {
            entityContainer = CreateDefaultEntityContainer(true);

            properties = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, ActorProperty>();

            buffs = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, CharacterBuff>();
            badges = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, Badge>();

            _actorComponents = new ConcurrentDictionary<long, ActorComponent>();
            _professions = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, Profession>();

            CreateDefaultAttachData();

            CreateDefaultListData();

            _gallery = CreateDefaultGallery();

            picaMineData = CreatePicaMineData();

            _leaflets = new List<Leaflet>();

            CreateDefaultPets();

            npcShopItemRecordDic = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, NpcShopItemData>();

            _relationAgent = CreateDefaultRelationAgent();

            _roomData = CreateDefaultCharacterRoom();

            _npcData = CreateDefaultNpcData();

            episodeDatas = new HelpBase.Collections.Concurrent.ConvenientDictionary<long, EpisodeData>();

            mails = new OwnedObjectList<Mail>(null, this, new Dictionary<long, Mail>());

            _customizedProperty = CreateDefaultCustomizedProperties();

            _noviceStatus = CreateDefaultNoviceStatus();

            PrivateChatDataDic = new Dictionary<long, long>();


            CreateDefaultActionTime();
            CreateDefaultExternalAction();
        }

        public string GetXmlTag()
        {
            return $"<color=#5C9191><link=playerName{ID}>{Name}</link></color>";
        }

        public Dictionary<long, Queue<KeyValuePair<string, DateTime>>> RoomOperationRecord = new Dictionary<long, Queue<KeyValuePair<string, DateTime>>>();

        public void RecordRoomOperation(RecordOperation operation, Scene scene, params string[] otherDatas)
        {
            if (scene.Dimension.Type != DimensionType.PicaRoom) return;
            string i18n_str = "";
            if (otherDatas == null)
            {
                i18n_str = I18nManager.I18n("PKT_RHM" + ((int)operation).ToString("0000"));
            }
            else
            {
                i18n_str = I18nManager.I18n("PKT_RHM" + ((int)operation).ToString("0000"), otherDatas);
            }
            var room = scene as PicaRoomScene;

            if (!room.Owner.RoomOperationRecord.TryGetValue(room.ID, out var valuePairs))
            {
                room.Owner.RoomOperationRecord[room.ID] = new Queue<KeyValuePair<string, DateTime>>();
                valuePairs = room.Owner.RoomOperationRecord[room.ID];
            }

            valuePairs.Enqueue(new KeyValuePair<string, DateTime>(i18n_str, DateTimeExtension.Now));
            if (valuePairs.Count > Constants.RecordLimit)
            {
                valuePairs.Dequeue();
            }
        }

        public void RecordBondIntimacyOperation(RecordOperation operation, long targetID, params string[] otherDatas)
        {
            if (Bonds.TryGetValue(targetID, out var bond) && bond.Group == BondGroup.Married)
            {
                AddOperationRecord(bond, operation, otherDatas);
                if (bond.Target.Bonds.TryGetValue(ID, out bond))
                {
                    AddOperationRecord(bond, operation, otherDatas);
                }
            }

            static void AddOperationRecord(Bond bond, RecordOperation operation, string[] arguments)
            {
                var record = new BondOperationRecord();
                record.Time = DateTimeExtension.Now;
                record.Content = arguments == null
                    ? I18nManager.I18n("PKT_SimpMessage" + ((int)operation).ToString("0000"))
                    : I18nManager.I18n("PKT_SimpMessage" + ((int)operation).ToString("0000"), arguments);

                bond.OperationRecords ??= new ConcurrentStack<BondOperationRecord>();
                bond.OperationRecords.Push(record);
                if (bond.OperationRecords.Count > Constants.RecordLimit)
                {
                    bond.OperationRecords.TryPop(out _);
                }
            }
        }


        private AnniversaryRecordData _anniversaryData;

        private bool _anniversaryDataLoaded;
        public AnniversaryRecordData AnniversaryData
        {
            get
            {
                if (_anniversaryData == null && !_anniversaryDataLoaded)
                {
                    _anniversaryData = GameApplication.DataManager.FindOrLoadGameObject<AnniversaryRecordData>(ID);
                    if (_anniversaryData != null && _anniversaryData.CreateDate == 0) CreateAnniversaryData(_anniversaryData, true);//添加内存中的数据
                    _anniversaryDataLoaded = true;
                }
                return _anniversaryData;
            }
        }

        public void CreateAnniversaryData(AnniversaryRecordData data, bool doSave)
        {
            if (data == null)
            {
                data = RowAdapter.Create<AnniversaryRecordData>();
            }
            data.OwnerID = ID;
            data.Owner = this;
            data.CreateSuccess();
            data.Initialize();
            data.DoInit();
            if (doSave) data.Save();
            _anniversaryData = data;
        }
    }

    public enum RecordOperation
    {
        EatFood = 1,
        Clean,
        InvestigateFurni,
        UseItem,
        Praise,
        HelloToAddIntimacy = 9,
        DonateItem = 10,
        EatFriendTable,
        ReducePopularity,
        RoomNotice,
        IncreasePopularity
    }
}