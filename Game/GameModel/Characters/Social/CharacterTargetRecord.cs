using System;
using DataBase;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID))]
    public class CharacterTargetRecord : AbstractEntity
    {
        /// <summary>
        /// 所属主人（<see cref="Character"/>）标识
        /// </summary>
        [TableColumn]
        public long OwnerID { get; set; }

        /// <summary>
        /// 所属目标（<see cref="Character"/>）标识
        /// </summary>
        [TableColumn]
        public long TargetID { get; set; }

        /// <summary>
        /// 最后一次每日更新的时间
        /// </summary>
        [TableColumn]
        public DateTime LastDailyUpdateTime { get; set; }

        /// <summary>
        /// 当日寄明信片的次数
        /// </summary>
        [TableColumn]
        public int TodaySendPostcardTimes { get; set; }

        /// <summary>
        /// 当日问候的次数
        /// </summary>
        [TableColumn]
        public int DayGreetTimes { get; set; }

        private void Goday()
        {
            TodaySendPostcardTimes = 0;
            DayGreetTimes = 0;
        }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            LastDailyUpdateTime = DateTimeExtension.Now;
        }

        public void CheckExecuteGoday()
        {
            var now = DateTimeExtension.Now;
            if (LastDailyUpdateTime.Date != now.Date)
            {
                LastDailyUpdateTime = now;
                Goday();

                Save();
            }
        }
    }
}
