using DataBase;
using ExcelDataReader.Log;
using GameModel.Dimensions;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Collections.Concurrent;
using HelpBase.Json;
using HelpBase.Linq;
using Proto.GameModel;
using System;
using System.Linq;
using System.Threading;

namespace GameModel
{
    public partial class PicaPet : AbstractEntity
    {
        private ConvenientDictionary<long, PetProperty> properties;

        public ConvenientDictionary<long, PetProperty> Properties
        {
            get
            {
                return properties ?? LazyInitializer.EnsureInitialized(ref properties, () =>
                {
                    var dictionary = GameApplication.DataManager.LoadGameObjects<PetProperty>(p => p.OwnerID == ID, cache: false);
                    dictionary.Values.ForEach(p => p.Owner = this);
                    return new ConvenientDictionary<long, PetProperty>(dictionary.Values.ToDictionary(p => p.Type));
                });
            }
        }

        /// <summary>
        /// 获取指定类型属性
        /// </summary>
        public PetProperty GetPetProperty(long type, bool createIfNull = true)
        {
            if (!Properties.TryGetValue(type, out PetProperty property) && createIfNull)
            {
                var propertyType = GameApplication.DataManager.FindValue<PetPropertyType>(type);
                if (propertyType == null)
                {
                    throw new InvalidOperationException($"{nameof(PetPropertyType)}.{{ID:{type}}} not found.");
                }
                return GetPetProperty(propertyType, createIfNull);
            }
            return property;
        }

        public PetProperty GetPetProperty(PetPropertyType propertyType, bool createIfNull = true)
        {
            if (!Properties.TryGetValue(propertyType.ID, out PetProperty property) && createIfNull)
            {
                property = RowAdapter.Create<PetProperty>();
                property.OwnerID = ID;
                property.Owner = this;
                property.Type = propertyType.ID;

                property.Template = propertyType;
                property.CreateSuccess();
                property.Initialize();
                property.Save();

                Properties[property.Type] = property;
            }
            return property;
        }

        public void SetProperty(long id, int value, string action, string param = "", long paramID = 0, bool logTA = true)
        {
            var property = GetPetProperty(id);
            var old_value = property.Value;
            property.Value = value;
            var new_value = value;
            if (Constants.AllowTaLog && logTA)
            {
                //Owner.GetLeiTingTrackWriter("pet_set_property")
                //    .WriteStringEntry("pet_id", ID)
                //    .WriteEntry("pet_type", TypeID)
                //    .WriteEntry("pet_name", Name)
                //    .WriteEntry("property_type", property.Type)
                //    .WriteEntry("property_name", I18nManager.PetPropertyName(property.Type))
                //    .WriteEntry("num_before", old_value)
                //    .WriteEntry("num_after", new_value)
                //    .WriteEntry("num", value)
                //    .WriteEntry("action", action)
                //    .WriteToTaLogger();

                var ta_data_to_write = $"pet_id {ID} pet_type {TypeID} property_type {property.Type} {old_value} -> {new_value} changed {value} on {action}";
                Logger.Supplement.Write($"{ID} pet_set_property data {ta_data_to_write}");
            }
            property.Save();
        }

        /// <summary>
        /// 增加指定类型的属性值
        /// </summary>
        /// <param name="id"></param>
        /// <param name="value"></param>
        public void IncreaseProperty(long id, int value, string action, string param = "", long paramID = 0, bool logTA = true)
        {
            var property = GetPetProperty(id);
            if (Owner != null) // 创建宠物的时候没有 Owner
            {
                Owner.CheckUpdateAction(Owner, UserAction.宠物属性门槛, id, value: property.Value, mode: MissionValueTriggerCategory.Assignment);
            }
            IncreaseProperty(property, value, action, logTA: logTA);
        }
        /// <summary>
        /// 增加指定类型的属性值
        /// </summary>
        /// <param name="property"></param>
        /// <param name="value"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public void IncreaseProperty(PetProperty property, int value, string action, string param = "", long paramID = 0, bool logTA = true)
        {
            if (property == null)
                throw new ArgumentNullException(nameof(property));

            var old_value = property.Value;
            property.Value += value;
            if (property.Value > property.MaxValue) property.Value = property.MaxValue;
            var new_value = property.Value;

            if (Constants.AllowTaLog && logTA)
            {
                //Owner.GetLeiTingTrackWriter("pet_add_property")
                //    .WriteStringEntry("pet_id", ID)
                //    .WriteEntry("pet_type", TypeID)
                //    .WriteEntry("pet_name", Name)
                //    .WriteEntry("property_type", property.Type)
                //    .WriteEntry("property_name", I18nManager.PetPropertyName(property.Type))
                //    .WriteEntry("num_before", old_value)
                //    .WriteEntry("num_after", new_value)
                //    .WriteEntry("num", value)
                //    .WriteEntry("action", action)
                //    .WriteToTaLogger();
                var ta_data_to_write = $"pet_id {ID} pet_type {TypeID} pet_name {Name} property_type {property.Type} property_name {I18nManager.PetPropertyName(property.Type)} {old_value} -> {new_value} changed {value} on {action}";
                Logger.Supplement.Write($"{Owner.ID} pet_add_property data {ta_data_to_write}");
            }
            property.Save();
        }
        /// <summary>
        /// 减少指定类型的属性值
        /// </summary>
        /// <param name="id"></param>
        /// <param name="value"></param>
        public void DecreaseProperty(long id, int value, string action, string param = "", long paramID = 0, bool logTA = true)
        {
            var property = GetPetProperty(id);
            DecreaseProperty(property, value, action, logTA: logTA);
        }
        /// <summary>
        /// 减少指定类型的属性值
        /// </summary>
        /// <param name="property"></param>
        /// <param name="value"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public void DecreaseProperty(PetProperty property, int value, string action, string param = "", long paramID = 0, bool logTA = true)
        {
            if (property == null)
                throw new ArgumentNullException(nameof(property));

            var old_value = property.Value;
            property.Value -= value;
            var new_value = property.Value;
            if (property.Value < 0) property.Value = 0;

            //if (Constants.AllowTaLog && logTA)
            //{
            //    Owner.GetLeiTingTrackWriter("pet_cost_property")
            //        .WriteStringEntry("pet_id", ID)
            //        .WriteEntry("pet_type", TypeID)
            //        .WriteEntry("pet_name", Name)
            //        .WriteEntry("property_type", property.Type)
            //        .WriteEntry("property_name", I18nManager.PetPropertyName(property.Type))
            //        .WriteEntry("num_before", old_value)
            //        .WriteEntry("num_after", new_value)
            //        .WriteEntry("num", value)
            //        .WriteEntry("action", action)
            //        .WriteToTaLogger();
            //}
            property.Save();
        }
        /// <summary>
        /// 检测指定属性是否足够消费
        /// </summary>
        public bool CheckPropertyCountEnough(PetProperty property, int value, CompareType compareType)
        {
            if (property == null)
                throw new ArgumentNullException(nameof(property));

            return property.Value.Compare(value, compareType);
        }

        /// <summary>
        /// 检测指定属性是否足够消费
        /// </summary>
        public bool CheckPropertyCountEnough(long type, int value, CompareType compareType)
        {
            var property = GetPetProperty(type);
            return CheckPropertyCountEnough(property, value, compareType);
        }

    }
}
