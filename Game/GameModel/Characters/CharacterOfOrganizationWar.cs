using DataBase;
using Proto.GameModel;

namespace GameModel
{
    public partial class Character
    {
        private OrganizationWarRecord _organizationWarRecord;
        public OrganizationWarRecord OrganizationWarRecord
        {
            get
            {
                if (_organizationWarRecord == null)
                {
                    _organizationWarRecord = GameApplication.DataManager.FindOrLoadGameObject<OrganizationWarRecord>(ID);
                    if (_organizationWarRecord == null)
                    {
                        _organizationWarRecord = RowAdapter.Create<OrganizationWarRecord, OrganizationWarRecord>(ID);
                    }
                    _organizationWarRecord.OwnerId = ID;
                    _organizationWarRecord.Owner = this;
                }
                return _organizationWarRecord;
            }
        }

        public void UpdateOrganizationWarActorData()
        {
            var data = GetOrganizationWarData();
            if (data == null) return;
            SendGoogleProto(ClientMethod.UpdateOrganizationWarActorData, data.ProtoData);
        }

        public void UpdateOrganizationWarSceneActorData()
        {
            var data = GetOrganizationWarData();
            if (data == null) return;
            SceneCharacter.Scene.Broadcast(ClientMethod.UpdateOrganizationWarSceneActorData, data.SceneActorData);
        }

        public OrganizationWarData GetOrganizationWarData()
        {
            if (OrganizationAgent.OrganizationID == 0) return null;
            var organization = OrganizationAgent.Organization;
            return organization.GetOrganizationWarData(this);
        }
    }
}