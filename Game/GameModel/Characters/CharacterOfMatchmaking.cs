using Proto.GameModel;

namespace GameModel
{
    public partial class Character
    {
        public DateTime NextMatchTime { get; set; }
        public MatchmakingType CurrentMatchmakingType { get; set; }

        public void SetCurrentMatchmakingType(MatchmakingType type)
        {
            CurrentMatchmakingType = type;
            UpdateToClient(c => c.CurrentMatchmakingType = CurrentMatchmakingType);
        }
    }
}
