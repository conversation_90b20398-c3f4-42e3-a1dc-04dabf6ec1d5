<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>     
    <Nullable>disable</Nullable>
    <LangVersion>latestMajor</LangVersion>
    <EmitCompilerGeneratedFiles>true</EmitCompilerGeneratedFiles>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CsvHelper" Version="27.2.1" />
    <PackageReference Include="Dynamitey" Version="**********" />
    <PackageReference Include="ExcelDataReader" Version="3.6.0" />
    <PackageReference Include="JWT" Version="8.4.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Net.Compilers.Toolset" Version="4.6.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.111" />
    <PackageReference Include="System.Json" Version="4.7.1" />
  </ItemGroup>

  <ItemGroup>
    
    <Reference Include="System.Web" Condition="'$(TargetFramework)' == 'net48'" />
    <Reference Include="System.ServiceModel.Web" Condition="'$(TargetFramework)' == 'net48'" />
    <Reference Include="System.ServiceModel" Condition="'$(TargetFramework)' == 'net48'" />
    <Reference Include="System.IdentityModel" Condition="'$(TargetFramework)' == 'net48'" />
    <Reference Include="PresentationFramework" Condition="'$(TargetFramework)' == 'net48'" />

  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="..\..\Framework\ServiceProto\Service\Plat\AccountService.proto" GrpcServices="Client" Link="Proto\AccountService.proto" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\..\Framework\AppLibrary\AppLibrary.csproj" />
    <ProjectReference Include="..\..\Framework\DataBase\DataBase.csproj" />
    <ProjectReference Include="..\..\Framework\IronRabbit\IronRabbit.csproj" />
    <ProjectReference Include="..\..\Generator\EntityGenerator\EntityGenerator.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="True" />
    <ProjectReference Include="..\..\Framework\GameCapsule\GameCapsule\GameCapsule.csproj" />
    <ProjectReference Include="..\..\Framework\GameServiceBase\GameServiceBase.csproj" />

    <ProjectReference Include="..\GameFramework.Application\GameFramework.Application.csproj" />
    <ProjectReference Include="..\GameFramework\GameFramework.csproj" />
    <ProjectReference Include="..\GameModelBase\GameModelBase.csproj" />
    <ProjectReference Include="..\ProtoData\ProtoData.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="System.Diagnostics.CodeAnalysis" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Activities\Functions\ShakeJarRaffleActivity\" />
    <Folder Include="Activities\Functions\CrowdfundingActivity\" />
    <Folder Include="DataModel\PicaFunctions\SceneObjectFunctions\Toys\" />
    <Folder Include="Dimensions\Desert\Weather\" />
  </ItemGroup>

</Project>
