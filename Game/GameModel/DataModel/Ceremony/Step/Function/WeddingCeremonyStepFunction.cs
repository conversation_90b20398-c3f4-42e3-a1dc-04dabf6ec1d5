using Proto.GameModel;

namespace GameModel.Ceremony
{
    /// <summary>
    /// 表示仪式步骤举行婚礼功能的类
    /// </summary>
    public sealed class WeddingCeremonyStepFunction : InteractionCeremonyStepFunction
    {
        public override IProtoObject GetProtocol(Character character, CeremonyFlow flow, CeremonyStepType step)
        {
            var record = flow.GetFunctionRecord<WeddingCeremonyFlowFunctionRecord>(false);
            if (record == null || !record.HasWedding)
            {
                Logger.Error.Write("{0} character:{1} {2}:{3} record not found", nameof(WitnessInvitationCeremonyStepFunction), character.ID, nameof(CeremonyFlow), flow.ID);
                return null;
            }

            var wedding = record.Wedding;
            var data = new IDByteMessage();
            data.Value = (byte)wedding.Status;
            data.ID = data.Value == (byte)WeddingStatus.None
                ? DateTimeExtension.ToTimestamp(wedding.StartTime)
                : DateTimeExtension.ToTimestamp(wedding.StopTime);
            return data;
        }

        public override void Interact(Character character, CeremonyFlow flow, CeremonyStepType step, byte action, byte[] bytes)
        {
            var record = flow.GetFunctionRecord<WeddingCeremonyFlowFunctionRecord>(false);
            if (record == null || !record.HasWedding)
            {
                character.ShowTipsMessage(PicaLocalityStrings.limitedTargetStatus);
                return;
            }
            var scene = record.Wedding.GetScene();
            if (scene == null)
            {
                character.ShowTipsMessage(PicaLocalityStrings.weddingFinished);
                return;
            }
            if (scene.CanEnter(character))
            {
                scene.Enter(character.SceneCharacter);
            }
        }
    }
}
