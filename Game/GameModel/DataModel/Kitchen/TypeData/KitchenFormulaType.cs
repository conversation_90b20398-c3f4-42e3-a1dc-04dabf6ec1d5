using DataBase;
using GameModel.Managers;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Type, CachePolicy.Full)]
    public class KitchenFormulaType : AbstractTypeEntity, ICacheSelf, IProtoObject<KitchenFormulaTypeData>
    {
        private string _name;

        public string Name
        {
            get
            {
                if (_name == null)
                {
                    _name = I18nManager.KitchenFormulaName(ID);
                }
                return _name;
            }
        }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> Formulas { get; set; }

        /// <summary>
        /// 前端显示的订单材料
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> DisplayedFormulas { get; set; }

        /// <summary>
        /// 操作方式
        /// </summary>
        [TableColumn]
        public long Operation { get; set; }

        /// <summary>
        /// 产物
        /// </summary>
        [TableColumn]
        public long Product { get; set; }

        /// <summary>
        /// 制作时间
        /// </summary>
        [TableColumn(DBType = "varchar(64)", FormatType = typeof(TimeSpanFormat))]
        public TimeSpan ProductionTime { get; set; }

        /// <summary>
        /// 订单周期
        /// </summary>
        [TableColumn(DBType = "varchar(64)", FormatType = typeof(TimeSpanFormat))]
        public TimeSpan OrderCycle { get; set; }

        private KitchenOperationType _operationTemplete;
        public KitchenOperationType OperationTemplete
        {
            get
            {
                if (_operationTemplete == null)
                {
                    _operationTemplete = GameApplication.DataManager.FindValue<KitchenOperationType>(Operation);
                }
                return _operationTemplete;
            }
        }

        public KitchenFormulaTypeData ProtoData
        {
            get
            {
                var data = new KitchenFormulaTypeData();
                data.Id = ID;
                data.Operation = Operation;
                data.Product = Product;
                data.ProductionTime = (float)ProductionTime.TotalSeconds;
                data.OrderCycle = (float)OrderCycle.TotalSeconds;
                if (Formulas != null && Formulas.Count > 0)
                {
                    data.Formulas.AddRange(Formulas);
                }
                if (DisplayedFormulas != null && DisplayedFormulas.Count > 0)
                { 
                    data.DisplayedFormulas.AddRange(DisplayedFormulas);
                }
                return data;
            }
        }


        public static KitchenFormulaType Default
        {
            get
            {
                var data = new KitchenFormulaType();
                data.ID = 0;
                data.Formulas = new List<long>();
                data.Operation = 0;
                data.Product = 1;
                data.ProductionTime = TimeSpan.FromSeconds(5);
                data.OrderCycle = TimeSpan.FromSeconds(10);
                return data;
            }
        }
    }

    public sealed class KitchenOrder : JsonFormatObject
    {
        [JsonMember]
        public long FormuleType { get; set; }

        /// <summary>
        /// 订单创建时间
        /// </summary>
        [JsonMember]
        public long GenerationTime { get; set; }

        /// <summary>
        /// 订单到期时间
        /// </summary>
        public DateTime ExpiredTime
        {
            get
            {
                return DateTimeExtension.ConvertFromTimestamp(GenerationTime).Add(Templete.OrderCycle);
            }
        }

        [JsonMember]
        public bool IsCompleted { get; set; }

        [JsonMember]
        public bool IsWrong { get; set; }

        [JsonMember]
        public bool IsExpired { get; set; }

        private KitchenFormulaType templete;
        public KitchenFormulaType Templete
        { 
            get
            {
                if (templete == null)
                {
                    templete = GameApplication.DataManager.FindValue<KitchenFormulaType>(FormuleType);
                }
                return templete;
            }
        }

        [JsonMember]
        public int MountIndex { get; set; }

        public SceneObject Npc { get; set; }
        
    }
}
