using GameModel.Dimensions.Kitchen;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 某个操作使用次数
    /// </summary>
    public class KitchenOperationUsageCountCondition : KitchenGameCondtionBase
    {
        [JsonMember]
        public long OperationId { get; set; }

        [JsonMember]
        public string OperatorValue { get; set; }

        public override bool Check(List<KitchenActionBase> KitchenActions)
        {
            var actions = KitchenActions.Where(p => p.KitchenAction == KitchenAction.使用操作台)
                .Select(p => (UseKitchenwareAction)p)
                .Where(p =>p.OperationId == OperationId);
            return EvaluateCondition(OperatorValue, actions.Count());
        }

    }
}
