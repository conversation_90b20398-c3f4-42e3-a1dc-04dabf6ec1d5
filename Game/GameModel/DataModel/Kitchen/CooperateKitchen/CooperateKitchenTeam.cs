using GameModel.Activities;
using GameModel.Dimensions;
using HelpBase;
using HelpBase.Collections.Concurrent;
using HelpBase.Linq;
using Proto.GameModel;

namespace GameModel.CooperateGame
{
    public class CooperateKitchenTeam : CooperateGameTeam<CooperateKitchenConfig>
    {
        static TimeSpan CloseTimeSpan = TimeSpan.FromSeconds(60);

        public override DateTime DeleteTime => base.DeleteTime.Add(CloseTimeSpan);

        public override bool AutoDestroy => false;

        protected override bool ArrowScoreNegative => false;

        public override bool Permitted
        {
            get
            {
                if (IsStarted())
                {
                    return base.Permitted;
                }
                else
                {
                    return true;
                }
            }
        }

        private CooperateKitchenScene Scene { get; set; }

        private DelayAwaiter GameDelayAwaiter;
        private DelayAwaiter ReadyDelayAwaiter;
        private TimeAction OrderTimer;

        public CooperateKitchenTeam(Character character, long cooperateGameType) : base(character, cooperateGameType)
        {
            var sceneType = GameApplication.DataManager.FindValue<SceneType>(Config.SceneType);
            var dimension = GameApplication.SceneManager.GetDimension(sceneType);
            var level = dimension.GetLevel(sceneType.ID);
            if (level == null)
            {
                Logger.Error.Write($"CooperateKitchenTeam GetScene dimensionLevel is null : {Config.SceneType}");
                return;
            }
            var scene = dimension.CreateScene(level, Character.System);
            if (scene == null)
            {
                Logger.Error.Write($"CooperateKitchenTeam CreateScene scene is null : Type: " + sceneType);
                return;
            }
            var rc = GetSceneRecord(scene);
            rc.OwnerCharacterID = OwnerID;
            rc.HelpType = Config.HelpType;
            Scene = scene as CooperateKitchenScene;
        }

        private CooperateKitchenSceneFunctionRecord GetSceneRecord(Scene scene)
        {
            if (scene == null) return null;
            var rc = scene.GetFunctionRecord<CooperateKitchenSceneFunctionRecord>(true);
            return rc;
        }

        public override void AddScore(int value)
        {
            base.AddScore(value);
            var rc = GetSceneRecord(Scene);
            rc.CurrentScore = CurrentScore;
            rc.UpdateTo();
        }

        public override void AddOvertime(TimeSpan time)
        {
            base.AddOvertime(time);
            var rc = GetSceneRecord(Scene);
            rc.EndTime = DeleteTime.ToTimestamp();
            rc.UpdateTo();
        }

        public override bool FirstEnter(Character character, long itemType)
        {
            if (!base.FirstEnter(character, itemType))
            {
                return false;
            }
            var rc = GetSceneRecord(Scene);
            rc.EndTime = DateTimeExtension.Now.Add(Config.ReadyDuration).ToTimestamp();
            rc.UpdateTo();
            Scene.Enter(character.SceneCharacter);

            ReadyDelayAwaiter = TimerManager.Delay(Config.ReadyDuration, OnGameStart);
            return true;
        }

        public override bool GetFinalRewards(Character character)
        {
            if (!base.GetFinalRewards(character)) return false;

            FindAllCharacter().ForEach(p =>
            {
                try
                {
                    var idintm = new MiniGameData();
                    idintm.MiniGameCode = MiniGameCodeType.None;
                    var acts = GameApplication.ActivityManager.FindActivity(p => p is CooperateKitchActivity && p.Owner.Phase == ActivityPhase.开启);
                    var act = acts.FirstOrDefault(p => ((CooperateKitchActivity)p).CooperateGameId == CooperateGameType);
                    foreach (var item in acts)
                    {
                        CooperateKitchActivity cka = item as CooperateKitchActivity;
                        if (cka.CooperateGameId == CooperateGameType)
                        {
                            idintm.MiniGameCode = cka.GameCode;
                            continue;
                        }
                    }
                    if (idintm.MiniGameCode != MiniGameCodeType.None)
                    {
                        idintm.Score = CurrentScore;
                        p.TriggerAction(UserAction2.完成小游戏, idintm);
                        p.CheckUpdateAction(p, UserAction.胡闹厨房分数门槛, CooperateGameType, 0, this.CurrentScore, mode: MissionValueTriggerCategory.Assignment);
                    }
                }
                catch (Exception e)
                {
                    Logger.Error.Write("CooperateKitchenTeam GetFinalRewards Error : " + e.Message + e.StackTrace);
                }
            });

            return true;
        }

        public override bool CanAddCollaborator(Character owner, Character target)
        {
            if (!base.CanAddCollaborator(owner, target))
            {
                return false;
            }
            if (IsStarted())
            {
                owner.ShowTipsMessage("已开始游戏，无法再邀请好友");
                return false;
            }

            return true;
        }

        public override bool TryAddCollaborator(Character owner, Character target)
        {
            if (!base.TryAddCollaborator(owner, target))
            {
                return false;
            }
            if (IsStarted())
            {
                target.ShowTipsMessage("已开始游戏，无法再进入");
                return false;
            }
            Scene.Enter(target.SceneCharacter);
            return true;
        }

        protected override bool IsLeaveCoorerateGame(SceneCharacter character)
        {
            return character.Scene != Scene;
        }

        protected override void OnLeaveTeam(SceneCharacter character)
        {
            base.OnLeaveTeam(character);
        }

        protected override void DoClose()
        {
            base.DoClose();
            Interlocked.Exchange(ref GameDelayAwaiter, null)?.Cancel();

            Scene.FindArea(0).Players.Values.ForEach(p =>
            {
                OnLeaveTeam(p);
                if (!p.Owner.IsOnline) return;
                p.Owner.GoHome();
            });
            Scene.Close();
        }

        public override bool TryReconnect(Character character)
        {
            if (!base.TryReconnect(character))
            {
                return false;
            }

            Scene.Enter(character.SceneCharacter);
            return true;
        }


        public void StartGame()
        {
            Interlocked.Exchange(ref ReadyDelayAwaiter, null)?.Cancel();

            OnGameStart();
        }

        private void OnGameStart()
        {
            var rc = GetSceneRecord(Scene);
            if (!rc.IsStarted)
            {
                CreateTime = DateTimeExtension.Now;
                rc.EndTime = DeleteTime.Subtract(CloseTimeSpan).ToTimestamp();
                rc.CurrentScore = CurrentScore;
                rc.IsStarted = true;
                rc.IsStarting = true;
                rc.UpdateTo();
                rc.IsStarting = false;

                GameDelayAwaiter = TimerManager.Delay(Templete.Duration, OnGameOver);
                OrderTimer = TimerManager.Run(TimeSpan.FromSeconds(1.1f), OnOrderTimerCheck);
                TryAddNewOrder();
            }
        }

        private void OnGameOver()
        {
            var rc = GetSceneRecord(Scene);
            if (rc.IsStarted)
            {
                Interlocked.Exchange(ref GameDelayAwaiter, null)?.Cancel();
                TimerManager.Release(ref OrderTimer);

                GetFinalRewards(null);
                rc.EndTime = DateTimeExtension.Now.Add(CloseTimeSpan).ToTimestamp();
                rc.IsOver = true;
                //rc.KitchenOrderDatas = new List<KitchenOrderData>();
                rc.UpdateTo();

                //活动排行榜界面
            }
        }

        public bool IsStarted()
        {
            var rc = GetSceneRecord(Scene);
            return rc.IsStarted;
        }

        #region 订单
        private TimeSpan OrderTimeSpan;
        private List<KitchenOrder> CompleteOrderList = new List<KitchenOrder>();
        private List<KitchenOrder> ExpiredOrderList = new List<KitchenOrder>();
        private List<KitchenOrder> CurrentOrderDir = new List<KitchenOrder>();

        private void OnOrderTimerCheck()
        {
            CheckOrderExpire();

            OrderTimeSpan = OrderTimeSpan.Add(TimeSpan.FromSeconds(1.3f));
            if (OrderTimeSpan >= Config.Requirement.RequirementDuration)
            {
                if (TryAddNewOrder())
                {
                    OrderTimeSpan = TimeSpan.Zero;
                }
            }
        }

        private bool TryAddNewOrder(long orderId = 0)
        {
            lock (CurrentOrderDir)
            {
                if (CurrentOrderDir.Count >= Config.Requirement.MaxRequirementNum) return false;
                if (CurrentOrderDir.Count + CompleteOrderList.Count + ExpiredOrderList.Count >= Config.Requirement.TotalRequirementNum)
                {
                    if (CurrentOrderDir.Count == 0)
                    {
                        OnGameOver();
                    }
                    return false;
                }

                var formula = Config.Requirement.FormulaTypes.GetRandomItem();
                //var expirationTime = DateTimeExtension.Now.Add(formula.LimitedTime).ToTimestamp();
                var data = new KitchenOrder()
                {
                    FormuleType = orderId == 0? formula.ID : orderId,
                    GenerationTime = DateTimeExtension.NowTimeStamp,
                };
                CurrentOrderDir.Add(data);

                var rc = GetSceneRecord(Scene);
                rc.SetOrderDatas(CurrentOrderDir, CompleteOrderList, ExpiredOrderList);
            }
            return true;
        }

        private void CheckOrderExpire()
        {
            lock (CurrentOrderDir)
            {
                foreach (var item in CurrentOrderDir)
                {
                    //var expiredTime = item.Key;
                    if (DateTimeExtension.Now > item.ExpiredTime)
                    {
                        CurrentOrderDir.Remove(item);
                        item.IsExpired = true;
                        ExpiredOrderList.Add(item);
                        var rc = GetSceneRecord(Scene);
                        rc.SetOrderDatas(CurrentOrderDir, CompleteOrderList, ExpiredOrderList);
                        return;
                    }
                }
            }
        }

        public int CheckCompleteOrder(long id)
        {
            lock (CurrentOrderDir)
            {
                KitchenOrder order = null;
                foreach (var item in CurrentOrderDir)
                {
                    var formuleTypeData = GameApplication.DataManager.FindValue<KitchenFormulaType>(item.FormuleType);
                    if (formuleTypeData.Product == id)
                    {
                        if (order == null || item.ExpiredTime < order.ExpiredTime)
                        {
                            order = item;
                        }
                    }
                }
                var score = 0;
                if (order != null)
                {
                    score += Config.KitchenScoreRule.RightScore;
                    var c = Config.Requirement.FormulaTypes.FirstOrDefault(p => p.ID == order.FormuleType);
                    if (c != null)
                    {
                        score += c.ExtraScore;
                    }
                    score += Math.Max(0, (order.ExpiredTime - DateTimeExtension.Now).Seconds) * Config.KitchenScoreRule.TimeSecondScore;
                    CurrentOrderDir.Remove(order);
                    order.IsCompleted = true;
                    CompleteOrderList.Add(order);
                    var rc = GetSceneRecord(Scene);
                    rc.SetOrderDatas(CurrentOrderDir, CompleteOrderList, ExpiredOrderList, false);

                    if (CurrentOrderDir.Count <= 0)
                    {
                        TryAddNewOrder();
                    }
                }
                else
                {
                    score += Config.KitchenScoreRule.WrongScore;
                }
                AddScore(score);
                return score;
            }
        }

        public bool TestAddOrder(long orderId)
        {
            return TryAddNewOrder(orderId);
        }
        #endregion
    }
}
