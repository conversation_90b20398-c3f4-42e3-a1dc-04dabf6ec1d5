using System.Collections.Generic;
using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// NPC 对话模板数据
    /// </summary>
    //[EntityTable(DbGroup.Type)]
    public class DialogueType : AbstractMemoryEntity, IProtoObject<DialogueTypeData>, ICacheSelf
    {
        /// <summary>
        /// Npc 标识
        /// </summary>
        [TableColumn]
        public long NpcType { get; set; }

        /// <summary>
        /// 段落集合
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> Paragraphs { get; set; }

        public DialogueTypeData ProtoData
        {
            get 
            {
                var data = new DialogueTypeData();
                data.ID = ID;
                data.NpcType = NpcType;
                if (Paragraphs?.Count > 0) data.Paragraphs.AddRange(Paragraphs);
                return data;
            }
        }
    }
}
