using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 植物模板
    /// </summary>
    [EntityTable(DbGroup.Type)]
    public class PlantType : TypeDataObject, ICacheSelf, IProtoObject<PlantTypeData>
    {
        /// <summary>
        /// 名称
        /// </summary>
        [TableColumn(DBType = "nvarchar(50)")]
        public string Name { get; set; }

        /// <summary>
        /// 描述
        /// </summary>
        [TableColumn(DBType = "nvarchar(max)")]
        public string Description { get; set; }

        /// <summary>
        /// 预制体名称
        /// </summary>
        [TableColumn(DBType = "nvarchar(200)")]
        public string Prefab { get; set; }

        /// <summary>
        /// 预制资源包
        /// </summary>
        [TableColumn(DBType = "nvarchar(500)")]
        public string PrefabBundle { get; set; }

        /// <summary>
        /// 采集的奖励集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<RandomItem, RandomGroup>))]
        public RandomGroup GatheringAwards { get; set; }

        /// <summary>
        /// 偷窃的奖励集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<RandomItem, RandomGroup>))]
        public RandomGroup PilferingAwards { get; set; }

        public PlantTypeData ProtoData
        {
            get 
            {
                var data = new PlantTypeData();
                data.ID = ID;
                data.Name = Name;
                data.Description = Description;
                data.Prefab = Prefab;
                data.PrefabBundle = PrefabBundle;
                return data;
            }
        }
    }
}
