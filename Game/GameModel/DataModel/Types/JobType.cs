using System;
using System.Collections.Generic;
using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 职位模板
    /// </summary>
    [EntityTable(DbGroup.Type)]
    public class JobType : AbstractTypeEntity, ICacheSelf, IProtoObject<JobTypeData>
    {
        /// <summary>
        /// 名称
        /// </summary>
        [TableColumn(DBType = "nvarchar(20)")]
        public string Name { get; set; }

        /// <summary>
        /// 图标资源包
        /// </summary>
        [TableColumn(DBType = "varchar(max)")]
        public string IconBundle { get; set; }

        /// <summary>
        /// 图标资源名
        /// </summary>
        [TableColumn(DBType = "varchar(max)")]
        public string Icon { get; set; }

        /// <summary>
        /// 图片资源包
        /// </summary>
        [TableColumn(DBType = "varchar(max)")]
        public string ImageBundle { get; set; }

        /// <summary>
        /// 图片资源名
        /// </summary>
        [TableColumn(DBType = "varchar(max)")]
        public string Image { get; set; }

        /// <summary>
        /// 最小建筑等级
        /// </summary>
        [TableColumn]
        public int MinBuildingLv { get; set; }

        /// <summary>
        /// 增加的营业值
        /// </summary>
        [TableColumn]
        public int AddOperation { get; set; }

        /// <summary>
        /// 需要达标的属性集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<TypeValue>))]
        public List<TypeValue> Properties { get; set; }

        /// <summary>
        /// 条件集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<ActorPropertyRangeCondition>))]
        public List<ActorPropertyRangeCondition> Conditions { get; set; }

        /// <summary>
        /// 奖励货币集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<VirtualCurrencyRange, RandomList<VirtualCurrencyRange>>))]
        public RandomList<VirtualCurrencyRange> VirtualCurrencies { get; set; }

        /// <summary>
        /// 最大工作次数
        /// </summary>
        [TableColumn]
        public int MaxWorkingTimes { get; set; }

        /// <summary>
        /// 工作恢复时长
        /// </summary>
        [TableColumn(DBType = "varchar(64)", FormatType = typeof(TimeSpanFormat))]
        public TimeSpan RecoveringInterval { get; set; }

        /// <summary>
        /// 工作恢复次数
        /// </summary>
        [TableColumn]
        public int RecoveringTimes { get; set; }

        /// <summary>
        /// 男性装扮集合
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> MaleEquipTypes { get; set; }

        /// <summary>
        /// 女性装扮集合
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> FemaleEquipTypes { get; set; }

        /// <summary>
        /// 动画名称
        /// </summary>
        [TableColumn(DBType = "varchar(max)")]
        public string Animation { get; set; }

        /// <summary>
        /// 协议数据
        /// </summary>
        public JobTypeData ProtoData
        {
            get 
            {
                var data = new JobTypeData();
                data.ID = ID;
                data.Name = Name;
                data.IconBundle = IconBundle;
                data.Icon = Icon;
                data.ImageBundle = ImageBundle;
                data.Image = Image;
                data.MinBuildingLv = MinBuildingLv;
                data.AddOperation = AddOperation;
                Properties?.ForEach(p => data.Properties.Add(p.ProtoData));
                Conditions?.ForEach(p => data.Conditions.Add(p.ProtoData));
                data.MaxWorkingTimes = MaxWorkingTimes;
                data.RecoveringIntervalTicks = RecoveringInterval.Ticks;
                data.RecoveringTimes = RecoveringTimes;
                VirtualCurrencies?.ForEach(p => data.VirtualCurrencies.Add(p.ProtoData));
                if (MaleEquipTypes?.Count > 0) data.MaleEquipTypes.AddRange(MaleEquipTypes);
                if (FemaleEquipTypes?.Count > 0) data.FemaleEquipTypes.AddRange(FemaleEquipTypes);
                data.Animation = Animation;
                return data;
            }
        }

        /// <summary>
        /// 属性
        /// </summary>
        public class ActorPropertyRangeCondition : JsonFormatObject
        {
            /// <summary>
            /// 属性标识
            /// </summary>
            [JsonMember]
            public long Type { get; set; }

            /// <summary>
            /// 属性值
            /// </summary>
            [JsonMember]
            public int Value { get; set; }

            /// <summary>
            /// 最小范围（包含）
            /// </summary>
            [JsonMember]
            public int Min { get; set; }

            /// <summary>
            /// 最大范围（包含）
            /// </summary>
            [JsonMember]
            public int Max { get; set; }

            public JobTypeData.ActorPropertyConditionData ProtoData
            {
                get 
                {
                    var data = new JobTypeData.ActorPropertyConditionData();
                    data.Type = Type;
                    data.Value = Value;
                    data.Min = Min;
                    data.Max = Max;
                    return data;
                }
            }

            public int GetValue()
            {
                return Value == 0 ? RandomEvent.Next(Min, Max + 1) : Value;
            }
        }

        /// <summary>
        /// 奖励货币
        /// </summary>
        public class VirtualCurrencyRange : JsonFormatObject, IProbabilityItem
        {
            /// <summary>
            /// 权重
            /// </summary>
            [JsonMember]
            public int Probability { get; set; }

            /// <summary>
            /// 最小值（包含）
            /// </summary>
            [JsonMember]
            public int Min { get; set; }

            /// <summary>
            /// 最大值（包含）
            /// </summary>
            [JsonMember]
            public int Max { get; set; }

            /// <summary>
            /// 是否暴击
            /// </summary>
            [JsonMember]
            public bool IsLucky { get; set; }

            public JobTypeData.VirtualCurrencyRangeData ProtoData
            {
                get 
                {
                    var data = new JobTypeData.VirtualCurrencyRangeData();
                    data.Probability = Probability;
                    data.Min = Min;
                    data.Max = Max;
                    data.IsLucky = IsLucky;
                    return data;
                }
            }
        }
    }
}
