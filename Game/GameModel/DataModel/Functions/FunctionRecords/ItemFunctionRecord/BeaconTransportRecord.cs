using System;
using HelpBase.Json;

namespace GameModel
{
    public class BeaconTransportRecord : ItemFunctionRecord
    {
        [JsonMember]
        public long AreaID { get; set; }

        [JsonMember]
        public int Posx { get; set; }

        [JsonMember]
        public int Posy { get; set; }

        [JsonMember(FormatType = typeof(DateTimeStampFormat))]
        public DateTime NextOperateTime { get; set; }
    }
}
