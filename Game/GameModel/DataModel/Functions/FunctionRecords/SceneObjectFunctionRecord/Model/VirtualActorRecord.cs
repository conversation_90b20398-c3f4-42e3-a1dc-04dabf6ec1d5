using System.Collections.Generic;
using HelpBase;
using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public partial class VirtualActorRecord : SceneObjectFunctionRecord
    {
        [JsonMember]
        public string Name { get; set; }

        [JsonMember]
        public long AvatarType { get; set; }

        [JsonMember]
        public string AnimationName { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> EquipTypes { get; set; }

        /// <summary>
        /// 交互时是否自动朝向交互对象
        /// </summary>
        [JsonMember]
        public bool IsNormalDialogRotatable { get; set; } = true;

        /// <summary>
        /// 任务交互时是否自动朝向交互对象
        /// </summary>
        [JsonMember]
        public bool IsMissionDialogRotatable { get; set; } = true;

        /// <summary>
        /// 名称
        /// </summary>
        [JsonMember]
        public bool IsNameEnabled { get; set; }

        /// <summary>
        /// 启用展示
        /// </summary>
        [JsonMember]
        public bool IsInformationEnabled { get; set; }

        /// <summary>
        /// 启用对话
        /// </summary>
        [JsonMember]
        public bool IsDialogEnabled { get; set; }

        /// <summary>
        /// 按钮名称
        /// </summary>
        [JsonMember]
        public string Button { get; set; }

        [JsonMember]
        public bool IsInitialized { get; set; }
    }
}
