using System;
using GameModel.Award;
using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class PickUpRecord : SceneObjectFunctionRecord
    {
        public PickUpRecord()
        {
            BirthTime = DateTimeExtension.Now;
        }

        [JsonMember(FormatType = typeof(DynamicDataFormat<AwardBase>))]
        public AwardBase Award { get; set; }

        [JsonMember(FormatType =typeof(UTCDateTimeFormat))]
public DateTime BirthTime { get; set; }

        [JsonMember]
        public string OwnerActorName { get; set; }

        /// <summary>
        /// 是否功能性奖励，如果是就直接使用
        /// </summary>
        [JsonMember]
        public bool IsFunctionAward { get; set; }

        /// <summary>
        /// 掉落者
        /// </summary>
        public string Producer { get; set; }

        public long ProducerID { get; set; }
    }
}