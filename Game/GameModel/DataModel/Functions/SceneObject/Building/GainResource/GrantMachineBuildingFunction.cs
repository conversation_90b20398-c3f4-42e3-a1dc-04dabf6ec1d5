using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DataBase;
using GameModel.Award;
using HelpBase;
using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class GrantMachineBuildingFunction : UseableSceneObjectPropFunction, INeedRecordFunction<GrantMachineBuildingFunctionRecord>
    {
        [JsonMember]
        public int MaxTimes { get; set; }

        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan Cooling { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<Option>), ShowLayer = JsonMemberLayer.ExtraLayer)]
        public List<Option> Options { get; set; }

        private JsonDynamicList<AwardBase> GetAwards()
        {
            var awards = new JsonDynamicList<AwardBase>();
            foreach (var option in Options)
            {
                for (int i = 0; i < option.Count; i++)
                {
                    var awardGroup = option.Template.Awards.GetRandomItem() as AwardGroup;
                    awards.AddRange(awardGroup.Awards);
                }
            }

            return awards;
        }

        public GrantMachineBuildingFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull)
        {
            return owner.GetFunctionRecord<GrantMachineBuildingFunctionRecord>(createIfNull);
        }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var record = GetRecord(owner, true);
            record.NextLifecycleTime = DateTimeExtension.MinTime;
            if (owner is RowAdapter other)
            {
                other.Save();
            }
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if (!CanUse(character, ownerObject, mode))
            {
                return;
            }
            var record = GetRecord(ownerObject, false);
            if (record == null)
            {
                character.ShowError(LocalityStrings.unknownError);
                return;
            }
            if (record.NextLifecycleTime >= DateTimeExtension.Now)
            {
                character.ShowError(LocalityStrings.limitedCDTime);
                return;
            }
            if (++record.Times >= MaxTimes)
            {
                record.Times = 0;
                record.NextLifecycleTime = DateTimeExtension.Now + Cooling;
            }

            record.UpdateTo(character.Owner, ownerObject);
            ownerObject.Save();

            var awards = GetAwards();
            var result = character.Owner.GetAwards(awards, nameof(CapsuleBuildingFunction), null, ownerObject.ID);
            character.Owner.User.ShowAwardResult(result);
        }

        public class Option : JsonFormatObject
        {
            private SystemAwardType template;

            [JsonMember]
            public long Type { get; set; }

            [JsonMember]
            public int Count { get; set; }

            public SystemAwardType Template => template ??= GameApplication.DataManager.FindValue<SystemAwardType>(Type);
        }
    }
}
