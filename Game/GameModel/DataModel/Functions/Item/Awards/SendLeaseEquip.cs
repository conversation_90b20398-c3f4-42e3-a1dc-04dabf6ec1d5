using System;
using System.Collections.Generic;
using System.Linq;
using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    public class SendLeaseEquip : ItemFunction
    {
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> FemaleTypes { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> MaleTypes { get; set; }

        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan Duration { get; set; }

        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            if (!base.Use(user, owner, count, protoBytes, mode))
            {
                return false;
            }
            if (user.Gender != Gender.男 && user.Gender != Gender.女)
            {
                user.ShowError(LocalityStrings.operationError);
                return false;
            }

            var types = user.Gender == Gender.男 ? MaleTypes : FemaleTypes;
            foreach (var type in types)
            {
                var equips = user.FindEntities<Equip>(p => p.Type == type);
                if (equips.Count > 0)
                {
                    user.ShowError(LocalityStrings.limitedOwned);
                    return false;
                }
            }
            if (!owner.Delete(1, nameof(SendLeaseEquip), nameof(Use), user.ID))
            {
                user.ShowError(LocalityStrings.operationError);
                return false;
            }

            var awards = types.Select(p => new ObjectAward()
            {
                Type = p,
                Count = 1,
                Properties = new List<ObjectPropertySet>()
                {
                    new ObjectPropertySet()
                    {
                        Name = nameof(Equip.Lease),
                        Value = (int)Duration.TotalMinutes
                    },
                },
            });
            var result = user.GetAwards(awards, nameof(SendLeaseEquip), nameof(Use), user.ID);
            user.User.ShowAwardResult(result);
            return true;
        }
    }
}
