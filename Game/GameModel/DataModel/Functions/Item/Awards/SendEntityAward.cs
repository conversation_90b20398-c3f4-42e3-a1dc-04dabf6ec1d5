using System;
using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    public class SendEntityAward : ItemFunction
    {
        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public Gender Gender { get; set; }

        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            if (!base.Use(user, owner, count, protoBytes, mode))
            {
                return false;
            }
            if (Gender != Gender.无 && user.Gender != Gender)
            {
                user.ShowError(LocalityStrings.limitedCondition);
                return false;
            }
            if (!owner.Delete(1, nameof(SendEntityAward), null, 0))
            {
                user.ShowError(LocalityStrings.unknownError);
                return false;
            }

            var awards = new JsonDynamicList<AwardBase>();
            awards.Add(new ObjectAward() { Type = Type, Count = 1 });

            var result = user.GetAwards(awards, nameof(SendEntityAward), nameof(Use), user.ID);
            user.User.ShowAwardResult(result);
            return true;
        }
    }
}
