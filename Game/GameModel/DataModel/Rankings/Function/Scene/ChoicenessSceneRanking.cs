using System.Collections.Generic;
using System.Linq;
using DataBase;
using GameFramework.Rankings;
using GameModel.Dimensions;
using GameModel.Rankings;
using OpVirtualWorld;

namespace GameModel
{
    /// <summary>
    /// 精选房间排行
    /// </summary>
    public class ChoicenessSceneRanking : SceneRanking<ChoicenessSceneRankingItem, ChoicenessSceneRankingScore>, IActionTrigger<PersonalScene, SceneRankingAction>
    {
        public SceneRankingAction Action => SceneRankingAction.EntranceChanged;

        private ChoicenessSceneRankingItem CreateRankingItem(PersonalSceneSpec scene)
        {
            var item = new ChoicenessSceneRankingItem();
            item.Scope = Scope;
            item.SceneID = scene.ID;
            item.Scene = scene;
            item.CreateSuccess();
            item.Initialize();
            return item;
        }

        public override int Criteria => 0;

        public override int Score(Character source)
        {
            return 0;
        }
        public override int Score(Scene source)
        {
            return 0;
        }
        protected override IRanking<ChoicenessSceneRankingItem, ChoicenessSceneRankingScore> CreateRanking()
        {
            var scenes = GameApplication.SceneManager.FindAllSpec<PersonalSceneSpec>(p => true);
            return new TopRanking<ChoicenessSceneRankingItem, ChoicenessSceneRankingScore>(Top, new Comparer(), scenes.Select(p => CreateRankingItem(p)));
        }

        public void Trigger(PersonalScene source, SceneRankingAction action, object param)
        {
            if (IsInScope(source))
            {
                switch (action)
                {
                    case SceneRankingAction.EntranceChanged:
                        using (EnterWriterLock())
                        {
                            var item = GetItem(source);
                            if (source.IsEntranceOpened)
                            {
                                if (item == null)
                                {
                                    item = CreateRankingItem((PersonalSceneSpec)source.Spec);
                                    Add(item);
                                }
                            }
                            else
                            {
                                if (item != null) Remove(item);
                            }
                        }
                        break;
                    case SceneRankingAction.AdmirationChanged:
                        using (EnterWriterLock())
                        {
                            var item = GetItem(source);
                            if (item != null)
                            {
                                Update(item, new ChoicenessSceneRankingScore(0, DateTimeExtension.Now));
                            }
                        }
                        break;
                }
            }
        }

        public override void RecreateItem(Scene source)
        {
        }

        sealed class Comparer : IComparer<ChoicenessSceneRankingScore>
        {
            public int Compare(ChoicenessSceneRankingScore x, ChoicenessSceneRankingScore y)
            {
                var result = x.Times - y.Times;
                return result == 0 ? y.LastUpdateTime.CompareTo(x.LastUpdateTime) : result;
            }
        }
    }

    public class ChoicenessSceneRankingItem : SceneRankingItem<ChoicenessSceneRankingScore>
    {
        [TableColumn]
        public int Times { get; set; }

        public override ChoicenessSceneRankingScore Score => new ChoicenessSceneRankingScore(Times, LastUpdateTime);

        public override void UpdateScore(in ChoicenessSceneRankingScore value)
        {
            Times = value.Times;
            LastUpdateTime = value.LastUpdateTime;
        }
    }

    public readonly struct ChoicenessSceneRankingScore
    {
        public ChoicenessSceneRankingScore(int times, DateTime lastUpdateTime)
        {
            Times = times;
            LastUpdateTime = lastUpdateTime;
        }

        public int Times { get; }

        public DateTime LastUpdateTime { get; }
    }
}
