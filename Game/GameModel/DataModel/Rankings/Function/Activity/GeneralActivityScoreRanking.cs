using DataBase;
using GameFramework.Rankings;
using GameModel.Activities;
using GameModel.Rankings;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    public class GeneralActivityScoreRanking : CharacterRanking<GeneralActivityScoreRankingItem>, IActionTrigger<Character, UserAction2>
    {
        public UserAction2 Action => UserAction2.更新活动排行榜;

        public override bool ShowInList => false;

        public override PicaRankingType Type => PicaRankingType.活动;

        public override int Criteria => 0;

        public IScoreRankingActivity Activity;

        public override void RecreateItem(Character source)
        {
            //活动无需重上榜
        }

        public override void OnInitialized()
        {
            base.OnInitialized();
            GameApplication.RankingManager.TryAddRankingToActionRankings(Action, this);
        }

        public override int Score(Character source)
        {
            if(Activity == null) return 0;

            return Activity.Score(source);
        }

        public override int Score(Scene scene)
        {
            return 0;
        }

        public void Trigger(Character source, UserAction2 action, object param)
        {
            if (source.InnerTester) return;

            if ((int)param != Group) return;

            using (EnterWriterLock())
            {
                if (Indices.TryGetValue(source.ID, out var item))
                {
                    var ms = Score(source);
                    if (ms == 0 || ms == item.Value) return;

                    Update(item, new GameRankingScore(item.ID, ms, DateTimeExtension.Now));
                    item.Save();
                }
                else
                {
                    var new_item = CreateItem(source);
                    if (new_item != null) Add(new_item);
                }
            }
        }

        private GeneralActivityScoreRankingItem CreateItem(Character character)
        {
            var ms = Score(character);

            var item = RowAdapter.Create<GeneralActivityScoreRankingItem>();
            item.OwnerID = character.ID;
            item.Value = ms;
            item.LastChangeTime = DateTimeExtension.Now;
            item.Owner = character;
            item.Group = Group;
            item.Round = Activity.Round;
            item.CreateSuccess();
            item.Initialize();
            item.Save();
            return item;
        }

        protected override IRanking<GeneralActivityScoreRankingItem, GameRankingScore> CreateRanking()
        {
            var act = GameApplication.ActivityManager.Activities.Values.FirstOrDefault(a => (a.Function is IScoreRankingActivity sra) && sra.Group == Group).Function as IScoreRankingActivity;
            Activity = act;
            var list = GameApplication.DataManager.LoadGameObjects<GeneralActivityScoreRankingItem>(p => p.ID > 0 && !p.Deleted && p.Group == Group && p.Round == Activity.Round, cache: false).Values;
            var dictionary = new Dictionary<long, GeneralActivityScoreRankingItem>();
            foreach (var item in list)
            {
                if (dictionary.TryGetValue(item.OwnerID, out var olditem))
                {
                    if (olditem.LastChangeTime > item.LastChangeTime) continue;
                }
                //if (item.Hided)
                //{
                //    //下榜
                //    item.Deleted = true;
                //    item.Save();
                //    Logger.ImportantRecord.Write($"排行榜 {Enum.GetName(Type)} 隐藏长时间未登录的玩家 {item.OwnerID}");
                //    continue;
                //}
                dictionary[item.OwnerID] = item;
            }
            if (act == null)
            {
                Logger.Error.Write($"target act not found at group {Group} while create ranking");
            }
            return new TopRanking<GeneralActivityScoreRankingItem, GameRankingScore>(Top, new GameRankingComparer(), dictionary.Values);
        }
    }


    [EntityTable(DbGroup.Entity)]
    public class GeneralActivityScoreRankingItem : CharacterRankingItem, GameRankingComparer.IGameRankingItem
    {
        [TableColumn]
        public int Group { get; set; }

        [TableColumn]
        public int Round { set; get; }
    }

}
