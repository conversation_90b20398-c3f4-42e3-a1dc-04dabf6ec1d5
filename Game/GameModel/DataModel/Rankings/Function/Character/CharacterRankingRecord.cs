using System;
using System.Json;
using System.Net.Http.Headers;
using DataBase;
using GameFramework.Rankings;
using GameModel;
using GameModel.Award;
using GameModel.Functions.SceneObjects;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID))]
    public class CharacterRankingRecord : AbstractEntity, ICacheSelf
    {
        private Character owner;

        /// <summary>
        /// 所属角色 <see cref="Character"/> 标识
        /// </summary>
        [TableColumn]
        public long OwnerID { get; set; }

        public Character Owner
        {
            get => owner ??= GameApplication.DataManager.FindValue<Character>(OwnerID);
            set => owner = value;
        }

        /// <summary>
        /// 排行榜类型
        /// </summary>
        [TableColumn]
        public PicaRankingType RankingType { get; set; }

        /// <summary>
        /// 上期排名
        /// </summary>
        [TableColumn]
        public int Rank { get; set; }

        /// <summary>
        /// 上期分数
        /// </summary>
        [TableColumn]
        public int Score { get; set; }

        /// <summary>
        /// 可领取时间
        /// </summary>
        [TableColumn]
        public long DueDay { get; set; }

        /// <summary>
        /// 是否已经领取过本期奖励
        /// </summary>
        [TableColumn]
        public bool Collected { get; set; }

        /// <summary>
        /// 需要额外储存的内容(可能为空)
        /// </summary>
        [TableColumn(DBType = "nvarchar(max)", FormatType = typeof(DynamicDataFormat<PicaRankingExtraRecord>))]
        public PicaRankingExtraRecord ExtraRecord { get; set; }

        [TableColumn]
        public bool Deleted { get; set; }

        public AwardResult GetRankingAward()
        {
            if (Collected) return null;
            Collected = true;
            Save();
            if (ExtraRecord != null && ExtraRecord.RewriteGetRankingAward)
            {
                return ExtraRecord.GetRankingAward(this);
            }
            long AwardID = 0;
            if (Rank == 0)
            {
                var award = GameApplication.DataManager.FirstValue<RankingAwardType>(ra => ra.RankingType == RankingType && ra.Count == 0);//rank为0那只要找阳光普照奖
                if (award != null) AwardID = award.AwardID;
            }
            else
            {
                var awardList = GameApplication.DataManager.FindValue<RankingAwardType>(ra => ra.RankingType == RankingType);
                var award1 = awardList.FirstOrDefault(a => a.TopIndex <= Rank && (a.TopIndex + a.Count) > Rank);
                //没有匹配的奖励档位找阳光普照奖
                if (award1 == null) award1 = awardList.FirstOrDefault(a => a.Count == 0);

                if (award1 != null) AwardID = award1.AwardID;
            }

            if (AwardID == 0) return null;  //没得奖

            return Owner.GetSystemAward(AwardID, $"{RankingType}排行榜奖励");
        }

        public bool CanCollect
        {
            get
            {
                if (Collected) return false;
                if (DateTimeExtension.NowTimeStamp > DueDay) return false;
                if (ExtraRecord != null && ExtraRecord.RewriteGetRankingAward)
                {
                    return ExtraRecord.CanCollect;
                }
                return Rank > 0 && Score > 0;
            }
        }
    }

    public abstract class PicaRankingExtraRecord : FunctionRecord
    {
        /// <summary>
        /// 是否重写了GetRankingAward方法
        /// </summary>
        public virtual bool RewriteGetRankingAward { get; } = false;
        public virtual AwardResult GetRankingAward(CharacterRankingRecord record)
        {
            return null;
        }

        public virtual bool CanCollect { get { return true; } }
    }
}
