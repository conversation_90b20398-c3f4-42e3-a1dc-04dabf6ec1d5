using DataBase;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 徽章
    /// </summary>
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID))]
    public sealed class Badge : AbstractMission<BadgeType>, IProtoObject<BadgeData>, ICacheSelf
    {
        //private BadgeType template;
        //private Character owner;

        /// <summary>
        /// 所属模版（<see cref="BadgeType"/>）标识
        /// </summary>
        //[TableColumn]
        //public int Count { get; set; }

        /// <summary>
        /// 是否可以重复获得
        /// </summary>
        [TableColumn]
        public bool IsRepeat { get; set; }

        /// <summary>
        /// 模板
        /// </summary>
        //public BadgeType Template
        //{
        //    get => template ??= GameApplication.DataManager.FindValue<BadgeType>(Type);
        //    set => template = value;
        //}

        ///// <summary>
        ///// 所属角色
        ///// </summary>
        //public Character Owner
        //{
        //    get => owner ??= GameApplication.DataManager.FindValue<Character>(OwnerID);
        //    set => owner = value;
        //}

        [TableColumn]
        public override long Type { set; get; }

        public int Count
        {
            get
            {
                var condition = (ActionTriggerMissionCondition)(Template.Conditions[0]);
                var record = condition.GetRecord(this, false);
                if (record == null)
                {
                    return 0;
                }
                var completed = record.Completed;
                var target = condition.Target;
                if (completed < target)
                {
                    return 0;
                }
                if (!Template.BadgeUnique)
                {
                    return completed / target;
                }

                return 1;
            }
        }

        public int Process
        {
            get
            {
                if (Template == null || Template.Conditions == null)
                {
                    Logger.Error.Write($"徽章{ID} {OwnerID} {Type}的数据为空");
                    return 0;
                }
                var condition = (ActionTriggerMissionCondition)(Template.Conditions[0]);
                var record = condition.GetRecord(this, false);
                if (record == null)
                {
                    return 0;
                }
                var completed = record.Completed;
                var target = condition.Target;
                if (!Template.BadgeUnique)
                {
                    return completed % target;
                }

                return completed > target ? target : completed;
            }
        }

        /// <summary>
        /// 协议数据
        /// </summary>
        public BadgeData ProtoData
        {
            get
            {
                var data = new BadgeData();
                data.ID = ID;
                data.Type = Type;
                data.Process = Process;
                data.Count = Count;
                return data;
            }
        }

        public void CreateToClient()
        {
            Owner.User?.SendGoogleProto(ClientMethod.NewBadge, ProtoData);
        }

        public void UpdateToClient()
        {
            Owner.User?.SendGoogleProto(ClientMethod.UpdateBadge, ProtoData);
        }
    }
}
