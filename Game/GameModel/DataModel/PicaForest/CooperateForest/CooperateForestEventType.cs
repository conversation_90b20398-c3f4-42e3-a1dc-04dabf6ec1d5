using DataBase;
using GameModel.CooperateGame;
using HelpBase.Json;

namespace GameModel
{
    [EntityTable(DbGroup.Type, CachePolicy.Full)]
    public class CooperateForestEventType : AbstractTypeEntity, ICacheSelf
    {
        /// <summary>
        /// 游戏持续时间
        /// </summary>
        [TableColumn(DBType = "varchar(64)", FormatType = typeof(TimeSpanFormat))]
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// 最大合作人数(除了自己)
        /// </summary>
        [TableColumn]
        public int MaxCollaborator { get; set; }

        /// <summary>
        /// 需要好友好感度
        /// </summary>
        [TableColumn]
        public int NeedFavorability { get; set; }

    }

}
