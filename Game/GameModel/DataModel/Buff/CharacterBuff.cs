using DataBase;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID), nameof(Deleted))]
    public class CharacterBuff : AbstractEntity, ICacheSelf
    {
        private BuffType template;
        private Character owner;

        /// <summary>
        /// 所属角色（<see cref="Character"/>）标识
        /// </summary>
        [TableColumn]
        public long OwnerID { get; set; }

        /// <summary>
        /// 模版（<see cref="BuffType"/>）标识
        /// </summary>
        [TableColumn]
        public long Type { get; set; }

        /// <summary>
        /// 是否已删除
        /// </summary>
        [TableColumn]
        public bool Deleted { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [TableColumn]
        public DateTime CreationTime { get; set; }

        /// <summary>
        /// 删除时间
        /// </summary>
        [TableColumn]
        public DateTime DeletionTime { get; set; }

        /// <summary>
        /// 到期时间
        /// </summary>
        [TableColumn]
        public DateTime Deadline { get; set; }

        /// <summary>
        /// 暂停时间, 
        /// </summary>
        [TableColumn]
        public DateTime PauseTime { get; set; }

        /// <summary>
        /// 模板
        /// </summary>
        public BuffType Template
        {
            get => template ??= GameApplication.DataManager.FindValue<BuffType>(Type);
            set => template = value;
        }

        /// <summary>
        /// 所属角色
        /// </summary>
        public Character Owner
        {
            get => owner ?? (owner = GameApplication.DataManager.FindValue<Character>(OwnerID));
            set => owner = value;
        }

        /// <summary>
        /// 协议数据
        /// </summary>
        public CharacterBuffData ProtoData
        {
            get
            {
                var data = new CharacterBuffData();
                data.ID = ID;
                data.Type = Type;
                data.DeadlineTimestamp = Deadline.ToTimestamp();
                data.CreateTimestamp = CreationTime.ToTimestamp();
                return data;
            }
        }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool Available 
        { 
            get
            {
                if (PauseTime != DateTime.MinValue)
                {
                    return true;
                }
                return DateTimeExtension.Now <= Deadline;
            }
        }

        private void AddToClient()
        {
            Owner.User?.SendGoogleProto(ClientMethod.AddCharacterBuff, ProtoData);
        }

        private void RemoveToClient()
        {
            var req = new IDMessage();
            req.Id = ID;
            Owner.User?.SendGoogleProto(ClientMethod.RemoveCharacterBuff, req);
        }

        private void Trigger<T>(Action<T> action)
            where T : IBuffListener
        {
            if (Template.Function is T other)
            {
                action(other);
            }
        }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            CreationTime = DateTimeExtension.Now;
            DeletionTime = DateTimeExtension.MinTime;
        }

        public void AddCompleted()
        {
            AddToClient();
            Trigger<ICharacterBuffListener>(p => p.AddCompleted(Owner));
        }

        public bool Delete(bool triggerAction = true)
        {
            if (!Deleted)
            {
                Owner.Buffs.Remove(ID);
                if (triggerAction)
                {
                    Trigger<ICharacterBuffListener>(p => p.RemoveCompleted(Owner));
                }
                RemoveToClient();

                Deleted = true;
                DeletionTime = DateTimeExtension.Now;
                Save();
                return true;
            }

            return false;
        }

        public BuffType GetBuffConfig()
        {
            return GameApplication.DataManager.FindValueNoConstraint<BuffType>(Type);
        }

        public void Pause()
        {
            if (PauseTime  > DateTime.MinValue)
            {
                return;
            }
            PauseTime = DateTimeExtension.Now;
            Save();
        }

        public void Proceed()
        {
            var timeInterval = Deadline - PauseTime;
            Deadline = DateTimeExtension.Now + timeInterval;
            PauseTime = DateTime.MinValue;
            Save();
        }
    }
}