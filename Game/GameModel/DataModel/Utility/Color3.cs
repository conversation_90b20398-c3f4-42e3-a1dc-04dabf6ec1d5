using GameModel;
using HelpBase.Json;
using HelpBase.Web;
using Proto.GameModel;

namespace GameModel
{
    [WebClass("颜色")]
    public partial class Color3 : JsonFormatObject
    {
        [WebMember]
        [JsonMember]
        public byte R { get; set; }

        [WebMember]
        [JsonMember]
        public byte G { get; set; }

        [WebMember]
        [JsonMember]
        public byte B { get; set; }

        public static Color3 Random
        {
            get
            {
                byte r = (byte)RandomEvent.Next(0, 256);
                byte g = (byte)RandomEvent.Next(0, 256);
                byte b = (byte)RandomEvent.Next(0, 256);
                return new Color3() { R = r, G = g, B = b };
            }
        }

        public static Color3 White
        {
            get
            {
                return new Color3() { R = 255, G = 255, B = 255 };
            }
        }

        public static Color3 Black
        {
            get
            {
                return new Color3() { R = 0, G = 0, B = 0 };
            }
        }
    }
  
    public partial class EquipColor: Color3
    {
        public new static EquipColor Random
        {
            get
            {
                byte r = (byte)RandomEvent.Next(0, 256);
                byte g = (byte)RandomEvent.Next(0, 256);
                byte b = (byte)RandomEvent.Next(0, 256);
                return new EquipColor() { R = r, G = g, B = b,S= 0.595f,V=0.446f };
            }
        }

        [JsonMember]
        public float S
        {
            get;
            set;
        }

        [JsonMember]
        public float V
        {
            get;
            set;
        }
    }

   
}
