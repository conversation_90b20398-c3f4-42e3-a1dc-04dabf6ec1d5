using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 定时跑马灯
    /// </summary>
    [EntityTable(DbGroup.Entity)]
    public class RollMessage : GameDataObject, ICacheSelf
    {
        private DateTime deadLine = DateTimeExtension.Now.AddYears(100);
        private DateTime startTime = DateTimeExtension.Now;

        [TableColumn(ShowName = "后台标识")]
        public long Foreignkey { set; get; }

        [TableColumn(DBType = "nvarchar(max)", ShowName = "内容")]
        public string Content { get; set; }

        [TableColumn(ShowName = "类型")]
        public int Type { set; get; }

        [TableColumn(ShowName = "次数")]
        public int Times { set; get; }

        [TableColumn(ShowName = "间隔")]
        public int Interval { set; get; }

        [TableColumn(ShowName = "结束时间")]
        public DateTime DeadLine
        {
            get { return deadLine; }
            set { deadLine = value; }
        }

        [TableColumn(ShowName = "开始时间")]
        public DateTime StartTime
        {
            get { return startTime; }
            set { startTime = value; }
        }

        [TableColumn(ShowName = "发送的时间(小时)")]
        public int Hour { set; get; }

        [TableColumn(ShowName = "发送的时间(分钟)")]
        public int Minute { set; get; }

        [TableColumn(ShowName = "是否每分钟都播放")]
        public bool RunForEachMinute { set; get; }

        private List<DateTime> _intervalTimes = new List<DateTime>();

        [TableColumn(DBType = "nvarchar(max)", ShowName = "发送时间列表", FormatType = typeof(JsonCollectionFormat<DateTime, List<DateTime>, UTCDateTimeFormat>))]
        public List<DateTime> IntervalTimes
        {
            set
            {
                _intervalTimes = value;
            }
            get
            {
                return _intervalTimes;
            }
        }

        public void SetData(RollMessageDataWithTimeSet req)
        {
            Foreignkey = req.Id;
            Content = req.RollMessage.Message.DefaultFormatString;
            Type = req.RollMessage.Type;
            Times = req.RollMessage.Times;
            Interval = req.RollMessage.Interval;
            DeadLine = DateTimeExtension.ConvertFromTimestamp(req.Deadline);
            StartTime = DateTimeExtension.ConvertFromTimestamp(req.StartTime);

            //var repeat_time = DateTimeExtension.ConvertFromTimestamp(req.RepeatTime);
            //Hour = repeat_time.Hour;
            //Minute = repeat_time.Minute;

            Save();
        }

        public bool NeedToBeSent()
        {
            //还没到预定时间
            if (DateTimeExtension.Now >= DeadLine || DateTimeExtension.Now < startTime)
            {
                return false;
            }

            //是否每分钟都发
            if (RunForEachMinute)
            {
                return true;
            }

            //是否在预定的时间段之内
            foreach (var time in IntervalTimes)
            {
                if (time.Hour == DateTimeExtension.Now.Hour && time.Minute == DateTimeExtension.Now.Minute)
                {
                    return true;
                }
            }

            return false;
        }
    }
}
