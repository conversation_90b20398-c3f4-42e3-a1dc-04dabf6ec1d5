using DataBase;
using GameModel.Award;
using GameModel.Utility;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace GameModel
{
    [EntityTable(DbGroup.Type)]
    public class WishPoolChoiceType : TypeDataObject, IProtoObject<WishPoolChoiceTypeData>, ICacheSelf
    {
        [TableColumn]
        public int AwardPropertyType { get; set; }

        [TableColumn]
        public int Value { get; set; }

        public WishPoolChoiceTypeData ProtoData
        {
            get
            {
                var data = new WishPoolChoiceTypeData();
                data.Id = ID;
                data.AwardPropertyType = AwardPropertyType;
                data.Value = Value;
                return data;
            }
        }
    }
}
