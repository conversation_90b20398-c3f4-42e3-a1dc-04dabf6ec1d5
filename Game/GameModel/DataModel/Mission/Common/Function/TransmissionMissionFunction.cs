using GameModel.Functions.SceneObjects;
using HelpBase;
using HelpBase.Json;

namespace GameModel
{
    /// <summary>
    /// 传送任务功能
    /// </summary>
    public class TransmissionMissionFunction : InteractionMissionFunction
    {
        /// <summary>
        /// 系统
        /// </summary>
        [JsonMember(FormatType = typeof(DynamicDataFormat<TransmissionSystem>), ShowLayer = JsonMemberLayer.ExtraLayer)]
        public TransmissionSystem System { get; set; }

        public override void Use(Character character)
        {
            System.Do(character.SceneCharacter);
        }
    }
}
