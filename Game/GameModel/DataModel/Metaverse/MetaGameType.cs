#nullable enable

using DataBase;
using GameModel.Managers;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Metaverse
{
    [EntityTable(DbGroup.Type)]
    public sealed class MetaGameType : AbstractEntity, ICacheSelf, ICacheInvalidation
    {
        private SceneType? defaultSceneTemplate;

        [TableColumn(DBType = "varchar(max)")]
        public string? Name { get; set; }

        [TableColumn(DBType = "varchar(max)")]
        public string? Icon { get; set; }

        [TableColumn(DBType = "varchar(max)")]
        public string? IconBundle { get; set; }

        [TableColumn]
        public long DefaultSceneType { get; set; }

        [TableColumn]
        public int SortIndex { get; set; }

        [AllowNull]
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicDataFormat<MetaGameConfiguration>))]
        public MetaGameConfiguration Config { get; set; }

        public SceneType DefaultSceneTemplate => defaultSceneTemplate ??= GameApplication.DataManager.FindValue<SceneType>(DefaultSceneType);

        public MetaGameTypeData ProtoData
        {
            get 
            {
                var data = new MetaGameTypeData();
                data.ID = ID;
                data.Name = I18nManager.I18n(Name);
                data.Icon = Icon;
                data.IconBundle = IconBundle;
                return data;
            }
        }

        void ICacheInvalidation.Invalidate()
        {
            defaultSceneTemplate = null;
        }
    }
}
