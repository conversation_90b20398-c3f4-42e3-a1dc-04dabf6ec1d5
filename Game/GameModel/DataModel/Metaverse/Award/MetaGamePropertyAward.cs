using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Metaverse
{
    public sealed class MetaGamePropertyAward : MetaGameAward
    {
        public MetaGamePropertyAward()
        { }

        public MetaGamePropertyAward(long type, int value)
        {
            if (value <= 0) throw new ArgumentOutOfRangeException(nameof(value));

            Type = type;
            Value = value;
        }

        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public int Value { get; set; }

        public override AwardSpec Spec
        {
            get
            {
                var data = new AwardSpec();
                data.Category = ObjectSpecCategory.MetaGameProperty;
                data.Type = Type;
                data.Value = Value;
                return data;
            }
        }

        public override bool IsValid => Type != 0 && Value > 0;

        public override string AwardDescription => $"{Type}x{Value}";

        public override object[] GetFormatParam()
        {
            return new object[] { Type, Value };
        }

        public override bool CanMerge(AwardBase award)
        {
            return award is MetaGamePropertyAward oa && oa.Type == Type;
        }

        public override AwardBase Merge(AwardBase award)
        {
            if (award == null) throw new ArgumentNullException(nameof(award));
            if (award is MetaGamePropertyAward oa && oa.Type == Type)
            {
                var clone = (MetaGamePropertyAward)Clone();
                clone.Value += oa.Value;
                return clone;
            }

            throw new InvalidOperationException();
        }

        public override void MergeAdd(AwardBase award)
        {
            if (award == null) throw new ArgumentNullException(nameof(award));
            if (award is MetaGamePropertyAward oa && oa.Type == Type)
            {
                Value += oa.Value;
                return;
            }

            throw new InvalidOperationException();
        }

        public override AwardBase Multiply(int fator)
        {
            var clone = (MetaGamePropertyAward)Clone();
            clone.Value *= fator;
            return clone;
        }
    }
}
