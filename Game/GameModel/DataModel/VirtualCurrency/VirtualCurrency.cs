using System;
using DataBase;
using GameModel.Managers;
using Proto.GameModel;

namespace GameModel
{
    /// <summary>
    /// 虚拟货币
    /// </summary>
    [EntityTable(DbGroup.Entity)]
    public class VirtualCurrency : OwnedObjectBase, IProtoObject<VirtualCurrencyData>, ICacheSelf, IPayer, IPayee
    {
        private VirtualCurrencyType template;

        /// <summary>
        /// 地址
        /// </summary>
        [TableColumn]
        public Guid Address { get; set; }

        /// <summary>
        /// 模版（<see cref="VirtualCurrencyType"/>）标识
        /// </summary>
        [TableColumn]
        public long Type { get; set; }

        /// <summary>
        /// 金额
        /// </summary>
        [TableColumn]
        public int Value { get; set; }

        /// <summary>
        /// 当日已获得
        /// </summary>
        [TableColumn]
        public int TodayObtained { get; set; }

        /// <summary>
        /// 最后一次每日更新的时间
        /// </summary>
        [TableColumn]
        public DateTime LastDailyUpdateTime { get; set; } = DateTimeExtension.Now;

        public VirtualCurrencyType Template
        {
            get => template ??= GameApplication.DataManager.FindValue<VirtualCurrencyType>(Type);
            set => template = value;
        }

        public string TaLogName
        {
            get
            {
                switch (Type)
                {
                    case 1: return null;
                    case 2: return "coin";
                    case 5: return null;
                    case 51: return "item";
                    case 100: return "gold_diamond";
                }
                return "item";
            }
        }

        public string TaSetName
        {
            get
            {
                switch (Type)
                {
                    case 1: return "coin";
                    case 2: return "diamond";
                    case 100: return "gold_diamond";
                }
                return "none";
            }
        }

        public static HashSet<long> NeedLogIds = new HashSet<long>() { 2, 100 };

        public bool NeedTaSet => NeedLogIds.Contains(Type);

        public bool NeedLog = true;

        public VirtualCurrencyData ProtoData
        {
            get
            {
                var data = new VirtualCurrencyData();
                data.Id = ID;
                data.Type = Type;
                data.Value = (double)Value;
                data.TodayObtained = (int)TodayObtained;
                data.LastDailyUpdateTimestamp = LastDailyUpdateTime.ToTimestamp();
                return data;
            }
        }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            Address = Guid.NewGuid();
            LastDailyUpdateTime = DateTimeExtension.Now;
        }

        public void UpdateToClient()
        {
            Owner?.User?.SendGoogleProto(ClientMethod.UpdateVirtualCurrency, ProtoData);
        }

        public void CheckDayUpdate()
        {
            var now = DateTimeExtension.Now;
            if (!now.IsDayEqual(LastDailyUpdateTime, true))
            {
                LastDailyUpdateTime = now;
                TodayObtained = 0;
                Save();
            }
        }

        public ICounterfoil Pay(int value, string action, object param = null)
        {
            if (value < 0) throw new ArgumentOutOfRangeException(nameof(value));
            if (action == null) throw new ArgumentNullException(nameof(action));
            if (value == 0) return Counterfoil.Empty(this, Value, action, param);

            int final;
            lock (this)
            {
                final = Value;
                if (final < value)
                {
                    //余额不足
                    return null;
                }

                final -= value;
                Value = final;
            }

            Save();
            var counterfoil = new Counterfoil(this, Type, value, final, action, param);
            //counterfoil.WritePayoutTransactionRecord();
            return counterfoil;
        }

        public ICounterfoil ForcePay(int value, string action, object param = null)
        {
            if (value < 0) throw new ArgumentOutOfRangeException(nameof(value));
            if (action == null) throw new ArgumentNullException(nameof(action));
            if (value == 0) return Counterfoil.Empty(this, Value, action, param);

            int final;
            var oldValue = value;
            lock (this)
            {
                final = Value;
                final -= value;
                Value = final;
            }

            Save();
            var counterfoil = new Counterfoil(this, Type, value, final, action, param);
            
            Owner.GetLeiTingTrackWriter(TaLogName + "_cost")
                .WriteStringEntry("item_id", ID)
                .WriteEntry("item_name", I18nManager.VirtualCurrencyName(this.Type).Replace("-", ""))
                .WriteEntry("num_before", oldValue)
                .WriteEntry("num_after", final)
                .WriteEntry("num", value)
                .WriteRawEntry("action", action)
                .WriteEntry("extend_info", param.ToString())
                .WriteToTaLogger();
            return counterfoil;
        }

        public void Execute(ICounterfoil counterfoil)
        {
            if (counterfoil == null) throw new ArgumentNullException(nameof(counterfoil));
            if (counterfoil.Type != Type) throw new ArgumentException(string.Format("Counterfoil.Type:{0} validate fail.", counterfoil.Type));
            if (counterfoil.Value < 0) throw new ArgumentException(string.Format("Counterfoil.Value:{0} validate fail.", counterfoil.Value));
            if (counterfoil.Value == 0) return;

            int final;
            lock (this)
            {
                final = Value;
                final += counterfoil.Value;
                Value = final;
            }

            Save();
            counterfoil.WritePayinTransactionRecord(this, final);
        }
    }
}
