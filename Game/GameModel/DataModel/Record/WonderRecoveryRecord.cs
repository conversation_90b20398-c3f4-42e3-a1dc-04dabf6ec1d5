using System;
using DataBase;

namespace GameModel
{
    [EntityTable(DbGroup.Record)]
    public class WonderRecoveryRecord : RecordDataObject
    {
        [TableColumn]
        public long ObjectID { get; set; }

        [TableColumn]
        public long OwnerID { get; set; }

        [TableColumn]
        public DateTime LastEndTime { get; set; }

        [TableColumn]
        public DateTime StartTime { get; set; }
    }
}
