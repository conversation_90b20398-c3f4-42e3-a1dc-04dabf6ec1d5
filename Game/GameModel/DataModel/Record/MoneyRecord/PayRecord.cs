using System;
using DataBase;
using HelpBase.Json;

namespace GameModel
{
    public enum PayResult
    {
        失败 = 0,
        成功 = 1,
        无角色 = 2,
        订单号重复 = 3,
        商品未找到 = 4,
        限购 = 5,
        订单金额错误 = 6,
        订单号为空 = 7,
        创建记录失败 = 8,
        金额不足 = 9,
        未开售 = 10,
        过期商品 = 11,
        充值活动未开启 = 12,
        会员限定 = 13,
        购买过互斥礼包 = 14,
        累计消费不足 = 15,
    }
    
    [Serializable]
    [EntityTable(DbGroup.Account)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OrderNumber))]
    public class PayRecord : RecordDataObject
    {
        [TableColumn]
        public string AccountName
        {
            get;
            set;
        }

        [TableColumn]
        public long CharacterID
        {
            get;
            set;
        }

        [TableColumn]
        public string CharacterName
        {
            get;
            set;
        }

        [TableColumn]
        public long ServiceID
        {
            get;
            set;
        }

        string platform = "";
        [TableColumn]
        public string Platform
        {
            get { return platform; }
            set { platform = value; }
        }

        [TableColumn]
        public decimal Dollar { get; set; }

        [TableColumn]
        public DateTime Date { get; set; }

        [TableColumn]
        public int Lv { get; set; }

        [TableColumn]
        public int Diamond { get; set; }

        [TableColumn]
        public int PayTimes { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        [TableColumn]
        public string OrderNumber
        {
            get;
            set;
        }

        [TableColumn]
        public string DeviceID
        {
            get;
            set;
        }

        [TableColumn]
        public string DeviceName
        {
            get;
            set;
        }

        [TableColumn]
        public string DeviceModel
        {
            get;
            set;
        }

        [TableColumn]
        public string IP
        {
            get;
            set;
        }

        /// <summary>
        /// 是不是假充
        /// </summary>
        [TableColumn]
        public bool IsVirtual
        {
            get;
            set;
        }

        /// <summary>
        /// 商城类型
        /// </summary>
        [TableColumn]
        public long ShopType
        {
            get;
            set;
        }

        [TableColumn]
        public bool Refund
        {
            set;
            get;
        }


        [TableColumn]
        public PayResult Result { get; set; }
        
        
        [TableColumn]
        public string ProductId { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonDictionaryFormat<long, int>))]
        public Dictionary<long, int> GetVirtualCurerncies { set; get; } = new Dictionary<long, int>();

        public override IDBManager DBManager
        {
            get { return GameApplication.DataManager.AccountDBManager; }
        }

        public void AddVirtualCurrencyRecord(long currencyType, int value)
        {
            if (!GetVirtualCurerncies.ContainsKey(currencyType))
            {
                GetVirtualCurerncies[currencyType] = 0;
            }
            GetVirtualCurerncies[currencyType] += value;
            SaveToDB(false);
        }
    }
}
