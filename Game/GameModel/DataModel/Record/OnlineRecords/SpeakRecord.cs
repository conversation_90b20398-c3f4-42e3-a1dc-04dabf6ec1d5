using DataBase;
using Proto.GameModel;
using System;

namespace GameModel
{
    /// <summary>
    /// 聊天记录
    /// </summary>
    [Serializable]
    [EntityTable(DbGroup.Record)]
    public class SpeakRecord : RecordDataObject
    {
        public SpeakRecord()
        {
            SpeakTime = DateTimeExtension.Now;
        }

        [TableColumn]
        public long OwnerID { get; set; }

        [TableColumn(DBType = "nvarchar(100)")]
        public string OwnerName { get; set; }

        [TableColumn(DBType = "nvarchar(500)")]
        public string Message { get; set; }

        [TableColumn(DBType = "nvarchar(500)")]
        public string Url { get; set; }

        [TableColumn]
        public SpeakType SpeakType { get; set; }

        [TableColumn]
        public SpeakGroup SpeakGroup { get; set; }

        [TableColumn]
        public long GroupID { get; set; }

        [TableColumn]
        public DateTime SpeakTime { get; set; }
      
    }
}