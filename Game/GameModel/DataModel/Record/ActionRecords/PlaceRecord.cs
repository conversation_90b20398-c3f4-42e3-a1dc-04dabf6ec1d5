using DataBase;
using Proto.GameModel;
using System;

namespace GameModel
{
    /// <summary>
    /// 普通操作记录（基类）
    /// </summary>
    [EntityTable(DbGroup.Record)]
    public class PlaceRecord : RecordDataObject
    {
        [TableColumn(DataIndex = DataIndex.NonClustered)]
        public long AreaID
        {
            get;
            set;
        }

        [TableColumn]
        public long AreaType
        {
            get;
            set;
        }

        /// <summary>
        /// 物品ID 如果是0 则代表是Block
        /// </summary>
        [TableColumn]
        public long ObjectID
        {
            get;
            set;
        }

        [TableColumn]
        public PlaceActionCode ActionCode
        {
            get;
            set;
        }
        [TableColumn(DBType = "varbinary(max)")]
        public byte[] Data
        {
            get;
            set;
        }

        [TableColumn]
        public DateTime Date
        {
            get;
            set;
        }

        public PlaceRecordData ProtoData
        {
            get
            {
                PlaceRecordData prd = new PlaceRecordData();
                prd.ActionCode = ActionCode;
                prd.Bytes = Data;
                prd.ObjectID = ObjectID;
                return prd;
            }
        }
    }
}