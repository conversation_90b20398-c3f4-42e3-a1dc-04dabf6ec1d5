using DataBase;
using GameModel.Activities;
using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;
using System.Collections.Generic;

namespace GameModel
{
    [EntityTable(DbGroup.Type)]
    public class PartyShopType : TypeDataObject, IProtoObject<PartyShopTypeData>, ICacheSelf
    {
        [TableColumn]
        public PartyCategory Category { set; get; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<PartyItemSetting, JsonDynamicList<PartyItemSetting>>))]
        public JsonDynamicList<PartyItemSetting> ItemSettings { get; set; }

        [TableColumn]
        public string TexturePath { set; get; }

        [TableColumn]
        public string Icon { set; get; }

        [TableColumn]
        public int Price { set; get; }
        [TableColumn]
        public long Currency { set; get; }
        [TableColumn]
        public long CostItem { set; get; }
        [TableColumn]
        public string DependOnActivity { set; get; }
        [TableColumn]
        public long BgmId { set; get; }

        public PartyShopTypeData ProtoData
        {
            get
            {
                var res = new PartyShopTypeData();

                res.Id = ID;
                res.Category = Category;
                res.TexturePath = TexturePath;
                res.Icon = Icon;
                res.Price = Price;
                res.Currency = Currency;
                res.CostItem = CostItem;
                res.DependOnActivity = DependOnActivity;
                return res;
            }
        }

        public bool CheckCanStart(Character character)
        {
            if (Category == PartyCategory.钓鱼派对)
            {
                if (character.GuideStep(GuideID.钓鱼引导) < 5)
                {
                    character.ShowErrorTipsMessage("请先前往渔场学习钓鱼技能后再尝试开启派对");
                    return false;
                }
                var activity = GameApplication.ActivityManager.FirstActivity<FishingPartyActivity>();
                if (activity == null) return false;
                var record = activity.GetCharacterRecord(character, true, true);
                if (!activity.CheckAwardTimes(character))
                {
                    return false;
                }
            }
            if (Category == PartyCategory.生日派对)
            {
                if (character.ExternalAction.BirthDay == 0 || character.ExternalAction.BirthMonth == 0)
                {
                    character.ShowErrorTipsMessage("只有生日当天才能开启生日派对");
                    return false;
                }
                if (!DateTimeExtension.Now.AddHours(-DateTimeExtension.DailyResetHour).IsDayEqual(new DateTime(DateTimeExtension.Now.Year, character.ExternalAction.BirthMonth, character.ExternalAction.BirthDay)))
                {
                    character.ShowErrorTipsMessage("只有生日当天才能开启生日派对");
                    return false;
                }

                if (character.ExternalAction.StartBirthdayPartyToday)
                {
                    character.ShowErrorTipsMessage("一天只能开启一次生日派对哦");
                    return false;
                }
                character.ExternalAction.StartBirthdayPartyToday = true;
                Save();
            }
            return true;
        }
    }

    public class PartyItemSetting : Function
    {
        [JsonMember]
        public long ItemId { set; get; }

        [JsonMember]
        public int Weight { set; get; }
    }

    public class FishingPartySetting : PartyItemSetting
    {
        [JsonMember]
        public int Count { set; get; }

        [JsonMember]
        public int BaitCount { set; get; }

        [JsonMember]
        public int SubmitTimes { set; get; }

        [JsonMember(FormatType = typeof(JsonListFormat<PartyRankRangeAward>))]
        public List<PartyRankRangeAward> RankSetting { set; get; }
    }

    public sealed class PartyRankRangeAward : JsonFormatObject
    {
        [JsonMember]
        public int HighestRank { get; set; }

        [JsonMember]
        public int LowestRank { get; set; }

        [JsonMember]
        public long AwardId { get; set; }
    }
}
