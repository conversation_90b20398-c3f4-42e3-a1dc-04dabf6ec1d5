using GameModel.Activities;
using Proto.GameModel;
using System.Numerics;

namespace GameModel
{

    public class PicaFixedGroup
    {
        private static readonly long EMPTY_ELEMENTID = 530276;

        public PicaFixedProductType ProductConfig;
        private Activity FixedActivity;

        public Area Area;
        //private List<SceneObject> PointObjects = new List<SceneObject>();
        private List<FixedPoint> PointsData = new List<FixedPoint>();

        private int MaxCount = 0;
        private float RebirthTimeMagnification = 1;

        public PicaFixedGroup(Scene scene, long fixedProductId)
        {
            Area = scene.FindArea(0);
            ProductConfig = GameApplication.DataManager.FindValue<PicaFixedProductType>(p => fixedProductId == p.ID).FirstOrDefault();

            FixedActivity = GameApplication.ActivityManager.Activities.Values.FirstOrDefault(p =>
                p.Function != null && p.Phase == ActivityPhase.开启
                && p.Function is IActivityFixedCollect fixedActivity
                && fixedActivity.IsFixedProduct(fixedProductId));

            var forestRanking = GameApplication.ActivityManager.FirstActivity<ForestRankingActivity>();
            if (forestRanking != null)
            {
                OnForestRankingActivityStart(forestRanking.AvailableObjects);
            }
        }

        public void InitGroup()
        {
            if (FixedActivity != null)
            {
                if (!FixedActivity.IsInTimeRegionNow) return;
                MaxCount = (FixedActivity.Function as IActivityFixedCollect).GetTotalCount(Area.Scene, ProductConfig.ID);
            }
            for (int i = 0; i < GetMaxCount(); i++)
            {
                AddProductObject(true);
            }
        }

        private void ChangeProductObject(SceneObject so, long typeId)
        {
            Area.Swap(Character.System, so, typeId, out var error);
        }

        public void AddPoint(SceneObject so)
        {
            var fp = new FixedPoint()
            {
                Owner = this,
                Position = so.Transform.Position,
                Direction = so.Transform.Direction,
            };
            PointsData.Add(fp);
        }

        public FixedPoint FindFixedPoint(long id)
        {
            return PointsData.Where(p => p.SceneObject != null && p.SceneObject.ID == id).FirstOrDefault();
        }

        //public void DestroyProductObject(long id)
        //{
        //    var so = FindSceneObject(id);
        //    if (so != null)
        //    {
        //        ChangeProductObject(so, EMPTY_ELEMENTID);
        //    }
        //}

        public void AddProductObject(bool isInit = false)
        {
            if (FixedActivity != null)
            {
                if (!FixedActivity.IsInTimeRegionNow) return;
                if (MaxCount <= 0) return;
                MaxCount--;
            }

            long typeid = 0;
            if (isInit)
            {
                var itemList = ProductConfig.Items.FindAll(m => m.ProductId != 0);
                typeid = itemList.GetRandomItem().ProductId;
            }
            else
            {
                typeid = ProductConfig.Items.GetRandomItem().ProductId;
            }
            var fp = PointsData.Where(p => p.SceneObject == null).Random();
            if (fp != null)
            {
                if (typeid == 0)
                {
                    fp.UpdateToSceneService();
                }
                else
                {
                    fp.AddProductObject(typeid);
                }
                
            }
        }

        #region 活动相关
        public bool CanUse(Character character)
        {
            if (FixedActivity != null)
            {
                if (!FixedActivity.IsInTimeRegionNow)
                {
                    character.ShowMessage("不在活动时间内，请联系客服");
                    return false;
                }
                if (!(FixedActivity.Function as IActivityFixedCollect).CanUse(ProductConfig.ID, character))
                {
                    //character.ShowErrorTipsMessage(PicaLocalityStrings.GetPicaLocalityString("PKT_SYS2_0000106"));
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 增加活动采集次数
        /// </summary>
        public void ExecuteMaxCount(Character character)
        {
            if (FixedActivity != null && FixedActivity.IsInTimeRegionNow)
            {
                (FixedActivity.Function as IActivityFixedCollect).ExecuteMaxCount(ProductConfig.ID, character);
            }
        }

        //public PicaFixedColletionCharacterRecord GetCharacterActivityRecord(Character character)
        //{
        //    if (FixedActivity != null && FixedActivity.IsInTimeRegionNow)
        //    {
        //        return (FixedActivity.Function as IActivityFixedCollect).GetCharacterRecord(character, true, true);
        //    }
        //    return null;
        //}
        #endregion

        public int GetRebirthTime()
        {
            if (FixedActivity != null)
            { 
                return (int)((FixedActivity.Function as IActivityFixedCollect).GetRebirthTime(Area.Scene, ProductConfig.ID) * RebirthTimeMagnification);
            }
            return (int)(ProductConfig.RefreshCycle * RebirthTimeMagnification);
        }

        public int GetMaxCount()
        {
            if (FixedActivity != null)
            {
                return (FixedActivity.Function as IActivityFixedCollect).GetMaxNumber(Area.Scene, ProductConfig.ID);
            }
            return ProductConfig.MaxNumber; 
        }

        public void OnActivityStart(ActivityFunction af)
        {
            if (af is IActivityFixedCollect fixedActivity && fixedActivity.IsFixedProduct(ProductConfig.ID))
            {
                if (FixedActivity == null)
                {
                    FixedActivity = af.Owner;
                }
                InitGroup();
            }
        }

        public void OnActivityEnd(ActivityFunction af)
        {
            if (af is IActivityFixedCollect fixedActivity && fixedActivity.IsFixedProduct(ProductConfig.ID))
            {
                PointsData.ForEach(p => p.DestroyProductObject(false));
                Logger.Debug.Write($"Clean FixedObject : {ProductConfig.ID}");

                // 红包活动的场景需要弹出运气王 粗暴写法 刷新点41就是红包
                //if (ProductConfig.ID == 41)
                //{
                //    List<SceneCharacter> players = new List<SceneCharacter>();
                //    players.AddRange(Area.Players.Values.ToList());
                //    if (players.Count > 0)
                //    {
                //        var firstPlayer = players.OrderByDescending(p =>
                //        {
                //            var rc = fixedActivity.GetCharacterRecord(p.Owner);
                //            return rc == null ? 0 : rc.GoldCount;
                //        }).FirstOrDefault();

                //        var activityRecord = fixedActivity.GetCharacterRecord(firstPlayer.Owner, true);
                //        var data = new CharacterRankingItemData();
                //        data.Value = activityRecord == null ? 0 : activityRecord.bigRedPacketCount;
                //        data.Rank = activityRecord == null ? 0 : activityRecord.GoldCount;
                //        data.Character = firstPlayer.Owner.SpecData;
                //        Area.Scene.Broadcast(ClientMethod.RedPacketKingData, data);
                //    }
                //}
                



            }
        }

        public void OnForestRankingActivityStart(List<long> availableObjects)
        {
            foreach (var item in ProductConfig.Items)
            {
                if (availableObjects.Contains(item.ProductId))
                {
                    RebirthTimeMagnification = 0.5f;
                    return;
                }
            }
        }

        public void OnForestRankingActivityEnd(List<long> availableObjects)
        {
            foreach (var item in ProductConfig.Items)
            {
                if (availableObjects.Contains(item.ProductId))
                {
                    RebirthTimeMagnification = 1f;
                    return;
                }
            }
        }

        public void ClearAllColletions(bool updateToService = true)
        {
            PointsData.ForEach(p => p.DestroyProductObject(updateToService));
        }
    }

    public class FixedPoint
    {
        public PicaFixedGroup Owner;
        public Vector3 Position { get; set; }
        public Direction Direction { get; set; }
        public SceneObject SceneObject { get; set; }

        public void AddProductObject(long typeid)
        {
            Transform ts = new Transform()
            {
                Direction = Direction,
                Position = Position
            };
            SceneObject = Owner.Area.CreateTempObject(Character.System, typeid, ts, "交互点刷新", null, 0);
            if (SceneObject.FunctionRecords == null || SceneObject.FunctionRecords.Count == 0)
            {
                Owner.Area.UpdateCacheVersionToSceneServer();
            }
        }

        public void DestroyProductObject(bool updateToService = true, double delay = 0)
        {
            if (SceneObject != null && !SceneObject.Deleted)
            {
                if (updateToService)
                {
                    UpdateToSceneService();
                }
                Owner.Area.Remove(SceneObject, delay : delay);
                Owner.Area.UpdateCacheVersionToSceneServer();
                SceneObject = null;
            }
        }

        public void UpdateToSceneService(bool sendSceneObjectId = false, int checkTime = 0)
        {
            if (checkTime == 0)
            {
                checkTime = Owner.GetRebirthTime();
            }
            var idInt64 = new AreaIDInt64Message() { ID = Owner.ProductConfig.ID };
            if (sendSceneObjectId && SceneObject != null)
            {
                idInt64.Value = SceneObject.ID;
            }
            ((TimerCheckScene)Owner.Area.Scene).SendTimeCheckRecordToSceneService(checkTime, idInt64);
        }
    }
}
