using System;
using DataBase;
using Proto.GameModel;
using System.Linq;
using GameModel.Functions.Items;

namespace GameModel
{
    [Serializable]
    [EntityTable(DbGroup.Entity)]
    public class FishMarketType : GameDataObject, ICacheSelf, IProtoObject<FishMarketTypeData>
    {
        [TableColumn]
        public int TodayPrice { set; get; }

        [TableColumn]
        public int YesterDayPrice { set; get; } = RandomEvent.Next(50, 400);

        [TableColumn]
        public int TotalSoldCount { set; get; }

        [TableColumn]
        public FishMarketGoodStatus Status { set; get; } = FishMarketGoodStatus.正常;

        public ItemType FishType
        {
            get
            {
                return GameApplication.DataManager.FindValue<EntityType, ItemType>(ID);
            }
        }

        public FishMarketTypeData ProtoData
        {
            get
            {
                var res = new FishMarketTypeData();
                res.FishId = ID;
                res.Price = TodayPrice;
                res.ChangeRate = YesterDayPrice == 0 ? 0 : TodayPrice * 100 / YesterDayPrice - 100;

                return res;
            }
        }

        public void GoDay()
        {
            TotalSoldCount = 0;
            var fish_func = FishType.GetFunction<PicaFishFunction>();

            if (Status != FishMarketGoodStatus.正常)
            {
                var re_san = RandomEvent.Next(0, 100) < fish_func.Sanity;
                if (re_san)
                {
                    Status = FishMarketGoodStatus.正常;
                }
            }
            else
            {
                Status = fish_func.GetRandomFishStatus();
            }

            YesterDayPrice = TodayPrice;

            switch (Status)
            {
                case FishMarketGoodStatus.正常:
                    TodayPrice = RandomEvent.Next(fish_func.NormalPriceInterval[0], fish_func.NormalPriceInterval[1] + 1);
                    break;
                case FishMarketGoodStatus.暴涨:
                    TodayPrice = RandomEvent.Next(fish_func.FullRisePriceInterval[0], fish_func.FullRisePriceInterval[1] + 1);
                    break;
                case FishMarketGoodStatus.暴跌:
                    TodayPrice = RandomEvent.Next(fish_func.FullFallPriceInterval[0], fish_func.FullFallPriceInterval[1] + 1);
                    break;
            }

            Save();
        }
    }
}
