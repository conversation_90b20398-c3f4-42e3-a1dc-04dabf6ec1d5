using DataBase;
using GameModel.Award;
using GameModel.Functions.Skills;
using HelpBase.Json;
using Proto.GameModel;
using System.Collections.Generic;

namespace GameModel
{
    [EntityTable(DbGroup.Type)]
    public class EmotionType : TypeDataObject, IProtoObject<EmotionTypeData>, ICacheSelf
    {
        [TableColumn]
        public long StartIndex { set; get; }

        [TableColumn]
        public long EndIndex { set; get; }

        [TableColumn]
        public EmotionGroup Group { set; get; }
        public EmotionTypeData ProtoData
        {
            get
            {
                var res = new EmotionTypeData();

                res.Id = ID;
                res.StartIndex = StartIndex;
                res.EndIndex = EndIndex;
                res.Group = Group;

                return res;
            }
        }
    }
}
