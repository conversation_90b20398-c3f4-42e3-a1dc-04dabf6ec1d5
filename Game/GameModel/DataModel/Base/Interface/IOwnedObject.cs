namespace GameModel
{
    public interface IOwnedObject
    {
        long OwnerID { get; set; }

        Character Owner { get; set; }
    }

    public interface IDeletable
    {
        bool Deleted { get; set; }
    }

    public interface IUpdateToClient
    {
        void UpdateToClient(ClientNotificationMode mode = ClientNotificationMode.New);
    }

    /// <summary>
    /// 这个接口是指 当这个场景是一个需要保存的公共场景 并且可能创建超过上线的场景 则需要固定这个场景的index 否则在重启之后index会乱掉
    /// </summary>
    public interface IElasticBranchScene
    {
        int GetIndex();

        void SetIndex(int index);
    }
}