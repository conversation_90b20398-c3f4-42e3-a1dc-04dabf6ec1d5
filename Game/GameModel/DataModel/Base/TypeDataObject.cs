using DataBase;
using System;

namespace GameModel
{
    [Serializable]
    public abstract class TypeDataObject : RowAdapter
    {
        public override ICacheManager CacheManager
        {
            get { return GameApplication.DataManager.CacheDataManager; }
        }

        public override IDBManager DBManager
        {
            get { return GameApplication.DataManager.TypeDBManager; }
        }
    }
}
