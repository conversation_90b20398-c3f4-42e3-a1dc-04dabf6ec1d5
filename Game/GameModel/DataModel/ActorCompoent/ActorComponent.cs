using DataBase;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID), nameof(Deleted))]
    public class ActorComponent : OwnedObjectBase, ICacheSelf
    {
        [TableColumn]
        public long Type { set; get; }

        private ActorComponentType typeData;
        public ActorComponentType TypeData
        {

            get
            {
                if (typeData == null)
                {
                    typeData = GameApplication.DataManager.FindValue<ActorComponentType>(Type);
                    if (typeData == null)
                    {
                        Logger.Error.Write("找不到type值为[{0}]的[{1}][{2}]", Type, GetType().Name, StackInfo.GetStackNamesString());
                    }
                }
                return typeData;
            }
            set => typeData = value;
        }


        [TableColumn]
        public int LastDays { set; get; }

        public int ExpiredLimit()
        {
            if (!SelfExpire) return 0;
            return CreateDate.AddDays(LastDays).PassedDays(DateTimeExtension.Now);
        }

        [TableColumn]
        public bool SelfExpire { set; get; } = false;

        public ActorComponentGroup Group => TypeData.Group;

    }
}
