using System;
using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;
using GameModel.Award;
using System.Collections.Generic;

namespace GameModel.Functions.Skills
{
    public class SendGiftEmployFunction : EmploySkillFunctionBase
    {
        internal override void UseSkill(Character ch, long targetId, byte[] protoBytes)
        {

            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            //var agent = target.RelationAgent;
            //var relation = agent.Relations.Find(ch.ID);
            var ccr = new CharacterCallRequest();
            ccr.CallerID = targetId;
            ccr.SceneID = target.SceneCharacter.CurrentScene.ID;
            ccr.SceneType = target.SceneCharacter.CurrentScene.Type;
            ccr.SceneOwnerID = target.SceneCharacter.CurrentScene.OwnerID;
            ccr.SceneName = target.SceneCharacter.CurrentScene.Name;
            ccr.AreaID = target.SceneCharacter.CurrentArea.ID;
            ccr.Caller = target;
            ccr.IsUnread = true;
            ccr.Transform = target.SceneCharacter.Transform;
            ccr.Deadline = DateTimeExtension.Now + GameConfig.SocialConfig.CallCharacterEffective;
            ccr.Scene = target.SceneCharacter.CurrentScene;
            ccr.Area = target.SceneCharacter.CurrentArea;
            ccr.Level = target.Lv;
            if (target.SceneCharacter.CurrentScene.Dimension.Type == DimensionType.BeginnerLand)
            {
                ccr.SceneIndex = target.SceneCharacter.CurrentScene.Index;
            }
            target.SendGoogleProto(ClientMethod.GiftNoCommand);
            ch.SendGoogleProto(ClientMethod.GetGiftSureMsg, ccr.ProtoData);
           // target.SendGoogleProto(ClientMethod.GetGiftSureMsg, relation.ProtoData);
        }
        internal override bool CanUse(Character ch, long targetId, byte[] protoBytes)
        {
            var target = GameApplication.DataManager.FindValue<Character>(targetId);
            if (ch.EmployType != 0)
            {
                target.ShowErrorTipsMessage(GameModel.PicaLocalityStrings.PKT_SYS2_0000039);
                return false;
            }
            return true;
        }
        protected override string Work()
        {
            return "送礼物";
        }

        protected override void CreateRecord(Character ch, long targetId)
        {
            var record = new SendInvitationLetterRecord();
            record.Client = targetId;
            record.RemainLetterCount = 1;
            record.OwnerID = ch.ID;
            ch.employRecord = record;
        }

        public override SendInvitationLetterRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            var record = base.GetRecord(owner, createIfNull);
            return record as SendInvitationLetterRecord;
        }
    }
}
