using System;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.Skills
{
    public class UnlockProfessionDexSkillFunction : SkillFunction
    {
        [JsonMember]
        public long DexId { set; get; }
        [JsonMember]
        public long ProfessionId { set; get; }
        internal override void UseSkill(Character ch, long targetId, byte[] protoBytes)
        {
            ch.ProcessProfessionDex(DexId, ProfessionId);
        }

        internal override bool CanUse(Character ch, long targetId, byte[] protoBytes)
        {
            if (ch.ProfessionDexData.ProfessionDex.ContainsKey(DexId) && ch.ProfessionDexData.ProfessionDex[DexId] == GalleryStatus.AlreadyLighten)
            {
                //pkttodo:已经解锁过这个图鉴了
                return false;
            }
            return true;
        }
    }
}
