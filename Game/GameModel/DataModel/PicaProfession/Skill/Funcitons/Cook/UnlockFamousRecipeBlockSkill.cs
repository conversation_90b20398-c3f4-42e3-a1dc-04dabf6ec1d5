using System;
using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.Skills
{
    internal class UnlockFamousRecipeBlockSkill : SkillFunction
    {
        [JsonMember]
        public int BlockNum { get; set; }
        internal override void LearnSkillEvent(Character ch, long skillType)
        {
            base.LearnSkillEvent(ch, skillType);
            ch.FamousRecipeExtraMax = BlockNum;
            ch.Save();
            ch.UpdateToClient(p => p.FamousRecipeExtraMax = BlockNum);            
        }
    }
}
