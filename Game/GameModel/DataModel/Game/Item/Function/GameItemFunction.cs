#nullable enable

using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Games
{
    public abstract class GameItemFunction : Function
    { }

    public abstract class InteractionGameItemFunction : GameItemFunction
    {
        [JsonMember]
        public string? Tips { get; set; }

        [JsonMember]
        public string? Button { get; set; }

        protected void TrySendTips(GamePlayer player)
        {
            if (!string.IsNullOrEmpty(Tips))
            {
                player.ShowI18nTipsMessage(Tips);
            }
        }

        public abstract void Interact(GamePlayer player, GameItem item, ActionBytesMessage req);
    }

    public abstract class GameItemFunctionRecord : Function
    {
        private GameItem? owner;

        public GameItem Owner => owner ?? throw new InvalidOperationException("player is null");

        public void SetOwner(GameItem value)
        {
            if (value == null) throw new ArgumentNullException(nameof(value));

            this.owner = value;
        }

        public void UpdateToClient()
        {
            var data = new IDStringMessage();
            data.Id = Owner.ID;
            data.Value = ToJson().ToString();
            Owner.Owner.SendGoogleProto(ClientMethod.UpdateGameItemFunctionRecord, data);
        }
    }
}
