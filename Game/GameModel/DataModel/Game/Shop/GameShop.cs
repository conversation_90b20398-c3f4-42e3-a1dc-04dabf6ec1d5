#nullable enable

using DataBase;
using HelpBase.Json;

namespace GameModel.Games
{
    public sealed class GameShop : GameAbstractEntity
    {
        private GameShopType? template;
        private IReadOnlyDictionary<long, GameShopItemType>? items;

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<GameShopFunctionRecord, JsonDynamicList<GameShopFunctionRecord>>))]
        public JsonDynamicList<GameShopFunctionRecord>? FunctionRecords { get; set; }

        public GameShopType Template
        {
            get => template ??= FindTemplate(p => p.ShopTypes);
            set => SetTemplate(ref template, value);
        }

        public bool Available => Template.Function?.Available(this) ?? true;

        public IReadOnlyDictionary<long, GameShopItemType> Items => items ??= Game.ShopItemTypes.Values.Where(p => p.ShopID == Type).ToDictionary(k => k.ID);

        public void Trigger<T>(Action<T> action)
            where T : IGameShopListener
        {
            if (action == null) throw new ArgumentNullException(nameof(action));

            if (Template.Function is T listener)
            {
                action(listener);
            }
        }

        public IReadOnlyDictionary<long, GameShopItemType> GetItems()
        {
            return Template.Function is IGameShopItemsListener listener ? listener.GetItems(this) : Items;
        }

        public bool TryGetItem(long key, [MaybeNullWhen(false)] out GameShopItemType value)
        {
            return GetItems().TryGetValue(key, out value);
        }
    }
}
