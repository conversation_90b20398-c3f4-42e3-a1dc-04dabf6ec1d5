using DataBase;
using HelpBase.Json;
using System.Json;

namespace GameModel.Games
{
    /// <summary>
    /// 表示小游戏对话的类
    /// </summary>
    [EntityTable(DbGroup.Type)]
    public class GameDialogType : GameAbstractEntityType, ICacheSelf
    {
        /// <summary>
        /// 分组
        /// </summary>
        [TableColumn]
        public int Group { get; set; }

        /// <summary>
        /// 多语言
        /// </summary>
        [TableColumn]
        public string Text { get; set; }

        /// <summary>
        /// 子选项集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> Children { get; set; }

        /// <summary>
        /// 事件集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<GameDialogEvent, JsonDynamicList<GameDialogEvent>>))]
        public JsonDynamicList<GameDialogEvent> Events { get; set; }

        /// <summary>
        /// 前端事件集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<GameDialogEvent, JsonDynamicList<GameDialogEvent>>))]
        public JsonDynamicList<GameDialogEvent> ClientEvents { get; set; }

        /// <summary>
        /// 条件集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<ConditionBase, ConditionGroup>))]
        public ConditionGroup Conditions { get; set; }

        /// <summary>
        /// 分支集
        /// </summary>
        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicDataFormat<GameDialogBranchBase>))]
        public GameDialogBranchBase Branches { get; set; }

        /// <summary>
        /// 立绘标识
        /// </summary>
        [TableColumn]
        public long TargetID { get; set; }

        /// <summary>
        /// 覆盖NPC名字显示
        /// </summary>
        [TableColumn]
        public string TempNpcName { get; set; }

        /// <summary>
        /// 是否可关闭
        /// </summary>
        [TableColumn]
        public bool IsClosable { get; set; }

        /// <summary>
        /// 主图
        /// </summary>
        [TableColumn]
        public long MainTexture { get; set; }

        /// <summary>
        /// 允许跳过
        /// </summary>
        [TableColumn]
        public bool AllowSkip { get; set; }

        public bool Validate(out string message)
        {
            if(Branches != null && !Branches.Validate(out message))
            {
                return false;
            }
            if(Children != null)
            {
                foreach(var child in Children)
                {
                    if (GameApplication.DataManager.FindValue<GameDialogType>(child) == null)
                    {
                        message = "children找不到id: " + child;
                        return false;
                    }
                }
            }
            if(Events != null)
            {
                foreach(var e in Events)
                {
                    if (!e.Validate(out message)) return false;
                }
            }
            message = "检测成功";
            return true;
        }

        public sealed class Branch : JsonFormatObject, IProbabilityItem
        {
            /// <summary>
            /// 分支（<see cref="GameDialogType"/>）标识
            /// </summary>
            [JsonMember]
            public override long ID { get; set; }

            /// <summary>
            /// 随机权重
            /// </summary>
            [JsonMember]
            public int Probability { get; set; } = 1;
        }

        public sealed class IdBranch : GameDialogBranchBase
        {
            [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<Branch>))]
            public List<Branch> Ids { get; set; }

            public override long GetNextId(Predicate<IEnumerable<ConditionBase>> checkFunc = null)
            {
                return Ids.Random().ID;
            }

            public override bool Validate(out string message)
            {
                foreach (var b in Ids)
                {
                    if (GameApplication.DataManager.FindValue<GameDialogType>(b.ID) == null)
                    {
                        message = "branch找不到id: " + b.ID;
                        return false;
                    }
                }                
                message = "";
                return true;
            }
        }

        public sealed class ConditionBranch : GameDialogBranchBase
        {
            /// <summary>
            /// 验证成功的分支（<see cref="GameDialogType"/>）标识
            /// </summary>
            [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<Branch>))]
            public List<Branch> SuccessIds { get; set; }

            /// <summary>
            /// 验证失败的分支（<see cref="GameDialogType"/>）标识
            /// </summary>
            [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<Branch>))]
            public List<Branch> FailureIds { get; set; }

            /// <summary>
            /// 验证条件集
            /// </summary>
            [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<ConditionBase, ConditionGroup>))]
            public ConditionGroup Conditions { get; set; }

            public override long GetNextId(Predicate<IEnumerable<ConditionBase>> checkFunc)
            {
                if (checkFunc(Conditions))
                {
                    return SuccessIds.Random().ID;
                }
                return FailureIds.Random().ID;
            }

            public override bool Validate(out string message)
            {
                foreach(var b in SuccessIds)
                {
                    if(b.ID != 0 && GameApplication.DataManager.FindValue<GameDialogType>(b.ID) == null)
                    {
                        message = "branch找不到id: " + b.ID;
                        return false;
                    }
                }
                foreach (var b in FailureIds)
                {
                    if (b.ID != 0 && GameApplication.DataManager.FindValue<GameDialogType>(b.ID) == null)
                    {
                        message = "branch找不到id: " + b.ID;
                        return false;
                    }
                }
                message = "";
                return true;
            }
        }

        /// <summary>
        /// 表示小游戏对话分支的类
        /// </summary>
        public abstract class GameDialogBranchBase : Function
        {
            public abstract long GetNextId(Predicate<IEnumerable<ConditionBase>> checkFunc);

            public abstract bool Validate(out string message);
        }

        public sealed class SequentialGameDialogBranch : GameDialogBranchBase
        {
            private IReadOnlyList<GameDialogType> items;
            private GameDialogType missing;

            [JsonMember(FormatType = typeof(JsonListFormat<long>))]
            public List<long> Types { get; set; }

            [JsonMember]
            public long Missing { get; set; }

            public override long GetNextId(Predicate<IEnumerable<ConditionBase>> checkFunc)
            {
                if (items == null)
                {
                    var list = new List<GameDialogType>(Types.Count);
                    foreach (var type in Types)
                    {
                        list.Add(GameApplication.DataManager.FindValue<GameDialogType>(type));
                    }

                    items = list;
                }
                if (missing == null && Missing != 0)
                {
                    missing = GameApplication.DataManager.FindValue<GameDialogType>(Missing);
                }

                foreach (var template in items)
                {
                    if (template.Conditions == null || template.Conditions.Count == 0 || checkFunc(template.Conditions))
                    {
                        return template.ID;
                    }
                }
                if (missing != null)
                {
                    return missing.ID;
                }

                return 0;
            }

            public override bool Validate(out string message)
            {
                foreach (var t in Types)
                {
                    if (GameApplication.DataManager.FindValue<GameDialogType>(t) == null)
                    {
                        message = "branch找不到id: " + t;
                        return false;
                    }
                }
                if (GameApplication.DataManager.FindValue<GameDialogType>(Missing) == null)
                {
                    message = "branch找不到id: " + Missing;
                    return false;
                }
                message = "";
                return true;
            }
        }
    }
}
