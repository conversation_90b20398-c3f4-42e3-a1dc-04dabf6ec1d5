using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Games
{
    public sealed class GameObjectAward : GameAward
    {
        public GameObjectAward()
        { }

        public GameObjectAward(long type, int count)
        {
            if (count <= 0) throw new ArgumentOutOfRangeException(nameof(count));

            Type = type;
            Count = count;
        }

        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public int Count { get; set; }

        public override AwardSpec Spec
        {
            get 
            {
                var data = new AwardSpec();
                data.Category = ObjectSpecCategory.GameObject;
                data.Type = Type;
                data.Value = Count;
                return data;
            }
        }

        public override bool IsValid => Type != 0 && Count > 0;

        public override string AwardDescription => $"{Type}x{Count}";

        public override object[] GetFormatParam()
        {
            return new object[] { Type, Count };
        }

        public override bool CanMerge(AwardBase award)
        {
            return award is GameObjectAward oa && oa.Type == Type;
        }

        public override AwardBase Merge(AwardBase award)
        {
            if (award == null) throw new ArgumentNullException(nameof(award));
            if (award is GameObjectAward oa && oa.Type == Type)
            {
                var clone = (GameObjectAward)Clone();
                clone.Count += oa.Count;
                return clone;
            }

            throw new InvalidOperationException();
        }

        public override void MergeAdd(AwardBase award)
        {
            if (award == null) throw new ArgumentNullException(nameof(award));
            if (award is GameObjectAward oa && oa.Type == Type)
            {
                Count += oa.Count;
                return;
            }

            throw new InvalidOperationException();
        }

        public override AwardBase Multiply(int fator)
        {
            var clone = (GameObjectAward)Clone();
            clone.Count *= fator;
            return clone;
        }
    }
}
