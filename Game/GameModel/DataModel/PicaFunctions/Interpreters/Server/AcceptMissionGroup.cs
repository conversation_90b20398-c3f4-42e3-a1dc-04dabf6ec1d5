using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class AcceptMissionGroup : ServerInterpretFunction
    {
        [JsonMember]
        public long GroupId { get; set; }

        public override bool SafeForPublic => true;

        public override int DoExec()
        {
            this.OwnerCharacter.CheckUpdateAction(OwnerCharacter, Proto.GameModel.UserAction.触发Inter, GroupId);
            return this.Owner.Index + 1;
        }
    }
}
