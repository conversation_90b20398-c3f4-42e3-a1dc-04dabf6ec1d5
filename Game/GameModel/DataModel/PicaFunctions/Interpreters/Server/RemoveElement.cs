using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class RemoveElement : ServerInterpretFunction
    {
        [JsonMember]
        public long ElementId { get; set; }

        public override bool SafeForPublic => true;

        public override int DoExec()
        {
            var area = OwnerCharacter.SceneCharacter.CurrentArea;
            var so = new SceneObject();
            if (ElementId == 0)
            {
                var saim = ProtoHelp.GetObject<SceneAreaIDMessage>(Bytes);
                so = area.FindObject(saim.Id);
            }
            else
            {
                so = area.FindObject(ElementId);
            }
            if (so != null)
            {
                area.Remove(so);
            }
            return this.Owner.Index + 1;
        }
    }
}
