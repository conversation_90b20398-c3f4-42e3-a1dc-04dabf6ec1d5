using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class ChooseInitialPet : ServerInterpretFunction
    {
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> PetList { get; set; } = new List<long>();

        public override bool SafeForPublic => true;

        public override int DoExec()
        {
            OwnerCharacter.NoviceStatus.PetId = PetList.Random();
            OwnerCharacter.NoviceStatus.Save();
            return this.Owner.Index + 1;
        }
    }
}
