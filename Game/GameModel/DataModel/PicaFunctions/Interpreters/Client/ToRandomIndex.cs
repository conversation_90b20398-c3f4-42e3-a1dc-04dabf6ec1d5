using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class ToRandomIndex : ClientInterpretFunction
    {
        [JsonMember(FormatType = typeof(JsonListFormat<RandomIndex>))]
        public List<RandomIndex> RandomIndex { get; set; }
    }

    public class RandomIndex : JsonFormatObject, IProbabilityItem
    {
        [JsonMember]
        public int Index { get; set; }

        [JsonMember]
        public int Probability { get; set; }
    }
}
