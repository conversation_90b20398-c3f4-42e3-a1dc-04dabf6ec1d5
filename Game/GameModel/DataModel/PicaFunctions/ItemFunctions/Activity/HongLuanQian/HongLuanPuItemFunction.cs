using HelpBase.Json;

namespace GameModel.Functions.Items;

public class HongLuanPuItemFunction : ItemFunction
{
    [JsonMember]
    public int Score { set; get; }
    
    [JsonMember]
    public string ShowAnimeName { set; get; }
    
    [JsonMember]
    public long ShowEffectId { set; get; }
    
    [JsonMember]
    public bool EffectToAllServer{ set; get; }

    [JsonMember] public int ShowEffectTime { set; get; } = 5;
    
    [JsonMember]
    public int RollMessageOutwardsId { set; get; }
    
    [JsonMember]
    public string RollMessageContent { set; get; }
    
    [JsonMember]
    public int RollMessageSendTimes { set; get; }
    
    [JsonMember]
    public int RollMessageSendInterval{ set; get; }
}