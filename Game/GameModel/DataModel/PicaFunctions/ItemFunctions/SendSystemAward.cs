using GameModel.Award;
using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GameModel.Managers;
using Proto.GameModel;

namespace GameModel.Functions.Items
{
    public class SendSystemAward : ItemFunction
    {
        [JsonMember]
        public long AwardId { get; set; }
        [JsonMember]
        public bool NeedHighLight { get; set; }

        [JsonMember]
        public string WeightDes { set; get; }

        [JsonMember]
        public Gender Gender { set; get; } = Gender.无;

        [JsonMember] 
        public float HighLightDelayTime { get; set; }

        [JsonMember(FormatType = typeof(DynamicCollectionFormat<ConditionBase, JsonDynamicList<ConditionBase>>))]
        public JsonDynamicList<ConditionBase> Conditions { get; set; }

        [JsonMember]
        public string RollMessage { set; get; }

        [JsonMember(FormatType = typeof(DynamicCollectionFormat<ConditionBase, JsonDynamicList<ConditionBase>>))]
        public JsonDynamicList<ConditionBase> ShowRollMessageConditions { get; set; }

        public virtual bool NoDeleteAfterUse { set; get; } = false;

        public virtual string RecType(Item owner)
        {
            return string.Empty;
        }

        public virtual bool CanUse(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            if (Conditions != null && !user.CheckCondition(Conditions, showMsg: true))
            {
                return false;
            }
            if (Gender != Gender.无 && user.Gender != Gender)
            {
                user.ShowErrorTipsMessage(I18nManager.I18n("PKT_Message_0007"));
                return false;
            }
            var scene = user.SceneCharacter.CurrentScene;
            if (!CheckCount(user, owner, count))
            {
                user.ShowMessage(LocalityStrings.argumentError);
                return false;
            }
            return true;
        }
        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            //if (!base.Use(user, owner, count, param, mode))
            //{
            //    return false;
            //}

            if (!CanUse(user, owner, count, protoBytes, mode)) return false;

            if (owner is EntityBase eb)
            {
                EntityType et = eb.TypeData;
                //if (Condition != null)
                //{
                //    if (!user.TryCheckCondition(Condition, "使用功能物品", et.Name, eb.ID, true, true, count))
                //    {
                //        user.ShowMessage(LocalityStrings.limitedCondition);
                //        return false;
                //    }
                //}


                var awardType = GameApplication.DataManager.FindValue<SystemAwardType>(AwardId);
                var awards = awardType.Awards.Clone();
                foreach (var awardList in awards)
                {
                    foreach (var awardItem in ((awardList as AwardGroup).Awards))
                    {
                        if (awardItem is ActorPropertyAward)
                        {
                            var property = user.GetProperty(awardItem.Spec.Type);
                            if (property.IsTouchTop)
                            {
                                switch (property.Template.SortDirection)
                                {
                                    case Proto.GameModel.SortDirection.Ascending:

                                        if (property.Type == 1001)
                                        {
                                            user.ShowTipsMessage("饱食度已经是最低了!");
                                        }
                                        else
                                        {
                                            user.ShowTipsMessage(PicaLocalityStrings.PKT_ActorPropertyFull, property.Template.I18nName);
                                        }
                                        break;
                                    case Proto.GameModel.SortDirection.Descending:

                                        user.ShowTipsMessage(PicaLocalityStrings.PropertyTooLow, property.Template.I18nName);
                                        break;
                                }
                                return false;
                            }
                        }
                    }
                }
                if (!NoDeleteAfterUse)//必须删除自身
                {
                    if (!eb.Delete(count, "使用道具", "发送系统奖励", 0))
                    {

                        user.ShowMessage(LocalityStrings.targetDeleteFail);
                        return false;
                    }
                }
                else
                {
                    if (count == eb.Count)
                    {
                        //新获得的道具
                        user.SendGoogleProto(eb.UsedClientMethodOnNew, eb.ProtoObject);
                    }
                    else
                    {
                        user.SendGoogleProto(eb.UsedClientMethodOnUpdate, eb.ProtoObject);
                    }
                }

                if (string.IsNullOrEmpty(awards.Name))
                {
                    awards.Name = et.Name;
                    awards.ForEach(p =>
                    {
                        p.Name = p.Spec = et.Name;
                    });
                }
                AwardResult ar = new AwardResult() { ShowType = "使用" + et.Name };
                var result = user.GetAwards(awardType.Awards, "发送系统奖励", et.Name, eb.ID, count, probabilityMode: AwardProbabilityMode.Global, recType: RecType(owner));
                ar.MergeAddRange(result);

                if (ShowRollMessageConditions != null && AwardShowRollMessageCheck(result, ShowRollMessageConditions, out var item_name))
                {
                    GameApplication.UserManager.ShowRollMessage(I18nManager.I18n(RollMessage, user.Name, owner.Name, item_name));
                }

                if (ar.Count > 0)
                {
                    user.User.ShowAwardResult(ar, mode);
                }

                user.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000072);
                if (NeedHighLight)
                {
                    var dataTosend = new HighLightAwardSpecs();
                    dataTosend.Awards.AddRange(result.Select(r => r.ProtoData));
                    dataTosend.Cate = HighLightAwardCategory.Box;
                    dataTosend.DelayTime = HighLightDelayTime;
                    user.SendGoogleProto(ClientMethod.HighLightAwardSpecs, dataTosend);
                }

                AfterUseAnimation(user, result);

                return true;
            }
            return false;
        }

        public override List<AwardSpec> GetAwardPreview()
        {
            var list = new List<AwardSpec>();
            var awardType = GameApplication.DataManager.FindValue<SystemAwardType>(AwardId);
            foreach (var item in awardType.Awards)
            {
                if (item is AwardGroup group)
                {
                    foreach (var i in group.Awards)
                    {
                        list.AddRange(i.GetAwardPreview());
                    }
                }
            }
            return list;
        }

        public virtual void AfterUseAnimation(Character user, AwardResult result)
        {
            return;
        }

        private bool AwardShowRollMessageCheck<T>(AwardResult awardRes, List<T> conditions, out string itemName)
            where T : ConditionBase
        {
            var check = true;
            itemName = "";
            foreach (var condition in conditions)
            {
                switch (condition)
                {
                    case ShowByQualityCondition sbqc:
                        var show_item = awardRes.FirstOrDefault(asp => asp.Category == ObjectSpecCategory.Object && GameApplication.DataManager.FindValueNoConstraint<EntityType>(asp.Type).Quality >= sbqc.Quality);
                        if (show_item == null)
                        {
                            check = false;
                        }
                        else
                        {
                            itemName = show_item.Name;
                        }
                        break;
                    default:
                        check = check && false;
                        break;
                }
                if (!check)
                {
                    break;
                }
            }

            return check;
        }
    }
}
