using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.Items
{
    /// <summary>
    /// 这个 Function 是用来标记他是获得这个道具就
    /// </summary>
    public class OpenBox : SendSystemAward
    {
        [JsonMember]
        public override bool NoDeleteAfterUse { set; get; } = false;
        public override bool UpdateClientOnGet => false;
        public override void OwnerCountChanged(Item owner, int oldCount, int newCount)
        {
            base.OwnerCountChanged(owner, oldCount, newCount);
            if (newCount > oldCount)
            {
                base.Use(owner.Owner, owner, newCount - oldCount, null, ClientNotificationMode.Append);
            }
        }

        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            return false;
        }
    }
}
