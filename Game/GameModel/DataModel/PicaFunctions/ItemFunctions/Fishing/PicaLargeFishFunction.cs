using GameModel.Managers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.Items
{
    public class PicaLargeFishFunction : ItemFunction, ISpecialNameFunction, ISpecialDescriptionFunction
    {
        public string FindOwnerDescription(long ownerTypeId)
        {
            if (!DataCache.Instance.LargeFishDic.TryGetValue(ownerTypeId, out var result)) return null;
            return I18nManager.ItemDesc(result);
        }

        public string FindOwnerName(long ownerTypeId)
        {
            if(!DataCache.Instance.LargeFishDic.TryGetValue(ownerTypeId, out var result)) return null;
            return I18nManager.I18n("PKT_PREFIX_LARGE", I18nManager.ItemName(result));
        }
    }
}
