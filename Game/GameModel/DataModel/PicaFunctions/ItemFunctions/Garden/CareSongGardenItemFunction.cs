using DataBase;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.Items
{
    public class CareSongGardenItemFunction : SceneObjectNeedItemFunctionBase
    {
        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            var animals = user.SceneCharacter.CurrentArea.Objects.Values.Where(so => so.ExistsFunction<PicaLiveStockCareFunction>());
            bool CanCare = false;
            foreach (var animal in animals)
            {
                if (animal.GetFunctionRecord<PicaLiveStockCareFunctionRecord>(false).CurBehaviorStatus != 0)
                {
                    var animalCareReq = new ActionBytesMessage() { Action = 0 };
                    var animalBytes = ProtoSerializer.Serialize(animalCareReq);
                    animal.GetFunction<PicaLiveStockCareFunction>().GetReward(user,animal);
                    CanCare = true;
                    animal.GetFunctionRecord<PicaLiveStockCareFunctionRecord>(false).UpdateTo(user, animal);
                    animal.Save();
                }
            }
            if (CanCare)
            {
                user.DeleteEntity(owner.Type, 1, "抚育牧歌");
                user.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000072);
            }
            return CanCare;
        }
    }
}
