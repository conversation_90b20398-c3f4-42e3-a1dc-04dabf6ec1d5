using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Functions.Items
{
    public class CropRelatedFunctionBase : ItemFunction
    {
        [JsonMember]
        public string Name { get; set; }

        [JsonMember]
        public decimal Value { get; set; }

        [JsonMember]
        public ChangeType Method { get; set; }

        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            var msg = ProtoSerializer.Deserialize<IDMessage>(protoBytes);
            var targert = user.SceneCharacter.CurrentArea.FindObject(msg.Id);
            if (targert == null)
            {
                user.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000014);
                return false;
            }
            if (!CheckCount(user, owner, count))
            {
                user.ShowMessage(LocalityStrings.argumentError);
                return false;
            }
            var record = targert.GetFunctionRecord<PicaGardenCropFunctionRecord>(false);
            if (record == null || (record.CropStatus != (int)CropStatusWords.成长中 && record.CropStatus != (int)CropStatusWords.照顾))
            {
                user.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000003);
                return false;
            }
            var targetValue = record.GetValueByPropertyName(Name);
            if (targetValue == null)
            {
                user.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000042);
                return false;
            }
            if (Name == "Health" && (int)targetValue == 100)
            {
                user.ShowErrorTipsMessage(PicaLocalityStrings.PKT_Plant_Health_Over_Limit);
                return false;
            }
            if (owner is EntityBase eb)
            {

                if (!eb.Delete(count, "UseItemFunction", "发送系统奖励", 0))//必须删除自身
                {
                    user.ShowMessage(LocalityStrings.targetDeleteFail);
                    return false;
                }
                user.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000072);
                //if (Constants.AllowTaLog)
                //{
                //    user.GetLeiTingTrackWriter("crop")
                //        .WriteStringEntry("item_id", record.CurrentCrop.CropId)
                //        .WriteEntry("item_name", I18nManager.SceneObjectName(record.CurrentCrop.CropId))
                //        .WriteStringEntry("scene_id", user.SceneCharacter.CurrentScene.Type)
                //        .WriteStringEntry("room_id", user.SceneCharacter.CurrentSceneID)
                //        .WriteEntry("cost_item_name", I18nManager.ItemName(owner.Type))
                //        .WriteEntry("cost_item_id", owner.Type)
                //        .WriteRawEntry("operation_type", "使用道具")
                //        .WriteStringEntry("place_id", targert.ID)
                //        .WriteToTaLogger();
                //}
                //return true;
            }
            switch (Method)
            {
                case ChangeType.Change:
                    record.SetValueByJsonPropertyName(Name, Value);
                    break;
                case ChangeType.Add:
                    if (targetValue is int i)
                    {
                        record.SetValueByJsonPropertyName(Name, i + Value);
                        if (Name == "Health")
                        {
                            var func = targert.GetFunction<PicaGardenCropFunction>();
                            func.playHealthAnim(user, targert);
                        }
                    }
                    break;
                case ChangeType.Minus:
                    if (targetValue is int j)
                    {
                        record.SetValueByJsonPropertyName(Name, j - Value);
                    }
                    break;
                case ChangeType.Multiple:
                    if (targetValue is int k)
                    {
                        record.SetValueByJsonPropertyName(Name, k * Value);
                    }
                    break;
            }
            record.UpdateTo(user, targert);

            return true;
        }
    }

    public enum ChangeType
    {
        Change = 1,
        Add = 2,
        Minus = 3,
        Multiple = 4,
    }
}
