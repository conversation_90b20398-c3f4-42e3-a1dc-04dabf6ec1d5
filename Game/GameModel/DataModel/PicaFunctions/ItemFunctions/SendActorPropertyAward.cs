using GameModel.Award;
using GameModel.Managers;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.Items
{
    public class SendActorPropertyAward : ItemFunction, ISpecialDescriptionFunction
    {
        [JsonMember]
        public long PropertyId { set; get; }

        [JsonMember]
        public int Value { set; get; }

        [JsonMember]
        public int MinValue { set; get; }

        [JsonMember]
        public int MaxValue { set; get; }

        /// <summary>
        /// 不在背包中显示使用
        /// </summary>
        [JsonMember]
        public bool NotUseInBag { get; set; }

        private bool CanUse(Character user, int count)
        {
            var property = user.GetProperty(PropertyId);
            var max_value = 0;
            if (Value != 0)
            {
                max_value = Value;
            }
            else
            {
                max_value = MaxValue;
            }

            if (property.Right < max_value * (count - 1))
            {
                return false;
            }
            if (property.Right < 0)
            {
                return false;
            }
            return true;
        }

        public override bool Use(Character user, Item owner, int count, byte[] protoBytes, ClientNotificationMode mode)
        {
            if (!CanUse(user, count))
            {
                return false;
            }
            if (!owner.Delete(count, "使用道具", "发送系统奖励", 0))
            {

                user.ShowMessage(LocalityStrings.targetDeleteFail);
                return false;
            }

            var add_value = (Value == 0 ? RandomEvent.Next(MinValue, MaxValue + 1) : Value) * count;

            user.AddProperty(PropertyId, add_value, "使用道具", paramID : owner.Type);
            //user.SendHighLightAwards(new List<AwardSpec>() { new AwardSpec() { Type = PropertyId, Value = add_value, Category = Proto.GameModel.ObjectSpecCategory.ActorProperty } });
            user.User.ShowAwardResult(new AwardResult() { new AwardSpec() { Type = PropertyId, Value = add_value, Category = Proto.GameModel.ObjectSpecCategory.ActorProperty } }, mode);
            return base.Use(user, owner, count, protoBytes, mode);
        }

        public string FindOwnerDescription(long ownerTypeId)
        {

            string random = "";
            string value_num = Value.ToString();
            if (Value == 0)
            {
                random = "随机";
                value_num = (MinValue + "~" + MaxValue);

            }
            string name = I18nManager.ActorPropertyName(PropertyId);
            string key = "PKT_DESIP" + ownerTypeId;
            return I18nManager.I18n(key, random, value_num, name);//{0}表示随机与否，{1}表示属性数值，{2}表示属性名称
        }
    }
}
