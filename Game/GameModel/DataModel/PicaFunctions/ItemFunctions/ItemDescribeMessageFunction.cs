using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proto.GameModel;

namespace GameModel.Functions.Items
{
    public class ItemDescribeMessageFunction : ItemFunction, IShowDescribeFunction
    {
        [JsonMember]
        public string Describe { set; get; } = "";
        public EntityName EntityName => EntityName.Item;
    }

    public class ItemDescribeFeatureFunction : ItemFunction, IShowDescribeFunction
    {
        [JsonMember]
        public long NewFeatureID { set; get; }
        public EntityName EntityName => EntityName.Item;
    }
}
