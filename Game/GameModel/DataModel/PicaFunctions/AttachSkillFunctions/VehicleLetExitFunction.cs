using GameModel.DataModel.State;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace GameModel.Functions
{
    public class VehicleLetExitFunction:AttachSkillFunctionBase
    {
        public override bool CanUse(Character owner, Character target, AttachSkillType skillType, byte[] protoBytes)
        {
            VehicleState ownerState = null;
            VehicleState targetState = null;
            owner.SceneCharacter.TryGetState<VehicleState>(out ownerState);
            target.SceneCharacter.TryGetState<VehicleState>(out targetState);
            if (targetState == null || !targetState.IsActive)
            {
                owner.ShowErrorTipsMessage(PicaLocalityStrings.GetUndefinedString("选择的玩家未处于载具模式!"));
                return false;
            }
            if (ownerState == null && !ownerState.IsActive)
            {
                owner.ShowErrorTipsMessage(PicaLocalityStrings.GetUndefinedString("您未处于载具模式!"));
                return false;
            }
            if (owner == target)
            {
                owner.ShowErrorTipsMessage(PicaLocalityStrings.GetUndefinedString("不能请离自己!"));
                return false;
            }
            if (ownerState.LeaderId != owner.ID)
            {
                owner.ShowErrorTipsMessage(PicaLocalityStrings.GetUndefinedString("您不是驾驶者!"));
                return false;
            }
            if (targetState.LeaderId != owner.ID)
            {
                owner.ShowErrorTipsMessage(PicaLocalityStrings.GetUndefinedString("选择的玩家不在你的载具上!"));
                return false;
            }
            return true;
        }

        public override bool Use(Character owner, Character Target, AttachSkillType skillType, byte[] protoBytes)
        {
            Target.ExitVehicleState();
            return base.Use(owner, Target, skillType, protoBytes);
        }
    }
}
