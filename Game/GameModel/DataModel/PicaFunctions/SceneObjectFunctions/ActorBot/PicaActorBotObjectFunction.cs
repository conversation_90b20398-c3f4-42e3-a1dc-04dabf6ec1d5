using GameModel.Managers;
using HelpBase;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class PicaActorBotObjectFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaActorBotObjectFunctionRecord>
    {
        public PicaActorBotObjectFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaActorBotObjectFunctionRecord>(createIfNull);
        }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var msg = ProtoHelp.GetObject<IDListMessage>(protoBytes);
            ChangeAvatar(character.Owner, ownerObject, msg.Items);
        }       

        void ChangeAvatar(Character character, SceneObject ownerObject, List<long> equipTypes)
        {
            foreach(var equip in equipTypes)
            {
                if(!character.CheckEntityCountEnough(equip, 1))
                {
                    character.ShowErrorTipsMessage(PicaLocalityStrings.lackOfCount);
                    return;
                }
            }
            var record = GetRecord(ownerObject, true);
            record.ChangeAvatar(equipTypes);
            ownerObject.Save();
        }
    }
}
