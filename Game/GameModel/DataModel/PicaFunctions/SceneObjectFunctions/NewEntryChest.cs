using GameModel.Dimensions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HelpBase.Json;
using Proto.GameModel;
using GameModel.Managers;

namespace GameModel.Functions.SceneObjects
{
    public class NewEntryChest : SwapChest
    {
        [JsonMember]
        public int Phase { get; set; }

        readonly string Prefix = "PKT_REPAIR";
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            base.Use(character, ownerObject, protoBytes, mode);
            if(character.CurrentScene is PicaNewEntryScene)
            {
                var scene = (PicaNewEntryScene)character.CurrentScene;
                var sm = new PicaLocalityStringMessage();
                sm.DefaultFormatString = Prefix + (Phase + 1).ToString("0000");
                character.SendGoogleProto(Proto.GameModel.ClientMethod.ShowTipPanel, sm);
                scene.CurrentPhase++;

                //scene.OpenDoor();
            }
        }
    }
}
