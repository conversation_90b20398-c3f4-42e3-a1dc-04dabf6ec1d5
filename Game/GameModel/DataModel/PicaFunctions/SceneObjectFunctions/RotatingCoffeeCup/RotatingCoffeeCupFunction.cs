using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    public class RotatingCoffeeCupFunction : PicaMountFunction, INeedRecordFunction<RotatingCoffeeCupFunctionRecord>
    {


        [JsonMember(FormatType = typeof(JsonListFormat<float>))]
        public List<float> XList { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<float>))]
        public List<float> YList { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> Duration { get; set; }

        public override List<string> MountAttrs => new List<string>(1) { "sit" };

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var building = owner as SceneObject;
            building.OnMount += GoAround;
        }

        //返回一个record
        public RotatingCoffeeCupFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<RotatingCoffeeCupFunctionRecord>(createIfNull);
        }


        public void GoAround(SceneObjectBase so)
        {
            var r = GetRecord((SceneObject)so);
            if (r.Start)
            {
                return;
            }
            var b= so.Area.Objects.Values.Where(p => p.TypeData.ExistsFunction<RotatingCoffeeCupBaseFunction>()).FirstOrDefault();
            if (b != null)
            {
                var r2 = b.GetFunctionRecord<RotatingCoffeeCupBaseFunctionRecord>(false);
                if (r2.Start)
                {
                    return;
                }
                else
                {
                    b.GetFunction<RotatingCoffeeCupBaseFunction>().ChangeStatus(b);
                }
             

            }
            

        }

        public void ChangeStatus(SceneObject so,long round)
        {
            var r = GetRecord(so);
            r.Index = 0;
            r.Start = true;
            r.Round = round;
            r.Time = DateTimeExtension.NowTimeStamp + Duration[0];
            r.UpdateTo(null, (SceneObject)so);
            so.ChangeStatus(0, "sit");
        }
    }
}
