using DataBase;
using GameModel.Utility;
using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.Organizations
{
    [EntityTable(DbGroup.Entity)]
    public abstract class OrganizationLvUpFunctionRecord : OrganizationBuildingRecord
    {
        [TableColumn]
        public virtual int Lv { get; set; } = 1;
    }
}
