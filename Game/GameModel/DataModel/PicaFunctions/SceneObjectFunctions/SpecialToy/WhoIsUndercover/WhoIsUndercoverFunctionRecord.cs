using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class WhoIsUndercoverFunctionRecord : SceneObjectFunctionRecord
    {
        [JsonMember(FormatType = typeof(JsonListFormat<WhoIsUndercoverPlayer>))]
        public List<WhoIsUndercoverPlayer> Characters { get; set; } = new List<WhoIsUndercoverPlayer>();

        [JsonMember]
        public long WordType { get; set; }

        [JsonMember]
        public bool ShowWord { get; set; }

        [JsonMember]
        public bool ShowVote { get; set; }

        [JsonMember]
        public bool GameOver { get; set; }

        [JsonMember]
        public int SpeakIndex { get; set; }

        public void Reset()
        {
            Characters.Clear();
            WordType = 0;
            ShowWord = false;
            ShowVote = false;
            GameOver = false;
            SpeakIndex = 0;
        }
    }

    public class WhoIsUndercoverPlayer : JsonFormatObject
    {
        [JsonMember]
        public long Id { get; set; }

        [JsonMember]
        public int Index { get; set; }

        [JsonMember]
        public string Name { get; set; }

        [JsonMember]
        public string Word { get; set; }

        [JsonMember]
        public bool Light { get; set; }

        [JsonMember]
        public int VoteForIndex { get; set; }

        public Character Character { get; set; }
    }
}
