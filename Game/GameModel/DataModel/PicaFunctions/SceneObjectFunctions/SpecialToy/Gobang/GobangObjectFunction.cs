using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class GobangObjectFunction : UseableSceneObjectPropFunction, INeedRecordFunction<GobangObjectFunctionRecord>
    {
        public GobangObjectFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<GobangObjectFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var rc = GetRecord(owner, true);
        }
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            //var RC = GetRecord(ownerObject, true);
            ActionBytesMessage abm = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            switch (abm.Action)
            {
                case 0:
                    if (ownerObject.InteractionSceneObjects != null && ownerObject.InteractionSceneObjects.Count == 2)
                    {
                        var characters = ownerObject.InteractionSceneObjects.Values.Select(p => GameApplication.DataManager.FindValue<Character>(p));
                        GameApplication.GobangManager.CreateChessGame(characters);
                    }
                    break;
                case 1:
                    break;
            }
        }
    }
}