using HelpBase;
using Proto.GameModel;
using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class DigitalClockFunction : UseableSceneObjectPropFunction, INeedRecordFunction<DigitalClockFunctionRecord>
    {
        [JsonMember]
        public int DefaultCountdown { get; set; } = 3;//默认倒计时，默认为3
        [JsonMember]
        public int MaxCountdown { get; set; } = 99;//最大倒计时，默认为99
        [JsonMember]
        public int MinCountdown { get; set; } = 1;//最小倒计时，默认为1
        [JsonMember]
        public int FinishStateDuration { get; set; } = -1;//结束后自动转化为默认状态的时间，默认为-1，表示不转化
        [JsonMember]
        public string ActiveCountdownSound { get; set; }
        [JsonMember]
        public string FinishCountdownSound { get; set; }

        [JsonMember]
        public string SoundBundle { get; set; }

        public DigitalClockFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<DigitalClockFunctionRecord>(createIfNull);
        }


        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var rc = GetRecord(owner, true);
            rc.CountdownDuration = 3;
            rc.EndStamp=0;
        }
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var rc = GetRecord(ownerObject, true);
            lock (rc)
            {

                var data = ProtoHelp.GetObject<Int32BoolMessage>(protoBytes);
                if (data.Item2)
                {
                    rc.CountdownDuration = data.Item1;
                    rc.EndStamp = DateTimeExtension.NowTimeStamp + data.Item1 + 1;//加1秒用于让倒计时开始的时间和时间戳变化的瞬间对齐
                }
                else
                {
                    rc.EndStamp = 0;
                }
                rc.UpdateToSceneService(ownerObject);
            }
        }
    }
}
