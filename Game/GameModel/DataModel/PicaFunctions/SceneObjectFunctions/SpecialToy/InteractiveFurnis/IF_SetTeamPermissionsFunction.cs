using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class IF_SetTeamPermissionsFunction : UseableSceneObjectPropFunction , INeedRecordFunction<IF_SetTeamPermissionsFunctionRecord>
    {
        public override bool IsStatic => false;

        public IF_SetTeamPermissionsFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<IF_SetTeamPermissionsFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            //GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if (ownerObject.Owner != character.Owner) return;
            var im = ProtoHelp.GetObject<IDListMessage>(protoBytes);
            var rc = GetRecord(ownerObject, true);
            lock (rc)
            {
                rc.TeamType = new List<int>();
                rc.TeamType.AddRange(im.Items.Select(p => (int)p));
            }
            ownerObject.Save();
            rc.UpdateToSceneService(ownerObject);
        }
    }
}
