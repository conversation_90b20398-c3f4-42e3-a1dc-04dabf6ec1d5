using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.DataModel.PicaFunctions
{
    public abstract class PicaNpcOrderBehavior
    {
        protected PicaNpcOrderFunction Function { get; set; }

        public static PicaNpcOrderBehavior GetInstance(int index, PicaNpcOrderFunction func)
        {
            switch (index)
            {
                case 0:
                case 1: 
                    return new NormalOrderBehavior {  Function = func };
                case 2: 
                    return new BirthdayBehavior {  Function = func };

                default: throw new NotImplementedException();
            }
        }

        public abstract void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New);
    }

    public class NormalOrderBehavior : PicaNpcOrderBehavior
    {
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var abm = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            switch (abm.Action)
            {
                case 0:
                    PutSo(character, ownerObject);
                    break;
                default:
                    character.ShowError(LocalityStrings.operationError);
                    return;
            }
        }

        public void PutSo(SceneCharacter character, SceneObject ownerObject)
        {
            var rr = Function.GetRecord(ownerObject, false);
            if (rr.Type != 0 && rr.Type != 3)
            {
                return;
            }
            if (!Function.CheckPro(character.Owner, 8))
            {
                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000018, I18nManager.ProfessionName(2));
                return;
            }
            if (!Function.CheckProEquip(character.Owner))
            {
                //character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000023);
                return;
            }
            int i = 8;
            if (rr.FunctionIndex == 1)
            {
                i = 9;
            }
            if (Function.HandHeldId[rr.FunctionIndex] != 0)
            {
                if (!character.EquipManager.CheckIsEquip(Function.HandHeldId[rr.FunctionIndex]))
                {
                    return;
                }
                var et = GameApplication.DataManager.FindValue<EntityType, EquipType>(Function.HandHeldId[rr.FunctionIndex]);
                character.EquipManager.Unequip(et.Part);
                character.AvatarChanged();

            }

            if (!character.Owner.Interact(i, ownerObject.ID))
            {
                var l = GameApplication.DataManager.FindValue<InteractionType>(p => p.ID == i);
                if (l == null)
                {
                    return;
                }

                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000031);
                return;
            }

            rr.Type = 1;
            rr.charactor = character.Owner;
            rr.UpdateToSceneService(ownerObject);
            IDInt64ListMessage idl = new IDInt64ListMessage();
            idl.Items.Add(character.CurrentScene.ID);
            idl.Items.Add(character.CurrentAreaID);
            idl.Items.Add(rr.FunctionIndex);
            idl.Items.Add(character.ID);
            idl.Items.Add(ownerObject.ID);
            character.CurrentArea.SendToSceneService(SceneManagerClientMethod.PlayNpcOrderAction, idl);
            ownerObject.Save();
        }
    }

    public class BirthdayBehavior : PicaNpcOrderBehavior
    {
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if (!Function.CheckPro(character.Owner, 13))
            {
                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000018, I18nManager.ProfessionName(2));
                return;
            }
            if (!Function.CheckProEquip(character.Owner))
            {
                //character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000023);
                return;
            }
            var l = GameApplication.DataManager.FindValue<InteractionType>(p => p.ID == 13);
            if (l == null)
            {
                return;
            }
            if (!character.Owner.CheckPropertyCountEnough(1015, l[0].Fatigue))
            {
                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000031);
                return;
            }
            if (IsInBirthday(character,ownerObject))
                return;
            var rr = Function.GetRecord(ownerObject, true);
            if (rr.ParticipantIDs.Count < 1)
            {
                rr.TheOriginatorId = character.Owner.ID;
                rr.IsInviting = true;
            }
            else
            {
                rr.IsInviting = false;
                rr.IsInBirthday = true;
            }
            rr.BirthDayType = 1;
            rr.ParticipantIDs.Add(character.ID);
            rr.IsFull = rr.ParticipantIDs.Count >= 4 ? true : false;
            rr.UpdateToSceneService(ownerObject);
        }

        private bool IsInBirthday(SceneCharacter character, SceneObject ownerObject)
        {
            var rr = Function.GetRecord(ownerObject, true);
            for (int i = 0;i < rr.ParticipantIDs.Count;i++)
            {
                if (character.OwnerID == rr.ParticipantIDs[i])
                {
                    return true;
                }
            }
            return false;
        }
    }
}
