using GameModel.Functions.Interactions;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class PicaWorkFunction : PicaMountFunction, INeedRecordFunction<PicaWorkFunctionRecord>
    {
        [JsonMember]
        public string Mount { get; set; }

        public override List<string> MountAttrs => new List<string>(1) { Mount };

        [JsonMember]
        public float BubbleYOffset { get; set; } = 0f;

        public PicaWorkFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaWorkFunctionRecord>(createIfNull);
        }

        private List<long> _profession = new List<long>();
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> Profession
        {
            get { return _profession ??= new List<long>(); }
            set { _profession = value; }
        }

        [JsonMember(FormatType = typeof(JsonListFormat<InteractionConfig>))]
        public List<InteractionConfig> Interactions { get; set; } = new List<InteractionConfig>();

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var host = owner as SceneObject;
            var rr = GetRecord(owner, true);
            rr.Reset();
            host.Save();

        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var record = GetRecord(ownerObject, false);
            var type = record.CurrentInteractionFunction();
            if (type == null) return;

            WorkInteract(character, ownerObject, type);
        }

        private void WorkInteract(SceneCharacter character, SceneObject so, InteractionType interaction)
        {
            var work = interaction.GetFunction<WorkInteractionFunctionV2>();
            //if (!CheckProfession(character.Owner, work)) return;
            //if (!CheckProEquip(character.Owner))
            //{
            //    //character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000023);
            //    return;
            //}

            //if (!character.Owner.CheckPropertyCountEnough(1015, interaction.Fatigue))
            //{
            //    character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000031);
            //    return;
            //}

            if (!DoWork(character.Owner, so, work))
            {
                return;
            }
            character.Owner.InteractV2(interaction.ID);

            //character.Owner.ConsumeProperty(1015, interaction.Fatigue, "职业打工", interaction.ID.ToString());
            //Award.AwardResult re = new Award.AwardResult() { ShowType = "职业打工" };
            //re.Add(new Award.AwardSpec() { Category = ObjectSpecCategory.ActorProperty, Type = 1015, Value = interaction.Fatigue });

            //var awards = character.Owner.CurrentProfession.TakeAward(interaction);
            //re.AddRange(awards);
            //character.Owner.SendGoogleProto(ClientMethod.AwardResult, re.ProtoData);
        }

        private bool DoWork(Character character, SceneObject so, WorkInteractionFunctionV2 work)
        {
            return work.DoWork(character, so);
        }

        private bool CheckProfession(Character character, WorkInteractionFunctionV2 work)
        {
            if (work.ProfessionList.Count == 0) return true;
            foreach (var pid in work.ProfessionList)
            {
                if (character.CurrentProfessionId == pid) return true;
            }
            return false;
        }
    }

}
