using GameModel.Utility;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    internal class PicaSelfMovingFunction2 : UseableSceneObjectPropFunction, INeedRecordFunction<PicaSelfMovingFunctionRecord2>
    {
        [JsonMember(FormatType = typeof(JsonDataFormat<FloatRect>))]
        public FloatRect Rect { get; set; } = new FloatRect();

        public PicaSelfMovingFunctionRecord2 GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaSelfMovingFunctionRecord2>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var req = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            var record = GetRecord(ownerObject, true);
            switch (req.Action)
            {
                case 1://更新列表

                    break;
                case 2://更新目标点索引
                    var index = ProtoHelp.GetObject<IntMessage>(req.Bytes);
                    if (record.currentTargetIndex == index.Value) return;
                    record.currentTargetIndex = index.Value;
                    break;
            }
            record.UpdateTo(character.Owner, ownerObject);
        }
    }
}
