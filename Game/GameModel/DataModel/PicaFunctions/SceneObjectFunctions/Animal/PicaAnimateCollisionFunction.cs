using System;
using System.Collections.Generic;
using System.Linq;
using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{

    public class PicaAnimateCollisionFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaAnimateCollisionFunctionRecord>
    {

        //
        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }
        //返回一个record
        public PicaAnimateCollisionFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaAnimateCollisionFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {

            var rr = GetRecord(ownerObject, false);
            if (rr == null)
            {
                character.ShowError(LocalityStrings.unknownError);
                return;
            }
            if (rr.Type == 0)
            {
                rr.Type = 1;
                rr.UpdateToSceneService(ownerObject);
                ownerObject.ChangeStatus(0, "open");
                ownerObject.Save();
            }
            else
            {
                rr.Type = 0;
                rr.UpdateToSceneService(ownerObject);
                ownerObject.ChangeStatus(0, "idle");
                ownerObject.Save();

            }
        }

      

    }
}