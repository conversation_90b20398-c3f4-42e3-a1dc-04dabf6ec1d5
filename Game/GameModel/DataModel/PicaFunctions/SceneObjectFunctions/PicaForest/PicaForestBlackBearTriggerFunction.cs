using System;

namespace GameModel.Functions.SceneObjects
{
    public class PicaForestBlackBearTriggerFunction : UseableSceneObjectPropFunction , INeedRecordFunction<PicaForestBlackBearTriggerFunctionRecord>
    {
        public PicaForestBlackBearTriggerFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaForestBlackBearTriggerFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if (character.Owner.PicaForestCanEnterBlackBear())
            {
                var BlackBearRecord = ownerObject.Scene.GetFunctionRecord<BlackBearCaveSceneFunctionRecord>(false);
                if (BlackBearRecord.IsStarted) return;

                var RC = GetRecord(ownerObject, true);
                RC.Started = true;
                RC.UpdateToSceneService(ownerObject);
                if (BlackBearRecord != null)
                {
                    BlackBearRecord.IsStarted = true;
                    BlackBearRecord.UpdateTo();
                }
            }
        }
    }
}
