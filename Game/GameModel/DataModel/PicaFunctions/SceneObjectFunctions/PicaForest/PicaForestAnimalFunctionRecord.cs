using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class PicaForestAnimalFunctionRecord : SceneObjectFunctionRecord
    {
        /// <summary>
        /// 归属者ID
        /// </summary>
        public long AscriptionId { get; set; }
        public long ExpireTime { get; set; }
        public long LastAttackTime { get; set; }

        [JsonMember]
        public float RemainingHP { get; set; }

        [JsonMember]
        public int ActionState { get; set; }



        private PicaForestAnimalFunction GetFunction()
        {
            return Owner.GetFunction<PicaForestAnimalFunction>();
        }

        public void OnOwnerInitialize()
        {
            var func = GetFunction();
            RemainingHP = func.HP;
        }
    }
}
