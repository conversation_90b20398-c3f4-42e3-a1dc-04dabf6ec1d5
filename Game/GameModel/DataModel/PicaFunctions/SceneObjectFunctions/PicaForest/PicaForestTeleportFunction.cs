

using GameCapsule.ConfigObjects;
using GameModel.Dimensions;
using Proto.GameModel;
using System.Numerics;

namespace GameModel.Functions.SceneObjects
{
    public class PicaForestTeleportFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaForestTeleportFunctionRecord>
    {
        public PicaForestTeleportFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaForestTeleportFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var rc = GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var RC = GetRecord(ownerObject, true);
            if (RC != null)
            {
                if (RC.SceneId != 0)
                {
                    var scene = GameApplication.SceneManager.GetScene(RC.SceneId);
                    if (scene != null)
                    {
                        SpawnPointNode sceneSpawnPointNode = null;
                        if (RC.SpawnPointId == 0)
                        {
                            sceneSpawnPointNode = scene.TypeData.SceneNode?.getSpawnPointNode().FirstOrDefault();
                        }
                        else
                        {
                            sceneSpawnPointNode = scene.TypeData.SceneNode?.getSpawnPointNode().FirstOrDefault(p => p.id == RC.SpawnPointId);
                        }
                        var transform = CreateTransformFromSpawnPoint(sceneSpawnPointNode);
                        scene.Enter(character, 0, transform);
                    }
                }
                else if (RC.SceneTypeId != 0 && RC.SpawnPointId != 0)
                {
                    character.Owner.TelePortWithTypeAndPosition(RC.SceneTypeId, Character.TeleportMode.ToSpawnId, RC.SpawnPointId, Vector3.Zero);
                }
            }
        }

        static TransformData CreateTransformFromSpawnPoint(SpawnPointNode obj)
        {
            var transform = new TransformData();
            var position = new Vector3(obj.location.X, obj.location.Y, obj.location.Z);
            transform.Position = position.ProtoData();
            return transform;
        }
    }
}
