using System;
using System.Collections.Generic;
using System.Json;
using System.Numerics;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class PicaColliderFunctionRecordBase : RegenerationFunctionRecord
    {
        [JsonMember]
        public bool IsTrapped { get; set; }
        [JsonMember]
        public long CharacterId { get; set; }

        public override void Update(SceneObject obj, JsonObject json)
        {
            base.Update(obj, json);
            if(IsTrapped)
            {
                ResetTrapStatus(obj);
                IsTrapped = false;
            }
        }

        protected virtual void ResetTrapStatus(SceneObject so)
        {

        }
    }
}
