using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class FixedProductOfFairyFunctionRecord : SceneObjectFunctionRecord
    {
        [JsonMember]
        public long RebirthTime { get; set; }

        public override void TimerChecked(SceneObject so)
        {
            if (so.Scene is PicaFixedProductScene FPScene)
            {
                var fp = FPScene.FindFixedPoint(so.ID);
                fp?.DestroyProductObject();
            }
        }
    }
}
