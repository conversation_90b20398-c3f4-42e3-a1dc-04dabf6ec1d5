using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel
{
    public class PublicCarFunction : PicaMountFunction, INeedRecordFunction<PublicCarFunctionRecord>
    {
        [JsonMember(FormatType = typeof(JsonListFormat<float>))]
        public List<float> XList { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<float>))]
        public List<float> YList { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> Duration { get; set; }

        public override List<string> MountAttrs => new List<string>(1) { "sit"};

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.OnMount += GoAround;
        }

        //返回一个record
        public PublicCarFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PublicCarFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var abm = ProtoSerializer.Deserialize<IDListMessage>(protoBytes);
          
       
           
        }
        public void GoAround(SceneObjectBase so)
        {
            var r = GetRecord((SceneObject)so);
            if (r.Start)
            {
                return;
            }
            r.Index = 0;
            r.Start = true;
            r.Time = DateTimeExtension.NowTimeStamp + Duration[0];
            r.UpdateTo(null, (SceneObject)so);
            
        }

    }
}
