using System;
using System.Collections.Generic;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class PicaSurpriseChairFunctionRecord : SceneObjectFunctionRecord
    {

        [JsonMember]
        public long Type { get; set; }
        public void Reset()
        {
            Type = 0;

        }
        public override void TimerChecked(SceneObject so)
        {
            base.TimerChecked(so);
            //so.ChangeStatus(0, "idle");
            Type = 0;
            UpdateTo(null, so);
            so.ChangeStatus(0, "idle");
        }
    }
}
