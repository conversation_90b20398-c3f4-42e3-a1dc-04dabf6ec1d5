using System;
using System.Collections.Generic;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class PicaGiftFuncitonRecord : SceneObjectFunctionRecord
    {
        public override bool CanPickUp(SceneObject so, out LocalityString error)
        {
            error = LocalityStrings.limitedPickUp;
            return false;
        }

            // _giftOrder.giftOrderDatas.Add(gd.OwnerId);
          //  _giftOrder.giftOrderDatas.Add(gd.FriendId);
          //  _giftOrder.giftOrderDatas.Add(gd.GiftId);
          //  _giftOrder.giftOrderDatas.Add(gd.ShopId);
          //  _giftOrder.giftOrderDatas.Add(gd.SendTime);
           // _giftOrder.giftOrderDatas.Add(gd.TargetId);

        [JsonMember]
        public long OwnerId { get; set; }

        [JsonMember]
        public long FriendId { get; set; }
        [JsonMember]
        public long GiftId { get; set; }
        [JsonMember]
        public long ShopId { get; set; }
        [JsonMember]
        public long SendTime { get; set; }
        [JsonMember]
        public long TargetId { get; set; }
        public override SceneObjectFunctionRecordLayer DetailLevel => SceneObjectFunctionRecordLayer.Server;

       
    }
}
