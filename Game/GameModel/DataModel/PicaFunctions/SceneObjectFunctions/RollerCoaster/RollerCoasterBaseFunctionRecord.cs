using System;
using System.Collections.Generic;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class RollerCoasterBaseFunctionRecord : SceneObjectFunctionRecord
    {
        //状态
        [JsonMember]
        public long Status { get; set; }

        [JsonMember]
        public long CarId { get; set; }
        [JsonMember]
        public long RoadId { get; set; }

        public override SceneObjectFunctionRecordLayer DetailLevel => SceneObjectFunctionRecordLayer.Server;


    }
}
