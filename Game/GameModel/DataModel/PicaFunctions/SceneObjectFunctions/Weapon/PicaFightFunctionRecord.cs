using System;
using System.Collections.Generic;
using System.Numerics;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class PicaFightFunctionRecord : SceneObjectFunctionRecord
    {

        //怪的状态
        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public long Hp { get; set; }

      

        public void Reset()
        {
            Type = 0;
            Hp = 100;
        }
    
        public override void TimerChecked(SceneObject so)
        {

            base.TimerChecked(so);
            if (Type == 1)
            {
                so.ChangeStatus(0, "finish");
                Type = 2;
            }
            else if (Type == 2)
            {
                so.ChangeStatus(0, "idle");

                Reset();
              
            }
            so.Save();
            UpdateTo(null, so);

            //so.ChangeStatus(0, "idle");
        }
    }
}
