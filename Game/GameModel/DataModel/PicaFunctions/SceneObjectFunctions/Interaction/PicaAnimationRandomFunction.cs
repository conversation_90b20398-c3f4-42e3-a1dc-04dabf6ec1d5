using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class PicaAnimationRandomFunction : PicaMountFunction, INeedRecordFunction<PicaAnimationRandomFunctionRecord>
    {
        [JsonMember]
        public int probability { get; set; }

        public override List<string> MountAttrs => new List<string>(1) { "sit" };

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var rr = GetRecord(owner, true);
            var so = (owner as SceneObject);
            so.Save();
        }
        
        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var so = (owner as SceneObject);
            so.OnMount += Mount;
            so.OnUpwardUmount += (sob) => {
                if (sob is SceneCharacter)
                {
                    UnMount(so);
                }
            };
            so.UpdateToSceneService();
        }

        public PicaAnimationRandomFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaAnimationRandomFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {

        }

        void Mount(SceneObjectBase so)
        {
         
            var building = (SceneObject)so;
            var r = new Random().Next(1, 101);
            if (probability >= r)
            {
                building.ChangeStatus(0, "open");
            }
        }
        void UnMount(SceneObjectBase so)
        {
            var building = (SceneObject)so;
            building.ChangeStatus(0, "idle");
        }
    }
}
