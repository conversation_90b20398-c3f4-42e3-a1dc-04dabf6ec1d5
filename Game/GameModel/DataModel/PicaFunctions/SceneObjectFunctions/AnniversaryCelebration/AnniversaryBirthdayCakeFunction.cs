using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class AnniversaryBirthdayCakeFunction : UseableSceneObjectPropFunction
    {
        [JsonMember]
        public float Duration { get; set; }

        public override void Use(<PERSON><PERSON><PERSON><PERSON> character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            
        }
    }
}
