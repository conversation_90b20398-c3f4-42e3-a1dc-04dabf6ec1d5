using System;
using System.Diagnostics;
using DataBase;
using GameFramework.Linq;
using GameModel.Dimensions;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Functions.SceneObjects
{
    public class PicaNoteFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaNoteFunctionRecord>
    {
        public override void OnOwnerPlace(SceneObject building)
        {
            base.OnOwnerPlace(building);
            Save(building.Owner, building);
            var scene = building.Scene as PicaRoomScene;
            scene.Owner.RecordRoomOperation(RecordOperation.RoomNotice, scene, building.Owner.GetXmlTag(),
                scene.ShowName);
            Logger.Debug.Write($"test owner  {building.OwnerID}");
        }

        //
        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }

        //返回一个record
        public PicaNoteFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaNoteFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes,
            ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var abm = ProtoSerializer.Deserialize<NoteData>(protoBytes);
            switch (abm.Action)
            {
                case 0: //读取
                    Read(character.Owner, ownerObject);
                    break;
                case 1: //删除
                    Delete(character.Owner, ownerObject);
                    //Save(character.Owner, ownerObject);
                    break;
                // default:
                //     character.Owner.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000082);
                //     return;
            }

            character.Owner.GetLeiTingTrackWriter("note_use")
                .WriteEntry("chat_content", GetRecord(ownerObject).Context)
                .WriteStringEntry("target_player_id", character.Owner.ID)
                .WriteEntry("target_player_name", character.Owner.Name)
                .WriteEntry("action_type", (abm.Action switch {
                    0=>"读取",
                    1=>"删除",
                    3=>"领奖",
                    _=>"未知"
            })).WriteToTaLogger();
        }

        public override void UseFromBag(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes,
            ClientNotificationMode mode = ClientNotificationMode.New)
        {
            //判断是否是玩家拥有
            if (character.ID != ownerObject.OwnerID)
            {
                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000002);
                return;
            }

            var abm = ProtoSerializer.Deserialize<NoteData>(protoBytes);
            switch (abm.Action)
            {
                case 0: //检查
                    Check(character.Owner, ownerObject, abm.Title, abm.Context);
                    break;
                case 1: //保存
                    //Save(character.Owner, ownerObject);
                    break;
                case 2: //读取
                    //Read(character.Owner, ownerObject);
                    break;
                default:
                    character.Owner.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000082);
                    return;
            }
        }

        /// <summary>
        /// 创建留言
        /// </summary>
        protected virtual void Check(Character character, SceneObject building, String Title, String context)
        {
            //先检查数量
            int already_exist_count = 0;
            character.SceneCharacter.CurrentScene.Areas.ForEach(area =>
            {
                area.Value.Objects.Values.ForEach(so =>
                {
                    if (so.GetFunction<PicaNoteFunction>() != null)
                    {
                        already_exist_count++;
                    }
                });
            });

            if (already_exist_count >= Constants.NoteLimitEachRoom)
            {
                character.ShowErrorTipsMessage("该房间留言条数量已达上限");
                return;
            }


            //无论真伪禁言，都当真禁言处理
            if (character.StopSpeakDate >= DateTimeExtension.Now)
            {
                character.ShowError(LocalityStrings.alertStopSpeakMessageNoMsg,
                    character.StopSpeakDate.ToString("yyyy-MM-dd HH:mm:ss"));
                return;
            }

            if (character.StopSpeakButSendSelfDate >= DateTimeExtension.Now)
            {
                character.ShowError(LocalityStrings.alertStopSpeakMessageNoMsg,
                    character.StopSpeakButSendSelfDate.ToString("yyyy-MM-dd HH:mm:ss"));
                return;
            }


            if (Title.Length > 20 || context.Length > 200 || context.Length <= 0)
            {
                character.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000085);
                return;
            }

            if (GameApplication.SilenceManager.CheckSilenceSetting(SilenceCategory.Text))
            {
                character.ShowTipsMessage(PicaLocalityStrings.label_close);
                return;
            }

            //  var isIllegalTitle = IllegalWordHelper.CheckText(character?.User.Account.ThirdPartyInfo, Title);
            var record_id = RowAdapter.CreateID<EntityBase>();
            var isIllegalContext = IllegalWordHelper.CheckText(character?.User.Account.ThirdPartyInfo, context,
                character?.Name, character?.LeitingRid, "留言条", ExtendType.留言条,
                character.SceneCharacter.CurrentAreaID + ":" + record_id, GameApplication.Service.Config.ID);
            if (isIllegalContext == IllegalDetectResult.ServiceNotAvailable)
            {
                character.ShowError(GameModel.PicaLocalityStrings.PKT_ServiceNotAvaiable);
                return;
            }

            if (isIllegalContext == IllegalDetectResult.IllegalWord)
            {
                character.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000086);
                return;
            }

            character.Notetitle = Title;
            character.Notecontext = context;
            character.Notecreatename = character.Name;
            character.NoteCreatorId = character.ID;
            character.NoteBroadstr = character.SceneCharacter.CurrentAreaID + ":" + record_id;
            character.NoteBroadId = record_id;

            var data = new NoteData();
            data.Action = 0;
            character.SendGoogleProto(ClientMethod.NoteSuc, data);
            var target_player = character.SceneCharacter.CurrentScene.Owner;
            character.GetLeiTingTrackWriter("note")
                .WriteEntry("chat_content", context)
                .WriteStringEntry("target_player_id", target_player.ID)
                .WriteEntry("target_player_name", target_player.Name)
                .WriteEntry("clean_id", character.SceneCharacter.CurrentAreaID + ":" + record_id)
                .WriteStringEntry("room_id", character.SceneCharacter.CurrentScene.ID).WriteToTaLogger();
        }

        private void Save(Character character, SceneObject building)
        {
            var rr = GetRecord(building, false);
            if (rr == null)
            {
                character.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000082);
                return;
            }

            rr.Title = character.Notetitle;
            rr.Context = character.Notecontext;
            rr.CreateName = character.Notecreatename;
            rr.CompletionTime = DateTimeExtension.Now;
            rr.NoteBroadId = character.NoteBroadId;
            rr.CreatorId = character.NoteCreatorId;
            rr.UpdateTo(character, building);
            building.Save();
            var data = new NoteData();
            data.Action = 1;
            character.SendGoogleProto(ClientMethod.NoteSuc, data);
        }

        private void Read(Character character, SceneObject building)
        {
            // var rr = GetRecord(building, false);
            //if (rr == null)
            // {
            //     character.ShowError(LocalityStrings.unknownError);
            //    return;
            // }
            // rr.UpdateToClient(character, building);
            // rr.UpdateTo(character, building);
            long IsRoomOwner = 0;
            if (character.ID == building.Area.OwnerID)
            {
                IsRoomOwner = 1;
            }

            var data = new NoteData();
            data.Action = 2;
            data.RoomId = IsRoomOwner;
            data.SceneId = building.ID;
            var rr = GetRecord(building, false);
            if (rr != null)
            {
                var ch = GameApplication.DataManager.FindValue<Character>(rr.CreatorId);
                if (ch != null)
                {
                    data.CreateName = ch.Name;
                }
            }

            character.SendGoogleProto(ClientMethod.NoteSuc, data);
        }

        protected void Delete(Character character, SceneObject building, string action = "删除留言条")
        {
            //判断是否是玩家拥有
            if (character.ID != building.Area.OwnerID)
            {
                character.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000002);
                return;
            }

            building.Area.Remove(building);
            building.Delete(1, action);
            var data = new NoteData();
            data.Action = 3;
            character.SendGoogleProto(ClientMethod.NoteSuc, data);
        }

        private static void CallCmd(Character character, string methodName, object[] objs)
        {
            var command = character.User.Handler;
            var method = command.GetType().GetMethod(methodName);
            method.Invoke(command, objs);
        }
    }

    public class PicaNoteWithAwardFunction : PicaNoteFunction
    {
        [JsonMember] public long AwardId { set; get; }

        [JsonMember] public long ShowEffectId { set; get; }

        [JsonMember] public bool EffectToAllServer { set; get; }

        [JsonMember] public int RollMessageOutwardsId { set; get; }

        [JsonMember] public string RollMessageContent { set; get; }

        [JsonMember] public int ShowEffectTime { set; get; } = 5;
        
        [JsonMember] public int IntimacyIncrease { set; get; }

        [JsonMember(FormatType = typeof(JsonListFormat<AwardSpecData>))]
        public List<AwardSpecData> AwardSpecDatas => AwardId > 0
            ? (GameApplication.DataManager.FindValue<SystemAwardType>(AwardId)?.ProtoData.Awards)
            : new List<AwardSpecData>();

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes,
            ClientNotificationMode mode = ClientNotificationMode.New)
        {
            base.Use(character, ownerObject, protoBytes, mode);

            
            var rec = GetRecord(ownerObject, false);
            if (rec == null)
            {
                Logger.Error.Write($"PicaNoteWithAwardFunction rec is null {ownerObject.ID}");
                return;
            }

            if (character.ID != character.CurrentScene.OwnerID)
            {
                return;
            }
            
            var abm = ProtoSerializer.Deserialize<NoteData>(protoBytes);
            if (abm.Action == 3 && AwardId != 0)
            {
                Delete(character.Owner, ownerObject, "删除留言礼盒");
                character.Owner.SendHighLightAwards(character.Owner.GetSystemAward(AwardId));
                character.Owner.IncreaseIntimacyValue(rec.CreatorId, IntimacyIncrease, "房间赠礼");
                if (ShowEffectId > 0)
                {
                    var effect_data = new ShowEffectData();
                    effect_data.Type = ShowEffectType.Ui;
                    effect_data.Id = ShowEffectId;
                    effect_data.Time = ShowEffectTime;
                    character.CurrentScene.Broadcast(ClientMethod.ShowEffect, effect_data);
                    if (EffectToAllServer)
                    {
                        GameApplication.UserManager.BroadCastEffectToPublicScene(effect_data);
                    }
                }

                if (!string.IsNullOrEmpty(RollMessageContent))
                {
                    GameApplication.UserManager.ShowRollMessage(
                        I18nManager.I18n(RollMessageContent, rec.CreateName, character.Owner.Name),
                        outwardsId: RollMessageOutwardsId);
                }
            }
        }

        protected override void Check(Character character, SceneObject building, string Title, string context)
        {
            if (!character.RelationAgent.Relations.Any(r => r.TargetID == character.SceneCharacter.CurrentScene.OwnerID && r.Group == RelationGroup.Friend))
            {
                character.ShowErrorTipsMessage("该礼盒仅可在好友房间内摆放哦");
                return;
            }
            base.Check(character, building, Title, context);
        }
    }
}