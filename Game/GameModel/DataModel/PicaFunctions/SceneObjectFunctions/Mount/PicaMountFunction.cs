using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public abstract class PicaMountFunction : UseableSceneObjectPropFunction
    {
        // 抽象对象 不需要FormatType， 由上层类决定
        // [JsonMember(FormatType = typeof(JsonListFormat<string>))]
        public abstract List<string> MountAttrs { get; }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            if (owner is SceneObjectBase sob)
            {
                sob.MountAttrs.Clear();
                sob.MountAttrs.AddRange(MountAttrs);
            }
        }

        public bool CanMount(SceneObjectBase target)
        {
            return target.GetEmptyIndex() >= 0;
        }

        // /// <summary>
        // /// 往target身上挂载upwardSo
        // /// </summary>
        // /// <param name="area"></param>
        // /// <param name="upwardSo"></param>
        // /// <param name="target"></param>
        // /// <param name="index"></param>
        // /// <returns></returns>
        // public bool Mount(Area area, SceneObjectBase upwardSo, SceneObjectBase target, int? index = null)
        // {
        //     return area.Mount(upwardSo, target, index);
        // }
        //
        // /// <summary>
        // /// 从target身上卸载upwardSo
        // /// </summary>
        // /// <param name="area"></param>
        // /// <param name="upwardSo"></param>
        // /// <param name="target"></param>
        // /// <returns></returns>
        // public bool Umount(Area area, SceneObjectBase upwardSo, SceneObjectBase target)
        // {
        //     return area.Umount(upwardSo, target);
        // }


        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes,
            ClientNotificationMode mode = ClientNotificationMode.New)
        {
        }
    }
}