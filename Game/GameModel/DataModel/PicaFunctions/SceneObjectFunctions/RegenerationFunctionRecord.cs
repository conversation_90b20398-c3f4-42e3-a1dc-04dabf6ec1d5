using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    /// <summary>
    /// 刷新点的物件
    /// </summary>
    public class RegenerationFunctionRecord : SceneObjectFunctionRecord
    {
        /// <summary>
        /// 刷新的时间
        /// </summary>
        [JsonMember]
        public long RebirthTime { get; set; }

        public virtual void Reset()
        {
            RebirthTime = 0;
        }

        public override void TimerChecked(SceneObject so)
        {
            base.TimerChecked(so);
            RebirthTime = 0;
            AddProductObject(so);
        }

        public void AddProductObject(SceneObject so)
        {
            if (so.Scene is PicaFixedProductScene FPScene)
            {
                //var group = FPScene.FindGroup(so.ID);
                //group.AddProductObject();
            }
        }

        public void DestroyProductObject(SceneObject so)
        {
            if (so.Scene is PicaFixedProductScene FPScene)
            {
                //var group = FPScene.FindGroup(so.ID);
                //RebirthTime = DateTimeExtension.NowTimeStamp + group.GetRebirthTime();
               
                //group.DestroyProductObject(so.ID);
                //UpdateToSceneService(so);
            }
        }
    }
}
