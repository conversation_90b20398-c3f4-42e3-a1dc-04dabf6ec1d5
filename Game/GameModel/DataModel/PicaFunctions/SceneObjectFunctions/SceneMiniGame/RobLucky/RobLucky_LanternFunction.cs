using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class RobLucky_LanternFunction : SceneObjectFunction, INeedRecordFunction<RobLucky_LanternFunctionRecord>
    {
        public RobLucky_LanternFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<RobLucky_LanternFunctionRecord>(createIfNull);
        }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            GetRecord(owner, true);
        }
    }
}
