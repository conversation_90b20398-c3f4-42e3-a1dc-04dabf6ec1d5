using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public abstract class MiniGame_ScoreBoardFunction : SceneObjectFunction
    {
        //public MiniGame_ScoreBoardFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        //{
        //    return owner.GetFunctionRecord<MiniGame_ScoreBoardFunctionRecord>(createIfNull);
        //}

        //public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        //{
        //    base.OnOwnerCreate(owner, createDepth);
        //    GetRecord(owner, true);
        //}
    }
}
