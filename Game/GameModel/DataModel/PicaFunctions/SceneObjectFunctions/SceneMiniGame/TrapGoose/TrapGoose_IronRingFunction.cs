using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class TrapGoose_IronRingFunction : UseableSceneObjectPropFunction, INeedRecordFunction<TrapGoose_IronRingFunctionRecord>
    {
        [JsonMember]
        public long HandHeldId { get; set; }

        public TrapGoose_IronRingFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<TrapGoose_IronRingFunctionRecord>(createIfNull);
        }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            GetRecord(owner, true);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var rc = GetRecord(ownerObject);
            if (rc.InGaming)
            {
                //character.EquipForWork(HandHeldId);
                SendRequest(character.Owner, ownerObject, 1, null);
            }
        }
    }
}
