using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    public class SeesawFunction : PicaMountFunction, INeedRecordFunction<SeesawFunctionRecord>
    {


        public override List<string> MountAttrs => new List<string>(2) { "sit", "sit" };

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var building = owner as SceneObject;
            building.OnMount += OnMounted;
            building.OnUpwardUmount += (sob) => {
                if (sob is SceneCharacter)
                {
                    OnMounted(building);
                }
            };
        }

        //返回一个record
        public SeesawFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<SeesawFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
         
        }
        
        void OnMounted(SceneObjectBase so)
        {
            var t = so.InteractionSceneObjects;
             if (t.ContainsKey(0) && !t.ContainsKey(1))
            {
                var building = (SceneObject)so;
                building.ChangeStatus(0, "idle-1");
            }
            else if (!t.ContainsKey(0) && t.ContainsKey(1))
            {
                var building = (SceneObject)so;
                building.ChangeStatus(0, "idle-2");
            }
            else if (t.ContainsKey(0) && t.ContainsKey(1))
            {
                var building = (SceneObject)so;
                building.ChangeStatus(0, "open");
            }
            else if (!t.ContainsKey(0) && !t.ContainsKey(1))
            {
                var building = (SceneObject)so;
                building.ChangeStatus(0, "idle");
            }
        }
        void UnMounted(SceneObjectBase so)
        {
            var t = so.InteractionPoint.TargetID;
            var a=so.Area.FindObject(t);
            if (a != null)
            {
                OnMounted(a);
            }
        }
    }
}
