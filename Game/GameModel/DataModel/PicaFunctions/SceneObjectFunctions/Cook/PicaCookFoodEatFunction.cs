using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proto.GameModel;
using GameModel.Managers;

namespace GameModel.Functions.SceneObjects
{
    public class PicaCookFoodEatFunction : UseableSceneObjectPropFunction
    {
        public override void Use(<PERSON><PERSON><PERSON><PERSON> character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            throw new NotImplementedException();
        }
    }
}
