using System;
using System.Collections.Generic;
using System.Linq;
using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{

    public class PicaWorkTeleportFunction : UseableSceneObjectPropFunction
    {
        [JsonMember]
        public long WorkProfessionId { get; set; }

      
        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);

        }



        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {

        }
    }
}