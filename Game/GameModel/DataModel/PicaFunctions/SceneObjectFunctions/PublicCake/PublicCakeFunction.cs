using System;
using System.Collections.Generic;
using System.Linq;
using DataBase;
using GameModel.Activities;
using GameModel.Award;
using GameModel.Functions.Skills;
using GameModel.Managers;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    /// <summary>
    /// 烹饪器具功能
    /// </summary>
    public class PublicCakeFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PublicCakeFunctionRecord>
    {
        [JsonMember]
        public long Equip { get; set; }

        [JsonMember]
        public string Effect { get; set; }

        [JsonMember]
        public long TargetSuitId { get; set; }

        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }
        //返回一个record
        public PublicCakeFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PublicCakeFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {

            character.ChangeInteractionAvatar(new List<long>() { Equip});
            character.Owner.CheckUpdateAction(character.Owner, UserAction.糊你一脸);
        }


        public override void OnOwnerPlace(SceneObject building)
        {
            base.OnOwnerPlace(building);
            var observable = GameApplication.ObjectManager.GetObservable(building.Type);
            lock (observable) observable.Add(building);

            var act = GameApplication.ActivityManager.FirstActivity<YummyPartyActivity>();
            act?.RefreshYummyTable(building.Type);
        }

        public override void OnOwnerPickUp(SceneObject building, Area area)
        {
            base.OnOwnerPickUp(building, area);
            var observable = GameApplication.ObjectManager.GetObservable(building.Type);
            lock (observable) observable.Remove(building);
        }


    }

}