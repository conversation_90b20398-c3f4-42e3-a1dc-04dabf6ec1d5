using System;
using System.Collections.Generic;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class PicaGardenLuckyCateFunctionRecord : SceneObjectFunctionRecord, IDayUpdatable, INeedSaveOnCharacterRecord
    {
        [JsonMember]
        public long StartTime { get; set; }

        [JsonMember]
        public int TodayExtraCollectTimes { get; set; }

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        public DateTime LastUpdateDate { get; set; } = DateTimeExtension.Now;

        private long typeID;
        [JsonMember]
        public long TypeID
        {
            get
            {
                if (Owner != null) typeID = Owner.Type;
                return typeID;
            }
            set
            {
                typeID = value;
            }
        }

        public void CopyDataFromCharacter()
        {
            lock (Owner.Owner.ListDatas.UniqueSceneObjectRecords)
            {
                var savedRecord = Owner.Owner.GetOrLoadSceneObjectRecord(TypeID)?.Records.Find(r => r is PicaGardenLuckyCateFunctionRecord) as PicaGardenLuckyCateFunctionRecord;
                if (savedRecord != null)
                {
                    StartTime = savedRecord.StartTime;
                    TodayExtraCollectTimes = savedRecord.TodayExtraCollectTimes;
                    LastUpdateDate = savedRecord.LastUpdateDate;
                    if (!DateTimeExtension.IsDayEqual(DateTimeExtension.Now, LastUpdateDate, true))
                    {
                        GoDay();
                    }
                }
            }
        }

        public void GoDay()
        {
            LastUpdateDate = DateTimeExtension.Now;
            TodayExtraCollectTimes = 0;
        }

        public override void TimerChecked(SceneObject so)
        {
            base.TimerChecked(so);
            var maxTime = so.Owner.CrownLevel > 0 ? Constants.CrownMaxCollectTime : Constants.NormalMaxCollectTime;
            var currentTime = (DateTimeExtension.NowTimeStamp - StartTime) / 60;
            if (currentTime > maxTime)
            {
                //so.ChangeStatus(1, "");
                UpdateToSceneService(Owner);
            }
        }
    }
}
