using GameModel.Award;
using GameModel.Dimensions;
using GameModel.Managers;
using HelpBase.Json;
using HelpBase.Linq;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static GameModel.Dimensions.PicaRoomBaseSpec;

namespace GameModel.Functions.SceneObjects
{
    public class MerchantShopFunctionRecord : SceneObjectFunctionRecord, IDayUpdatable
    {
        [JsonMember]
        //商店类型
        public int Group { get; set; }

        [JsonMember(FormatType = typeof(JsonDataFormat<NpcShopDailyItemList>))]
        //商品列表
        public NpcShopDailyItemList ShopData { get; set; }

        [JsonMember]
        //商人今日出现次数
        public int MerchantsCount { get; set; }

        [JsonMember]
        //今天是否使用过交易书
        public bool ForceItemUsed { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        //购买玩家数
        public List<long> BoughtCharacters { get; set; } = new List<long>();

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        //商人离开时间
        public DateTime ShopCloseTime { get; set; } = DateTimeExtension.Now;

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        //商人下次刷新时间
        public DateTime NextShopChekTime { get; set; } = DateTimeExtension.Now;

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        //上次日期更新时间
        public DateTime LastUpdateDay { get; set; } = DateTimeExtension.Now;

        [JsonMember(FormatType = typeof(UTCDateTimeFormat))]
        //下次广播时间
        public DateTime NextMessageTime { get; set; } = DateTimeExtension.Now;

        public bool ShopCome;//重启后反正都商人都走了
        public bool needBroadCast;

        public override void TimerChecked(SceneObject so)
        {
            base.TimerChecked(so);
            var now = DateTimeExtension.Now;
            bool needSave = false;
            if (!now.IsDayEqual(LastUpdateDay, true))
            {
                GoDay();
                needSave = true;
            }
            if (Group != 0 && !ShopCome && ShopCloseTime <= now)
            {
                ShopEnd();
                needSave = true;
            }
            if((Group == 0 || ShopCome) && NextShopChekTime <= now)
            {
                CheckShop();
                needSave = true;
            }
            if(needBroadCast && NextMessageTime <= now)
            {
                SendCountDownMessage();
                needSave = true;
            }
            if (needSave)
            {
                Owner.Save();
                UpdateToSceneService(Owner);
            }
        }
        public void GoDay()
        {
            LastUpdateDay = DateTimeExtension.Now;
            MerchantsCount = 0;
            ForceItemUsed = false;
        }

        public void CheckShop(bool force = false, int forceNpc = 0)
        {
            if (ShopCome)
            {
                RefreshShop();
                return;
            }
            var func = Owner.GetFunction<MerchantShopFunction>();
            NextShopChekTime = DateTimeExtension.Now.AddMinutes(func.ShopRefreshTime);
            if (MerchantsCount >= func.MerchantsPerDay) return;//今天出现次数达到上限
            var appearCheck = new Random().Next(100);
            if (appearCheck > func.ShopPossiblity && !force) return;//商人不出现
            ShopCome = true;
            if (forceNpc == 0)
            {
                var rg = new RandomGroup();
                rg.AddRange(func.Shops.Select(shop => new RandomItem<int>((int)shop.Type, shop.Value)));
                var result = rg.GetRandomItem() as RandomItem<int>;
                Group = result.Value;
            }
            else
            {
                Group = forceNpc;
            }
            if (GameApplication.GlobalVariableManager.MerchantBoardCastAvailable)
            {
                var broadcastCheck = new Random().Next(100);
                if (broadcastCheck <= func.BroadcastPossiblity || force)
                {
                    needBroadCast = true;
                    GameApplication.GlobalVariableManager.MerchantBoardCastAvailable = false;
                    SendCountDownMessage();
                }
            }
        }

        void ShopEnd()
        {
            //商人消失
            var old_group = Group;
            var npcShop = GameApplication.DataManager.FirstValue<NpcShopType>(n => n.ShopGroup == Group);
            Group = 0;
            ShopData = null;
            var func = Owner.GetFunction<MerchantShopFunction>();
            NextShopChekTime = DateTimeExtension.Now;
            if (func.OthersBuyAward != null)
            {
                var count = BoughtCharacters.Count;
                var award = func.OthersBuyAward.FindLast(a => a.Type <= count);
                if (award != null)
                {

                    JsonDynamicList<AwardBase> ads = new JsonDynamicList<AwardBase>();
                    var awardType = GameApplication.DataManager.FindValue<SystemAwardType>(award.Value).Awards;
                    foreach (var aw in awardType)
                    {
                        if (aw is AwardGroup group)
                        {
                            foreach (var i in group.Awards)
                            {
                                ads.Add(i);
                            }
                        }
                    }
                    Owner.Owner.AddMail(I18nManager.I18n("PKT_MerchantLeaveMail_Title"), string.Format(I18nManager.I18n("PKT_MerchantLeaveMail_Content"), I18nManager.NpcName(npcShop.NpcId), count),
                                ads, true, DateTimeExtension.Now.AddDays(7), MailType.Award, "旅行商人离开");
                }
            }
            BoughtCharacters.Clear();
            Owner.ChangeStatus(0, "idle");

            //if (Constants.AllowTaLog)
            //{
            //    Owner.Owner.GetLeiTingTrackWriter("merchant_action")
            //        .WriteStringEntry("group", old_group)
            //        .WriteStringEntry("scene_id", Owner.Scene.ID)
            //        .WriteRawEntry("action", "消失")
            //        .WriteStringEntry("place_id", Owner.ID)
            //        .WriteToTaLogger();
            //}
            var spec = ((PicaRoomBaseSpec)Owner.Scene.Spec);
            func.RemoveTag(spec, Owner.Type);
        }

        /// <summary>
        /// 刷出商人
        /// </summary>
        public void RefreshShop(bool count = true, int forceNpc = 0)
        {
            ShopCome = false;
            if (count) MerchantsCount++;
            var func = Owner.GetFunction<MerchantShopFunction>();
            if(Group != 0)
            {
                //已经定好商人了
            }
            else if (forceNpc == 0)
            {
                var rg = new RandomGroup();
                rg.AddRange(func.Shops.Select(shop => new RandomItem<int>((int)shop.Type, shop.Value)));
                var result = rg.GetRandomItem() as RandomItem<int>;
                Group = result.Value;
            }
            else
            {
                Group = forceNpc;
            }
            ShopCloseTime = DateTimeExtension.Now.AddMinutes(func.ShopLastTime);
            NextShopChekTime = DateTime.MaxValue;//商店出现时不进行检查
            BoughtCharacters.Clear();//清空购买玩家列表
            SetShopItemList(Group);
            Owner.ChangeStatus(1, "open");

            //if (Constants.AllowTaLog)
            //{
            //    Owner.Owner.GetLeiTingTrackWriter("merchant_action")
            //        .WriteStringEntry("group", Group)
            //        .WriteStringEntry("scene_id", Owner.Scene.ID)
            //        .WriteRawEntry("action", "出现")
            //        .WriteStringEntry("place_id", Owner.ID)
            //        .WriteToTaLogger();
            //}

            var npcShop = GameApplication.DataManager.FirstValue<NpcShopType>(n => n.ShopGroup == Group);
            if (needBroadCast)
            {
                needBroadCast = false;
                var message = string.Format(I18nManager.I18n("PKT_MerchantRollMessage_3"), Owner.Owner.Name, I18nManager.NpcName(npcShop.NpcId));
                SendGlobalMessage(message);
            }
            var spec = ((PicaRoomBaseSpec)Owner.Scene.Spec);
            func.AddTag(spec, Owner.Type);

            if (Owner.Owner.SceneCharacter.CurrentAreaID == Owner.AreaID) return;//在场景里就不通知了
            Owner.Owner.SendGoogleProto(ClientMethod.MerchantNpcNotice, new IDInt64Message() { ID = npcShop.NpcId, Value = Owner.Scene.ID});
        }

        const int PiaoliGroup = 14;
        void SetShopItemList(int group)
        {
            var npcShop = GameApplication.DataManager.FirstValue<NpcShopType>(n => n.ShopGroup == group);
            var disposition = Owner.Owner.getNpcDispositionLevel(npcShop.NpcId);
            var Totallist = GameApplication.DataManager.FindValue<NpcShopItemType>(n => n.Available && n.Group == group && (n.AvailableDay.Count == 0 || n.AvailableDay.Contains((int)DateTimeExtension.Now.DayOfWeek))).ToList();
            var FinalList = new Dictionary<long, List<NpcShopItemType>>();
            foreach(var item in Totallist)
            {
                //将award相同的商品合并，随机一个来达到价格随机的功能
                var prop = item.Awards.FirstOrDefault().Spec.Type;
                if (FinalList.TryGetValue(prop, out var list))
                {
                    list.Add(item);
                }
                else
                {
                    FinalList.Add(prop, new List<NpcShopItemType>() { item });
                }
            }
            RandomList<RandomItem<long>> HighValueRandomList = new RandomList<RandomItem<long>>();
            RandomList<RandomItem<long>> RandomList = new RandomList<RandomItem<long>>();
            foreach (var list in FinalList)
            {
                var item = list.Value.Random();
                if (item.IsHighValue)
                {
                    HighValueRandomList.Add(new RandomItem<long>() { Value = item.ID, Probability = item.Probability });
                }
                else
                {
                    RandomList.Add(new RandomItem<long>() { Value = item.ID, Probability = item.Probability });
                }
            }
            int normalBlockCount = Constants.MerchantNpcDispositionNormalBlock.Last();
            int highValueBlockCount = Constants.MerchantNpcDispositionHighValueBlock.Last();
            if(npcShop.NpcId == PiaoliGroup)
            {
                //皮奥利没有好感度
                normalBlockCount = 2;
                highValueBlockCount = 1;
            }
            var resultList = RandomList.GetRandomItems(normalBlockCount);
            var highValueResultList = HighValueRandomList.GetRandomItems(highValueBlockCount);
            resultList.AddRange(highValueResultList);
            ShopData = new NpcShopDailyItemList();
            ShopData.Group = group;
            ShopData.Items.AddRange(resultList.Select(n => n.Value));
        }

        public void BuySuccess(long id)
        {
            if (BoughtCharacters.Contains(id)) return;//已经记录过了
            BoughtCharacters.Add(id);
            Owner.Save();
            UpdateToSceneService(Owner);
        }

        void SendGlobalMessage(string message)
        {
            GameApplication.UserManager.ShowRollMessage(message);
            var data = new SpeakData();
            data.Message = message +
                "<link=TryEnterScene" + Owner.Scene.ID + "><color=#74a852>" + I18nManager.I18n("PKT_NKW0000032") + "</color></link>";
            data.Character = new CharacterSpecData();
            data.Type = SpeakType.Text;
            data.Group = SpeakGroup.镇长;
            data.SpeakTime = DateTimeExtension.NowTimeStamp;
            GameApplication.UserManager.BroadCast(ClientMethod.Speak, data);
        }

        void SendCountDownMessage()
        {
            var lastMin = (NextShopChekTime - DateTimeExtension.Now).Minutes + 1;
            var npcShop = GameApplication.DataManager.FirstValue<NpcShopType>(n => n.ShopGroup == Group);
            var message = string.Format(I18nManager.I18n("PKT_MerchantRollMessage_1"), Owner.Owner.Name, I18nManager.NpcName(npcShop.NpcId) ,lastMin);
            SendGlobalMessage(message);
            NextMessageTime = lastMin > 5 ? DateTimeExtension.Now.AddMinutes(5) : DateTime.MaxValue;
        }
    }
}
