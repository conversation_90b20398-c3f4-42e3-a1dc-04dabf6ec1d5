using DataBase;
using GameModel.Config;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    internal class PicaCraftStationFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaCraftStationFunctionRecord>
    {
        [JsonMember]
        public int SlotNum { get; set; }

        [JsonMember]
        public int CrownSlotNum { get; set; }

        [JsonMember]
        public int Group { get; set; }

        [JsonMember]
        public bool Compatible { get; set; } = true;   
        
        /// <summary>
        /// 是否允许加速
        /// </summary>
        [JsonMember]
        public bool AllowAccel { get; set; } = true;

        public override FunctionUserLimit UserLimit => FunctionUserLimit.Owner;

        public PicaCraftStationFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaCraftStationFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var abm = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            switch (abm.Action)
            {
                case 0://开始加工
                    StartCrafting(character.Owner, ownerObject, ProtoHelp.GetObject<IDMessage>(abm.Bytes).Id);
                    break;
                case 1://收取奖励
                    CollectCraftItem(character.Owner, ownerObject, ProtoHelp.GetObject<Int32Message>(abm.Bytes).Value);
                    break;
                case 2://加速加工
                    AccelCraft(character.Owner, ownerObject, ProtoHelp.GetObject<Int32Message>(abm.Bytes).Value);
                    break;
                case 3://改变排序
                    ChangeSortDirection(character.Owner, ownerObject, ProtoHelp.GetObject<Int32Message>(abm.Bytes).Value);
                    break;
            }
        }

        public override void OnOwnerPlace(SceneObject building)
        {
            base.OnOwnerPlace(building);
            var record = GetRecord(building,true);
            record.InitSlots(SlotNum, CrownSlotNum);
            record.UpdateToSceneService(building);
            building.Save();
        }

        void StartCrafting(Character character, SceneObject ownerObject, long id)
        {
            var record = GetRecord(ownerObject, true);
            var slot = record.GetEmptySlot();
            if (slot == null)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("队列已满"));
                return;
            }
            var craftType = GameApplication.DataManager.FindValue<CraftItemType>(id);
            if (craftType == null)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("找不到加工配置{0}", id));
                return;
            }
            var lvRecord = ownerObject.GetFunctionRecord<PicaLvUpFunctionRecord>(false);
            if(lvRecord != null && lvRecord.Lv < craftType.UnlockLv)
            {
                character.ShowTipsMessage("建筑等级{0}解锁", craftType.UnlockLv);
                return;
            }
            //检查材料
            foreach (var req in craftType.Requirement)
            {
                var vc = GameApplication.DataManager.FindValue<VirtualCurrencyType>(req.Type);
                if (vc != null)
                {
                    if (!character.CheckVirtualCurrencyCountEnough(req.Type, req.Count))
                    {
                        character.ShowLackOfCurrency(req.Type);
                        return;
                    }
                    continue;
                }
                var property = GameApplication.DataManager.FindValue<ActorPropertyType>(req.Type);
                if(property != null)
                {
                    if (!character.CheckPropertyCountEnough(req.Type, req.Count))
                    {
                        if(req.Type == GameConfig.BaseConfig.ActorEnergyPropertyType) character.ShowLackOfEnergy();
                        else character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, I18nManager.ActorPropertyName(req.Type));
                        return;
                    }
                    continue;
                }
                if (!character.CheckEntityCountEnough(req.Type, req.Count))
                {
                    character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000069, I18nManager.ItemName(req.Type));
                    return;
                }
            }
            //消耗材料
            foreach (var req in craftType.Requirement)
            {
                var vc = GameApplication.DataManager.FindValue<VirtualCurrencyType>(req.Type);
                if (vc != null)
                {
                    character.Pay(req.Type, req.Count, "加工");
                    continue;
                }
                var property = GameApplication.DataManager.FindValue<ActorPropertyType>(req.Type);
                if (property != null)
                {
                    character.ConsumeProperty(req.Type, req.Count, "加工");
                    continue;
                }
                character.DeleteEntity(req.Type, req.Count, "加工");
            }
            //设置加工
            slot.ItemId = id;
            slot.EndTime = DateTimeExtension.NowTimeStamp + craftType.CraftTime*60;
            record.UpdateTo(character, ownerObject);
            ownerObject.ChangeStatus(1, "open");
            ownerObject.Save();
            //if (Constants.AllowTaLog)
            //{
            //    Logger.TA.WriteJson(new LeiTingLogEvent(character.ID.ToString(), character.Name, character.ThisLoginIP, "craft_start")
            //    {
            //        Properties = character.GetLeiTingLogProperties()
            //        .Add(ownerObject.GetTADictionary())
            //        .Add("craft_item_id", id.ToTaLogID())
            //        .Add("craft_item_slot", record.Slots.IndexOf(slot))
            //        .Add(character.SceneCharacter.CurrentScene.GetTADictionary())
            //    });
            //}
        }

        void CollectCraftItem(Character character, SceneObject ownerObject, int index)
        {
            var record = GetRecord(ownerObject, true);
            var slot = record.Slots[index];
            if(slot.ItemId == 0)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("槽位为空"));
                return;
            }
            if (slot.EndTime > DateTimeExtension.NowTimeStamp)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("加工未完成"));
                return;
            }
            var craftType = GameApplication.DataManager.FindValue<CraftItemType>(slot.ItemId);
            slot.ItemId = 0;
            slot.EndTime = 0;
            ownerObject.Save();
            record.UpdateTo(character, ownerObject);
            character.GetAwards(craftType.Awards, "加工完成");

        }

        void AccelCraft(Character character, SceneObject ownerObject, int index)
        {
            if (!AllowAccel) return;
            var record = GetRecord(ownerObject, true);
            var slot = record.Slots[index];
            if (slot.ItemId == 0)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("槽位为空"));
                return;
            }
            var lastTime = slot.EndTime - DateTimeExtension.NowTimeStamp;
            if (lastTime <= 0)
            {
                character.ShowTipsMessage(PicaLocalityStrings.GetUndefinedString("加工已完成"));
                return;
            }
            var cost = (int)Math.Ceiling(lastTime / 600f);
            if (!character.CheckVirtualCurrencyCountEnough(GameConfig.BaseConfig.DiamondType, cost))
            {
                character.ShowLackOfCurrency(GameConfig.BaseConfig.DiamondType);
                return;
            }
            character.Pay(GameConfig.BaseConfig.DiamondType, cost, "加速加工");
            slot.EndTime = DateTimeExtension.NowTimeStamp;
            record.UpdateTo(character, ownerObject);
            ownerObject.Save();
        }

        void ChangeSortDirection(Character character, SceneObject ownerObject, int sortDirection)
        {
            var record = GetRecord(ownerObject, true);
            record.SortDirection = sortDirection;
            record.UpdateToClient(character, ownerObject);
            ownerObject.Save();
        }
    }
}
