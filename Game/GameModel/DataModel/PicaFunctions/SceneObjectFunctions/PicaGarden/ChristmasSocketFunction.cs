using HelpBase;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Functions.SceneObjects
{
    public class ChristmasSocketFunction : PicaPublicAwardTreeFunction
    {
        [JsonMember]
        public int Lease { get; set; }

        protected override bool RelateToTag => false;

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {            
            var msg = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            if (msg.Action == 1)
            {
                if (DateTimeExtension.Now.PassedDays(ownerObject.BirthDate) >= Lease)
                {
                    //过期了
                    return;
                }
                var record = GetRecord(ownerObject, true);
                if (record.TodayTouchedPlayer.Contains(character.OwnerID)) return;//摸过了
                base.Use(character, ownerObject, protoBytes, mode);
                if (!record.TodayTouchedPlayer.Contains(character.OwnerID))
                {
                    record.TodayTouchedPlayer.Add(character.OwnerID);//无论是否是主人都加入列表
                    record.UpdateTo(character.Owner, ownerObject);
                }
            }
            else
            {
                base.Use(character, ownerObject, protoBytes, mode);
            }
        }
    }
}
