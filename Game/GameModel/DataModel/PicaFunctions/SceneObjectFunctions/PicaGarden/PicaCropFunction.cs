using GameModel.Managers;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HelpBase.Json;

namespace GameModel.Functions.SceneObjects
{
    public class PicaCropFunction : UseableSceneObjectPropFunction, ISpecialNameFunction
    {
        public override FunctionUserLimit UserLimit => FunctionUserLimit.Owner;

        [JsonMember]
        public bool IsHuge { set; get; }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
           
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var so = owner as SceneObject;
            if (Constants.useOldCrop)
            {
                if (so.InteractionPoint != null && so.Area.FindObject(so.InteractionPoint.TargetID) == null)
                {
                    so.Delete(1, "清除问题作物");
                }
            }
            else
            {
                so.Delete(1, "清除旧作物");
            }
        }

        public string FindOwnerName(long ownerTypeId)
        {
            var cropType = GameApplication.DataManager.FirstValue<GardenCropType>(gc => gc.CropId == ownerTypeId);
            return cropType?.Name;
        }
    }
}
