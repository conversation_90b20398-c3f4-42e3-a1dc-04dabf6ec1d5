using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Functions.SceneObjects
{

    public class PicaOutPutFunction : UseableSceneObjectPropFunction, INeedRecordFunction<PicaOutPutFunctionRecord>
    {

        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> Count { get; set; }
        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> Max { get; set; }
        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public long Time { get; set; }

        [JsonMember]
        public long OutReward { get; set; }
        [JsonMember]
        public string Method { get; set; }
        [JsonMember]
        public string Show { get; set; }
        [JsonMember(FormatType = typeof(JsonListFormat<int>))]
        public List<int> OutTime { get; set; }
        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> Reduce { get; set; }

        [JsonMember]
        public string Anime { get; set; }
        //
        public override void OnOwnerCreate(IFunctionOwner owner, int createDepth = 0)
        {
            base.OnOwnerCreate(owner, createDepth);
            var building = owner as SceneObject;
            GetRecord(owner, true);
            building.Save();
        }
        //返回一个record
        public PicaOutPutFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<PicaOutPutFunctionRecord>(createIfNull);
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {

            if (character.ID != ownerObject.OwnerID)
            {
                character.Owner.ShowErrorTipsMessage(PicaLocalityStrings.PKT_SYS2_0000002);
                return;
            }
            var abm = ProtoSerializer.Deserialize<NoteData>(protoBytes);
            switch (abm.Action)
            {
                case 0://产出
                    OutPut(character, ownerObject);
                    break;

                default:
                    character.Owner.ShowError(GameModel.PicaLocalityStrings.PKT_NSYS0000082);
                    return;
            }
        }
        public List<AwardSpec> OutPut(SceneCharacter character, SceneObject ownerObject,bool ShowAll=false)
        {
            long type = GetRecord(ownerObject).Type;
            var room = ownerObject.Area.FindObject(type);
            var rs = new List<AwardSpec>();
            if (room != null)
            {
                var status = room.GetFunctionRecord<PicaLiveStockRoomFunctionRecord>(false).Statuses;
                foreach (var item in status)
                {
                    if (item.LiveStockID == ownerObject.ID)
                    {
                        if (item.CanGather)
                        {
                           
                            //   item.LastTimestamp = DateTimeExtension.NowTimeStamp;

                            int c = Calculate(item.lastProduceTime, item.foodTime, OutTime, DateTimeExtension.Now);

                            int f = Ccount((int)(c / 100), item.Love, item.NowProduce, Count, Max);
                            Logger.Warning.Write("动物产出玩家id:"+ ownerObject.OwnerID+"类型:" + item.LiveStockTypeID + "生产值:"+c+"实际产出:"+f+"上次产出时间:"+ item.lastProduceTime+"上次喂食时间:"+ item.foodTime);
                            
                            if (f > 0)
                            {
                                if (c >= 100)
                                {
                                    item.lastProduceTime = DateTimeExtension.NowTimeStamp;
                                    item.LastTimestamp = DateTimeExtension.NowTimeStamp;
                                    item.produceNum += c;
                                    item.produceNum = item.produceNum % 100;
                                }
                                item.CanGather = false;
                                item.NowProduce += f;
                                ObjectAward oa = new ObjectAward(Type, f);
                               var rs2= character.Owner.GetAwards(oa, "牲畜产出奖励");
                                character.Owner.CheckUpdateAction(character.Owner, UserAction.庭舍动物收获, ownerObject.Type, value : f, sceneID: character.CurrentSceneID);
                                StringMessage sm = new StringMessage();
                                sm.Msg = ownerObject.ID + "%" + Anime;
                                character.Owner.SendGoogleProto(ClientMethod.OutPutAnime, sm);
                                if(!ShowAll)
                                {
                                    var dataTosend = new HighLightAwardSpecs();
                                    dataTosend.Awards.AddRange(rs2.Select(s => s.ProtoData).ToList());
                                    character.Owner.SendGoogleProto(ClientMethod.HighLightAwardSpecs, dataTosend);
                                }
                                else
                                {
                                    foreach (var tt in rs2)
                                    {
                                        rs.Add(tt);
                                    }
                                }
                              

                            }
                            room.Save();

                        }
                        else
                        {
                            //不可收货
                        }
                    }
                }
                room.GetFunctionRecord<PicaLiveStockRoomFunctionRecord>(false).UpdateTo(character.Owner, room);
            }
            else
            {
                Logger.Warning.Write("找不到畜生小屋" + type + "角色:" + character.ID);
            }
            return rs;
        }

        /// <summary>
        /// 计算从 lastProduceTime 到现在累计产出多少生产值
        /// </summary>
        /// <param name="lastTime">上次结算时间</param>
        /// <param name="foodTime">上次喂食时间。注意喂食的时候会结算一次，所以 lasttime >= foodtime</param>
        /// <param name="outTime">间隔 n 天没有喂食的话对应的每小时生产值</param>
        /// <returns></returns>
        public static int Calculate(long lastProduceTime, long foodTime, List<int> outTime, DateTime now)
        {
            var list = outTime.ToList();
            list.Insert(0, list[0]); // 注意这边第一天其实不会有衰减。所以加一个元素
            var lastTime = DateTimeExtension.ConvertFromTimestamp(lastProduceTime);
            var totalCuts = DateTimeExtension.PassedDays(now, lastTime);

            var startIndex = DateTimeExtension.PassedDays(lastTime, DateTimeExtension.ConvertFromTimestamp(foodTime));

            if (totalCuts == 0)
            {
                // 同一天
                return (int)(FindProduceValueOnDateX(list, startIndex) * (DateTimeExtension.ConvertToTimestamp(now) - lastProduceTime) / 3600.0);
            }
            else
            {
                var firstStage = FindProduceValueOnDateX(list, startIndex) * lastTime.HoursToNextDay();
                var lastStageIndex = startIndex + totalCuts;
                var lastStage = FindProduceValueOnDateX(list, lastStageIndex) * (24 - now.HoursToNextDay());
                var middle = 0;
                for (int i = 0; i < totalCuts - 1; i++)
                {
                    var index = startIndex + 1 + i;
                    middle += FindProduceValueOnDateX(list, index) * 24;
                }

                return (int)(firstStage + lastStage + middle);
            }
        }

        private static int FindProduceValueOnDateX(List<int> outTime, int dateIndex)
        {
            //if (dateIndex < 0) return 0;
            if (dateIndex < 0) return outTime[0];
            if (dateIndex >= outTime.Count) return 0;
            return outTime[dateIndex];
        }



        public int Ccount(int c, long love, int nowproduce, List<int> count, List<int> max)
        {
            int index = 0;
            if (love >= 70 && love <= 80)
            {
                index = 1;
            }
            if (love >= 80 && love <= 90)
            {
                index = 2;
            }
            if (love >= 90 && love <= 100)
            {
                index = 3;
            }
            c *= count[index];
            if (c + nowproduce > max[index])
            {
                c = max[index] - nowproduce;
            }
            return c;
        }

        public static long PerProduceTime(long lastProduceTime, long foodTime, List<int> outTime, DateTime now)
        {
            var list = outTime.ToList();
            list.Insert(0, list[0]); // 注意这边第一天其实不会有衰减。所以加一个元素
            var totalCuts = DateTimeExtension.PassedDays(now, DateTimeExtension.ConvertFromTimestamp(foodTime));
            var produceValue = FindProduceValueOnDateX(list, totalCuts);
            if (produceValue == 0) return 0;
            return 100 * 3600 / produceValue;
        }
    }
}