using GameModel.Dimensions.Triathlon;

namespace GameModel.Functions.SceneObjects
{
    public abstract class TriathlonFunctionBase : UseableSceneObjectPropFunction
    {
        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            if (character.GamePlayer is not TriathlonPlayer player)
            {
                return;
            }
            if (!player.CurrentScene.IsInGaming())
            {
                return;
            }
            TriathlonUse(player, ownerObject, protoBytes);
        }

        protected abstract void TriathlonUse(TriathlonPlayer player, SceneObject ownerObject, byte[] protoBytes);
    }

    //public abstract class TriathlonFunctionBase<T> : UseableSceneObjectPropFunction where T : TriathlonFunctionRecordBase
    //{
    //    private T _record;

    //    public T GetFunction(SceneObject owner)
    //    {
    //        if (_record == null)
    //        {
    //            _record = owner.GetFunctionRecord<T>(true);
    //        }
    //        return _record;
    //    }
    //}
}
