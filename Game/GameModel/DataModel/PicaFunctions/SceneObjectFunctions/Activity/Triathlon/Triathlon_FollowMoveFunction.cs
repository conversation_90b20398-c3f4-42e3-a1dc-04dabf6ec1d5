using GameModel.Dimensions.Triathlon;
using HelpBase.Json;
using System.Numerics;

namespace GameModel.Functions.SceneObjects
{
    /// <summary>
    /// 跟随物件移动
    /// </summary>
    public class Triathlon_FollowMoveFunction : TriathlonFunctionBase, INeedRecordFunction<Triathlon_FollowMoveFunctionRecord>, INeedTriathlonCollision
    {
        [JsonMember]
        public bool NeedTriggerEnter { get; set; } = true;

        [JsonMember]
        public bool NeedTriggerLeave { get; set; } = true;

        public Triathlon_FollowMoveFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<Triathlon_FollowMoveFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            GetRecord(owner, true);
        }

        protected override void TriathlonUse(TriathlonPlayer player, SceneObject ownerObject, byte[] protoBytes)
        {

        }
    }
}
