using GameModel.Award;
using GameModel.Dimensions.ExpelNien;
using HelpBase.Json;
using System.Numerics;

namespace GameModel.Functions.SceneObjects
{
    /// <summary>
    /// 年兽大作战- 年兽
    /// </summary>
    public class ExpelNienNienMonsterFunction : SceneObjectFunction, INeedRecordFunction<ExpelNienNienMonsterFunctionRecord>
    {
        [JsonMember(FormatType = typeof(Vector3Format))]
        public Vector3 EndOffsetDistance { get; set; }

        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan ToEndDuration { get; set; }

        [JsonMember]
        public float Hp { get; set; }

        [JsonMember(FormatType = typeof(JsonDataFormat<ExpelNienIntervalRange>))]
        public ExpelNienIntervalRange AttackIntervalRange { get; set; }

        [JsonMember]
        public float Attack { get; set; }

        [JsonMember]
        public bool IsBoss { get; set; }

        [JsonMember]
        public float VolumeRatio { get; set; }

        public ExpelNienNienMonsterFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<ExpelNienNienMonsterFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var rc = GetRecord(owner, true);
            rc.Hp = Hp;
        }

        //public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        //{
        //    if (character.CurrentScene is not ExpelNienScene scene)
        //    {
        //        return;
        //    }
        //    if (character.GamePlayer is not ExpelNienPlayer player)
        //    {
        //        return;
        //    }


        //}
    }

    public sealed class ExpelNienIntervalRange : JsonFormatObject
    {
        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan MinimumInterval { get; set; }

        [JsonMember(FormatType = typeof(TimeSpanFormat))]
        public TimeSpan MaximumInterval { get; set; }
    }
}
