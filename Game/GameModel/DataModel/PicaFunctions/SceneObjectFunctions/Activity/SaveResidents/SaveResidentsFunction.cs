using GameModel.Activities;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions.SceneObjects
{
    public class SaveResidentsFunction : UseableSceneObjectPropFunction, INeedRecordFunction<SaveResidentsFunctionRecord>
    {
        [JsonMember]
        public int MaxHp { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<float>))]
        public List<float> AttackHpRatio { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<long>))]
        public List<long> HenShinSuitIds { get; set; }

        public SaveResidentsFunctionRecord GetRecord(IFunctionOwner owner, bool createIfNull = false)
        {
            return owner.GetFunctionRecord<SaveResidentsFunctionRecord>(createIfNull);
        }

        public override void OnOwnerInitialize(IFunctionOwner owner)
        {
            base.OnOwnerInitialize(owner);
            var record = GetRecord(owner, true);
            record.CurrentHp = MaxHp;
        }

        public override void Use(SceneCharacter character, SceneObject ownerObject, byte[] protoBytes, ClientNotificationMode mode = ClientNotificationMode.New)
        {
            var abm = ProtoHelp.GetObject<ActionBytesMessage>(protoBytes);
            var act = GameApplication.ActivityManager.FirstActivity<SaveResidentsActivity>();
            if (act == null) return;

            var rc = GetRecord(ownerObject);
            if (rc.CurrentHp <= 0) return;

            if (character.Owner.TempEquipSet > 0 && HenShinSuitIds.Contains(character.Owner.TempEquipSet))
            {
                character.Owner.ShowTipsMessage("被变异了, 请先找花朵镇长恢复");
                return;
            }

            var actRc = act.GetCharacterRecord(character.Owner, true, true);
            var attack = act.Item1Attack;
            if (abm.Action == 2) attack = act.Item2Attack;
            
            lock (rc)
            {
                actRc.AddDamage(ownerObject.ID, attack);
                rc.CurrentHp -= attack;
                rc.UpdateToSceneService(ownerObject);
                if (rc.CurrentHp <= 0)
                {
                    act.Settlement(ownerObject.Scene, ownerObject.ID, MaxHp);

                    var fs = ownerObject.Scene as PicaFixedProductScene;
                    var fp = fs.FindFixedPoint(ownerObject.ID);
                    fp.DestroyProductObject(true, 2);
                    return;
                }
                foreach (var item in AttackHpRatio)
                {
                    if (rc.CurrentHp / (float)MaxHp <= item && !rc.AttackList.Contains(item))
                    {
                        rc.AttackList.Add(item);
                        //镇民开始攻击
                        ownerObject.DoAction("idle-attack");

                        var contestants = ownerObject.Area.Players.Values.Where(p =>
                        { 
                            var prc = act.GetCharacterRecord(p.Owner, false, false);
                            return prc != null && prc.SceneObjectId == ownerObject.ID;
                        });
                        foreach (var con in contestants.Randoms(Math.Max(1, contestants.Count() / 2)))
                        {
                            con.Owner.OnSuit(HenShinSuitIds.Random(), "疯狂镇民", 10);
                        }

                        break;
                    }
                }
            }
        }
    }
}
