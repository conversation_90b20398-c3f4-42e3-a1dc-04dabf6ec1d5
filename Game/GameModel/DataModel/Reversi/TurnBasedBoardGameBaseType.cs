using HelpBase;
using HelpBase.Linq;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.TurnBasedBoardGameBase
{
    //供回合制棋类游戏使用的基类
    public abstract class TurnBasedBoardGameBaseType<TPieceType,BoardGameData>
        where BoardGameData : ProtoObject, BoardGameProto
    {
        //基础属性
        public abstract int BoardSize { get; }
        protected int GameId;
        public List<BoardGameCharacter> Characters { get; set; } = new();
        public List<Character> Observers { get; set; } = new();
        protected TPieceType[,] Board;

        //游戏流程控制
        protected BoardGameCharacter CurrentPlayer { get; set; }
        protected DateTime LastCheckTime { get; set; }
        protected DelayAwaiter GameDelayAwaiter;
        protected IntVector2Data LastVector { get; set; }
        public bool GameOver { get; set; }

        protected abstract ClientMethod  UpdateBoardGameMethod { get; }

        //通用方法
        public TurnBasedBoardGameBaseType(IEnumerable<Character> characters, int index, double remaintime = 10)
        {
            GameId = index;

            Characters.AddRange(characters.Select(p => new BoardGameCharacter()
            {
                Character = p,
                RemainingTime = TimeSpan.FromMinutes(remaintime)
            }));
            characters.ForEach(p => p.BoardGameIndex = GameId);
            Init();
        }

        protected virtual void Init()
        {
            //初始化棋盘
            InitBoard();

            //初始化玩家
            var character = Characters.Random();
            character.IsBlack = true;
            UpdateToClient(null, true, false);
            GameDelayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(2), () =>
            {
                ChangeCurrentPlayer(character);
                UpdateToClient(null,false,true);
            });
        }

        protected abstract void InitBoard();
        protected void ChangeCurrentPlayer(BoardGameCharacter character)
        {
            if (CurrentPlayer != null)
            {
                var ts = DateTimeExtension.Now - LastCheckTime;
                CurrentPlayer.RemainingTime -= ts;
            }
            CurrentPlayer = character;
            LastCheckTime = DateTimeExtension.Now;
            Interlocked.Exchange(ref GameDelayAwaiter, null)?.Cancel();
            //Logger.Debug.Write($"当前玩家{CurrentPlayer.Character.Name},剩余时间{CurrentPlayer.RemainingTime.ToString(@"mm\:ss")}");
            GameDelayAwaiter = TimerManager.Delay(CurrentPlayer.RemainingTime, () =>
            {
                var winner = Characters.FirstOrDefault(p => p != CurrentPlayer);
                SendGameOver(winner.Character.ID, winner.Character.Name);
            });
        }

        protected virtual void UpdateToClient(Character character = null, bool sendCharacters = false, bool needAllBoard = false)//character观察者，无则更新发送给所有玩家；sendCharacters是否更新玩家信息;needAllBoard是否需要所有棋盘
        {

            var data = GetBoardUpdateData(needAllBoard);//获取棋局数据
            //添加棋手数据
            if (sendCharacters)
            {
                data.Characters.AddRange(Characters.Select(p => p.ProtoData));
            }
            if (CurrentPlayer != null)//棋局开始
            {
                data.CurrentCharacterId = CurrentPlayer.Character.ID;
                var ts = DateTimeExtension.Now - LastCheckTime;
                data.RemainingTime = (CurrentPlayer.RemainingTime - ts).TotalSeconds;
            }

            if (character == null)
            {
                Characters.ForEach(p =>
                {
                    p.Character.SendGoogleProto(UpdateBoardGameMethod, data);
                });
                data.IsObserver = true;
                Observers.ForEach(p =>
                {
                    p.SendGoogleProto(UpdateBoardGameMethod, data);
                });
            }
            else
            {
                if (Observers.Contains(character))
                {
                    data.IsObserver = true;
                }
                character.SendGoogleProto(UpdateBoardGameMethod, data);
            }
        }

        protected abstract BoardGameData GetBoardUpdateData(bool needAllBoard = false);

        protected virtual void SendGameOver(long id, string name)
        {
            GameOver = true;
            Interlocked.Exchange(ref GameDelayAwaiter, null)?.Cancel();
            //GameApplication.BoardGameManager.RemoveChessGame(GameId);

            Characters.ForEach(p =>
            {
                p.Character.BoardGameIndex = 0;
                p.Character.SendGoogleProto(ClientMethod.ShowBoardGameResult, new IDStringMessage { Id = id , Value = name });
            });
            Observers.ForEach(p =>
            {
                p.BoardGameIndex = 0;
                p.SendGoogleProto(ClientMethod.ShowBoardGameResult, new IDStringMessage { Id = id, Value = name });
            });
        }

        public virtual bool HasCharacter(Character character)
        {
            if (Characters.FirstOrDefault(p => p.Character == character) == null)
            {
                if (Observers.FirstOrDefault(p => p == character) == null)
                {
                    return false;
                }
            }
            return true;
        }

        public virtual void Observe(Character character)
        {
            if (!Observers.Contains(character))
            {
                character.BoardGameIndex = GameId;
                Observers.Add(character);
            }
            UpdateToClient(character, true, true);
        }

        public void Quit(Character character)
        {
            if (Observers.Contains(character))
            {
                Observers.Remove(character);
                character.BoardGameIndex = 0;
                return;
            }
            if (GameOver) return;
            var winer = Characters.FirstOrDefault(p => p.Character !=  character);
            SendGameOver(winer.Character.ID, winer.Character.Name);
        }

        public void Message(Character character, IDInt32x2BoolMessage data)
        {
            if (GameOver) return;
            if (Characters.FirstOrDefault(p => p.Character == character) != null)
            {
                Characters.ForEach(p =>
                {
                    p.Character.SendGoogleProto(ClientMethod.BoardGameMessage, data);
                });
            }
        }

        public virtual void FallPiece(Character character, IntVector2Data data)
        {
            if (GameOver) return;
            if (character.ID == CurrentPlayer.Character.ID)//落子者是否正确
            {
                if (ValidatePosition(data))
                {
                    LastVector = new IntVector2Data() { X = data.X, Y = data.Y };

                    ProcessMove(character,data);
                }
                else
                {
                    character.ShowErrorTipsMessage("当前位置无法落子");
                }
            }
        }

        public virtual bool ValidatePosition(IntVector2Data data)//判断落子位置是否合法
        {
            return true;
        }

        public virtual void ProcessMove(Character character, IntVector2Data data)//落子后的处理
        {
        }
    }
    public class BoardGameCharacter//游戏参与者
    {
        public Character Character;
        public bool IsBlack;
        public TimeSpan RemainingTime;

        public BoardGameCharacterData ProtoData
        {
            get
            {
                var data = new BoardGameCharacterData();
                data.Character = Character.SpecData;
                data.ChessType = IsBlack ? (byte)1 : (byte)2;
                data.RemainingTime = RemainingTime.TotalSeconds;
                return data;
            }
        }
    }
}

