using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using DataBase;
using HelpBase.Collections.Concurrent;
using HelpBase.Json;
using HelpBase.Linq;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID))]
    public class NpcShopItemData : AbstractEntity
    {
        private Character owner;

        /// <summary>
        /// 所属角色 <see cref="Character"/> 标识
        /// </summary>
        [TableColumn]
        public long OwnerID { get; set; }

        /// <summary>
        /// 商品id <see cref=""/> 标识
        /// </summary>
        [TableColumn]
        public long Type { get; set; }

        /// <summary>
        /// 已购次数
        /// </summary>
        [TableColumn]
        public int Count { get; set; }

        /// <summary>
        /// 限购类型
        /// </summary>
        [TableColumn]
        public LimitType BuyLimitType { get; set; }

        public NpcShopItemType TypeData
        {
            get { return GameApplication.DataManager.FindValue<NpcShopItemType>(Type); }
        }

        /// <summary>
        /// 所属角色
        /// </summary>
        public Character Owner
        {
            get => owner ??= GameApplication.DataManager.FindValue<Character>(OwnerID);
            set => owner = value;
        }

        
    }

    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OwnerID))]
    public class NpcShopDailyItemList: AbstractEntity
    {
        private Character owner;

        /// <summary>
        /// 所属角色 <see cref="Character"/> 标识
        /// </summary>
        [TableColumn]
        public long OwnerID { get; set; }

        [TableColumn (DBType = "VarChar(Max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> Items { get; set; } = new List<long> ();

        [TableColumn]
        public int RefreshTimes { get; set; }
        [TableColumn]
        public int Group { get; set; }

        /// <summary>
        /// 所属角色
        /// </summary>
        public Character Owner
        {
            get => owner ??= GameApplication.DataManager.FindValue<Character>(OwnerID);
            set => owner = value;
        }
    }
}
