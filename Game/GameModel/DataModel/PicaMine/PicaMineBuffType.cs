using DataBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    /// <summary>
    /// 矿洞房间数据
    /// </summary>
    [EntityTable(DbGroup.Type, CachePolicy.Full)]
    public class PicaMineBuffType : AbstractTypeEntity, ICacheSelf
    {
        /// <summary>
        /// buffid
        /// </summary>
        [TableColumn]
        public long BuffId { get; set; }

        /// <summary>
        /// 持续时间
        /// </summary>
        [TableColumn]
        public int Time { get; set; }

        /// <summary>
        /// 增加的数值
        /// </summary>
        [TableColumn]
        public int Value { get; set; }

        /// <summary>
        /// buff类型
        /// </summary>
        [TableColumn]
        public int BuffState { get; set; }

        /// <summary>
        /// 获得buff时的提示
        /// </summary>
        [TableColumn]
        public string MessageOnAdd { get; set; }

        /// <summary>
        /// buff消失时的提示
        /// </summary>
        [TableColumn]
        public string MessageOnRemoval { get; set; }

        /// <summary>
        /// 被无敌保护时的提示(减益状态的buff才会有)
        /// </summary>
        [TableColumn]
        public string MessageOnImmune { get; set; }
    }
}
