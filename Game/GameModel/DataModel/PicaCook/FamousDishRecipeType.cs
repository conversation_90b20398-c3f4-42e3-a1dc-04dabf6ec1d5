using DataBase;
using GameModel.Award;
using GameModel.Utility;
using HelpBase.Json;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    public class FamousDishRecipeType : TypeDataObject, ICacheSelf, IProtoObject<FamousDishRecipeTypeData>
    {
        [TableColumn]
        public string Sn { get; set; }

        [TableColumn]
        public long GallerySet { get; set; }

        [TableColumn]
        public int Grade { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<TypeCount>))]
        public List<TypeCount> Materials { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<int>))]
        public List<int> ProficiencyGrowValue { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<int>))]
        public List<int> AwardLevels { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<ObjectAward>))]
        public List<ObjectAward> Awards { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> CookMachine { get; set; }

        [TableColumn]
        public int Cost { get; set; }

        [TableColumn]
        public long CookingTime { get; set; }

        [TableColumn(DBType = "varchar(max)", FormatType = typeof(DynamicCollectionFormat<PicaCondition, JsonDynamicList<PicaCondition>>))]
        public JsonDynamicList<PicaCondition> Conditions { set; get; }

        public FamousDishRecipeTypeData ProtoData
        {
            get
            {
                var data = new FamousDishRecipeTypeData();
                data.Id = ID;
                data.Sn = Sn;
                data.GallerySet = GallerySet;
                data.Grade = Grade;
                data.Cost = Cost;
                data.CookingTime = CookingTime;

                if (Materials?.Count > 0) data.Materials.AddRange(Materials.Select(p => p.ProtoData));
                if (AwardLevels != null) data.AwardLevels.AddRange(AwardLevels);
                if (Awards != null) data.Awards.AddRange(Awards.SelectMany(a => a.GetAwardPreview().Select(p => p.ProtoData)));
                if (CookMachine != null) data.CookMachine.AddRange(CookMachine);

                return data;
            }
        }
    }

    
}
