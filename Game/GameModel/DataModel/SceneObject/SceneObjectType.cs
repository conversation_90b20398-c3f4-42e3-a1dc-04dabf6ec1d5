using DataBase;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Type, typeof(EntityType))]
    public class SceneObjectType : EntityType, ICacheParent<EntityType>, IOwnedObject, IProtoObject<SceneObjectTypeData>
    {
        public override EntityName EntityName => EntityName.SceneObject;

        #region I18n
        private string _name;

        public override string Name
        {
            get
            {
                if (_name == null)
                {
                    if (Functions != null)
                    {
                        foreach (var func in Functions)
                        {
                            if (func is ISpecialNameFunction f)
                            {
                                try
                                {
                                    var d = f.FindOwnerName(ID);
                                    if (d != null)
                                    {
                                        _name = d;
                                        return _name;
                                    }
                                }
                                catch
                                {
                                    Logger.Error.Write($"so {ID} name err");
                                    continue;
                                }
                            }
                        }
                    }

                    _name = I18nManager.SceneObjectNameInternal(ID);
                }
                return _name;
            }
            set
            {
                _name = value;
            }
        }

        private string _desc;
        public override string Description
        {
            get
            {
                if (_desc == null)
                {
                    if (!string.IsNullOrEmpty(DescRef))
                    {
                        _desc = I18nManager.I18n("PKT_REFIF" + DescRef);
                    }
                    else
                    {
                        if (Functions != null)
                        {
                            foreach (var func in Functions)
                            {
                                try
                                {
                                    if (func is ISpecialDescriptionFunction f)
                                    {
                                        var d = f.FindOwnerDescription(ID);
                                        if (d != null)
                                        {
                                            _desc = d;
                                            return _desc;
                                        }
                                    }
                                }
                                catch
                                {
                                    Logger.Error.Write($"so {ID} desc err");
                                    continue;
                                }
                            }
                        }

                        _desc = I18nManager.SceneObjectDescInternal(ID);
                        return _desc;
                    }
                }
                return _desc;
            }
            set
            {
                _desc = value;
            }
        }

        private string _productName;
        public string ProductName
        {
            get
            {
                if (_productName != null)
                {
                    return _productName;
                }
                foreach (var func in Functions)
                {
                    if (func is IHaveProductFunction f)
                    {
                        var pname = f.ProductName;
                        if (!string.IsNullOrEmpty(pname))
                        {
                            _productName = pname;
                            return pname;
                        }
                    }
                }
                _productName = "";
                return _productName;
            }
        }

        /// <summary>
        /// i18n 专用，如果这个不为空，则 Description 从 I18n_Ref_zh （也在 I18nManager 统一管理) 中找对应的描述
        /// </summary>
        [TableColumn]
        public string DescRef { get; set; }


        #endregion

        [TableColumn(DBType = "nvarchar(max)", FormatType = typeof(DynamicCollectionFormat<SceneObjectFunction, JsonDynamicList<SceneObjectFunction>>), ShowIndex = 1003)]
        public JsonDynamicList<SceneObjectFunction> Functions { get; set; } = new JsonDynamicList<SceneObjectFunction>();

        /// <summary>
        /// 叠加上限
        /// </summary>
        [TableColumn(ShowName = "叠加上限", Description = "表示该物品在同一个背包格子里面能叠加的上限 0 表示无上限")]
        public override int Unit { get; set; }

        [TableColumn]
        public int Category { get; set; }

        [TableColumn]
        public bool CanCombine { get; set; }

        [TableColumn]
        public bool CanReforge { get; set; }

        [TableColumn]
        public bool InGacha { get; set; }

        [TableColumn]
        public bool CanPickUp { get; set; } = true;

        [TableColumn]
        public bool Outdoor { get; set; }
        /// <summary>
        /// 这个其实是图鉴的套系 ID
        /// </summary>
        [TableColumn]
        public long GallerySet { get; set; }

        [TableColumn]
        public string Sn { get; set; }

        [TableColumn]
        public int Version { get; set; }

        [TableColumn]
        public string PiVersion { get; set; }

        [TableColumn]
        public string Code { get; set; }

        [TableColumn]
        public int CombineWeightType { get; set; }

        [TableColumn]
        public bool OnTABlackList { set; get; }

        private int _prosperity = 0;

        public int Prosperity
        {
            get
            {
                if (_prosperity == 0)
                {
                    var dex = GameApplication.DataManager.FindValue<GalleryType>(GetGalleryId());
                    if (dex == null)
                    {
                        return 0;
                    }

                    var rareTimes = 0;
                    switch (dex.Quality)
                    {
                        case Quality.一星:
                            rareTimes = 25;
                            break;
                        case Quality.二星:
                            rareTimes = 40;
                            break;
                        case Quality.三星:
                            rareTimes = 60;
                            break;
                        case Quality.四星:
                            rareTimes = 75;
                            break;
                        case Quality.五星:
                            rareTimes = 80;
                            break;
                    }
                    _prosperity = ((int)Quality * rareTimes) + (Rarity == SceneObjectRarity.稀有 ? (int)Quality * 20 : 0);
                }
                return _prosperity;
            }
        }

        #region 客户端数据

        /// <summary>
        /// 包名
        /// </summary>
        [TableColumn(DBType = "nvarchar(300)")]
        public string PrefabBundle { get; set; } = "";

        /// <summary>
        /// 资源名
        /// </summary>
        [TableColumn(DBType = "nvarchar(300)")]
        public string Prefab { get; set; } = "";

        [TableColumn(DBType = "nvarchar(500)")]
        public string IconBundle { get; set; } = "";

        [TableColumn(DBType = "nvarchar(300)")]
        public string Icon { get; set; } = "";

        #endregion 客户端数据

        #region Function

        public bool ExistsFunction<T>() where T : SceneObjectFunction
        {
            if (Functions == null)
            {
                return false;
            }
            return Functions.Exists(p => p is T);
        }

        public bool ExistsFunction<T>(Predicate<T> match) where T : SceneObjectFunction
        {
            if (Functions == null)
            {
                return false;
            }
            return Functions.Exists(p => p is T t && match(t));
        }

        public T GetFunction<T>()
            where T : SceneObjectFunction
        {
            T result = null;
            if (Functions != null)
            {
                foreach (var f in Functions)
                {
                    if (f is T t)
                    {
                        result = t;
                        break;
                    }
                }
            }
            return result;
        }

        public bool TryGetFunction<T>([MaybeNullWhen(false)] out T function)
            where T : SceneObjectFunction
        {
            if (Functions != null)
            {
                for (var i = 0; i < Functions.Count; i++)
                {
                    if (Functions[i] is T value)
                    {
                        function = value;
                        return true;
                    }
                }
            }

            function = null;
            return false;
        }

        public List<T> GetAllFunction<T>()
            where T : SceneObjectFunction
        {
            List<T> result = new List<T>();
            if (Functions != null)
            {
                foreach (var f in Functions)
                {
                    if (f is T t)
                    {
                        result.Add(t);
                    }
                }
            }
            return result;
        }

        public SceneObjectFunction GetFunction(string name)
        {
            return Functions?.Find(p => p.ClassName == name);
        }

        #endregion Function

        [TableColumn(ShowName = "拥有者", DataIndex = DataIndex.Clustered)]
        public long OwnerID { get; set; }

        private Character _owner;

        public virtual Character Owner
        {
            get
            {
                if (_owner == null)
                {
                    if (OwnerID == 0)
                    {
                        return null;
                    }
                    _owner = GameApplication.UserManager.FindCharacter(OwnerID);
                }
                return _owner;
            }
            set
            {
                _owner = value;
            }
        }

        /// <summary>
        /// 建造额度
        /// </summary>
        [TableColumn]
        public int BuildingQuota { get; set; }

        /// <summary>
        /// 稀有度
        /// </summary>
        [TableColumn]
        public SceneObjectRarity Rarity { get; set; }

        /// <summary>
        /// 能否参与合成
        /// 
        /// </summary>
        [TableColumn]
        public bool CanBeCombined { get; set; } = true;

        /// <summary>
        /// 获取的活动ID，没有就是0
        /// </summary>
        [TableColumn]
        public long ActivityID { get; set; } = 0;

        /// <summary>
        /// 家具对应的图文教程ID，没有就是0 
        /// </summary>
        [Obsolete("需求修改，不用了")]
        [TableColumn]
        public long TutorialID { get; set; } = 0;

        public override bool WillTriggerHelpCenter => true;

        public override void TriggerHelpCenter(Character character)
        {
            base.TriggerHelpCenter(character);
            Functions?.ForEach(func =>
            {
                if (func.ShowHelpCenterId == 0)
                {
                    return;
                }

                character.ListDatas.CheckHelpCenterAward(func.ShowHelpCenterId);
            });
        }

        public SceneObjectTypeData ProtoData
        {
            get
            {
                var data = new SceneObjectTypeData();
                data.Id = ID;
                data.Sn = Sn;
                data.Version = Version;
                //data.PrefabBundle = PrefabBundle;
                //data.Prefab = Prefab;
                //data.IconBundle = IconBundle;
                //data.Icon = Icon;
                data.Category = Category;
                data.GallerySet = GallerySet;
                data.BuildingQuota = BuildingQuota;
                data.PiVersion = PiVersion;

                data.Available = Available;
                data.CanCombine = CanCombine;
                data.CanReforge = CanReforge;
                data.Code = Code;
                data.Outdoor = Outdoor;
                data.CanBeCombined = CanBeCombined;
                data.CanTrade = CanTrade;
                data.Activityid = ActivityID;
                data.Tutorialid = TutorialID;
                if (Functions != null)
                {
                    var checkLayer = JsonMemberLayer.ServiceLayer | JsonMemberLayer.ClientLayer;//发送Service|Default|Client 三个层级 不会发送Detail层级
                    foreach (var f in Functions)
                    {
                        if (f.ShowLayer.HasFlag(SceneObjectFunctionShowLayer.ClientLayer) ||
                            f.ShowLayer.HasFlag(SceneObjectFunctionShowLayer.ServerLayer))
                        {
                            data.FunctionStrings.Add(f.ToJson(null, false, checkLayer).ToString());
                        }
                        if (f is HomeTableSkillFunction)
                        {

                        }
                    }
                }

                data.Quality = Quality;
                data.CanPickUp = CanPickUp;

                data.GuidingPrice = GuidingPrice;
                data.TradingScore = TradingScore;
                data.AuctionItemType = AuctionItemType;
                //data.GroupType = GroupType;
                data.Rarity = Rarity;
                data.Unit = Unit;
                data.DescRef = DescRef;
                if (Sources?.Count > 0) data.Sources.AddRange(Sources);
                if (SourceRedirectionFunctions?.Count > 0)
                    data.SourceRedirectionFunctionStrings.AddRange(SourceRedirectionFunctions.Select(p => p.ToJson().ToString()));
                return data;
            }
        }

        public override AuctionItemType AuctionItemType { get => AuctionItemType.家具; set { } }

        public override void Initialize()
        {
            base.Initialize();
            CheckCanAddTableFunction();
        }

        void CheckCanAddTableFunction()
        {
            if (this.Category != Constants.TableCategory) return;
            bool flag = true;
            if (this.Functions.Count != 0)
            {
                foreach (var func in this.Functions)
                {
                    if (func is SignInterface)
                        continue;
                    flag = false;
                }
            }
            if (flag)
            {
                this.Functions.Add(new HomeTableSkillFunction());
            }

        }

        public override long GetGalleryId()
        {
            var foodFunction = GetFunction<PicaEatFoodFunction>();
            if (foodFunction != null)
            {
                if (foodFunction.RecipeId == 0)
                {
                    return 0;
                }
                return GameApplication.DataManager.FindValue<PicaRecipeType>(foodFunction.RecipeId).FoodGalleryId;
            }
            return GallerySet;
        }

        public override void Invalidate()
        {
            base.Invalidate();
            _name = null;
        }

        public bool CanPlace(Area area, out LocalityString error)
        {
            if (Functions?.Count > 0)
            {
                foreach (var function in Functions.OfType<TransformSceneObjectFunction>())
                {
                    if (!function.CanPlace(area, this, out error))
                    {
                        return false;
                    }
                }
            }

            error = null;
            return true;
        }

        public void SetName(string name)
        {
            this._name = name;
        }
    }

    public class SceneObjectInteractioinPoint : JsonFormatObject
    {
        [JsonMember(FormatType = typeof(JsonDataFormat<Transform>))]
        public Transform Transform { get; set; }

        public SceneObjectInteractioinPointData ProtoData
        {
            get
            {
                var data = new SceneObjectInteractioinPointData();
                data.Transform = Transform.ProtoData;
                return data;
            }
        }
    }
}