using DataBase;
using DataBase.UIAssemblies;
using GameModel.Functions.Equip.Avatar;
using GameModel.Managers;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    [EntityTable(DbGroup.Type, typeof(EntityType))]
    public class EquipType : EntityType, ICacheParent<EntityType>, IProtoObject<EquipTypeData>
    {
        private string name;

        public override EntityName EntityName => EntityName.Equip;

        #region I18n
        public override string Name
        {
            get
            {
                if (name == null)
                {
                    name = GenerateName();
                }
                return name;
            }
            set { name = value; }
        }

        private string _finalSuitName;
        public string FinalSuitName
        {
            get
            {
                if (!BaseName.ContainsKey(Part))
                {
                    return "";
                }
                if (name == null)
                {
                    name = GenerateName();
                }
                return _finalSuitName;
            }
        }

        /// <summary>
        /// 用于寻找名字的引用
        /// </summary>
        public long HenshinSuitID { get; set; }
        /// <summary>
        /// 用于寻找名字的引用
        /// </summary>
        public long LuckyBoxHenshinSuitID { get; set; }

        public string RealSuitName
        {
            get
            {
                return string.IsNullOrEmpty(I18nSuitName) ? Suit : I18nSuitName;
            }
        }

        private string GenerateName()
        {
            if (I18nName == 1)
            {
                name = I18nManager.AvatarNameInternal(ID);
                _finalSuitName = name.Replace(Gender.ToString(), "");
                return name;
            }
            else
            {
                if (!BaseName.ContainsKey(Part))
                {
                    return "";
                }
                var partName = string.IsNullOrEmpty(PartAlias) ? BaseName[Part] : PartAlias;//用partname存储不同情况的部位名
                _finalSuitName = RealSuitName + partName;
                var name = _finalSuitName;
                if (Gender != Gender.无)//如果有性别信息
                {
                    if (string.IsNullOrEmpty(PartAlias) && BaseNameWithGender.ContainsKey(Part))//一般部位名称（受性别影响）
                    {
                        partName = BaseNameWithGender[Part];//一般部位名称
                    }
                    name = RealSuitName + Gender + partName;
                }

                var suffix = FindSuffix();
                if (!string.IsNullOrEmpty(suffix))
                {
                    name += $"-{suffix}";
                }

                return name;
            }
        }

        public string FindSuffix()
        {
            if (I18nName == 2 && HenshinSuitID > 0)
            {
                var henshinType = GameApplication.DataManager.FindValue<HenShinSuitType>(HenshinSuitID);
                if (henshinType != null)
                {
                    return henshinType.Suffix;
                }
            }
            return null;
        }

        public string FindLuckyBoxSuffix()
        {
            if (I18nName == 2 && LuckyBoxHenshinSuitID > 0)
            {
                var henshinType = GameApplication.DataManager.FindValue<HenShinSuitType>(LuckyBoxHenshinSuitID);
                if (henshinType != null)
                {
                    return henshinType.Suffix;
                }
            }
            return null;
        }

        private Dictionary<EquipPart, string> BaseName = new Dictionary<EquipPart, string>()
        {
            {EquipPart.Chin, "脸饰"},
            {EquipPart.Cloa, "披风"},
            {EquipPart.Costume, "服装"},
            {EquipPart.Eye, "眼瞳"},
            {EquipPart.Face, "脸饰"},
            {EquipPart.Hair, "头发"},
            {EquipPart.Hat, "头饰"},
            {EquipPart.Mouse, "嘴巴"},
            {EquipPart.Tail, "尾巴"},
            {EquipPart.Weapon, "手持"},
            {EquipPart.Shield, "手持"},
            {EquipPart.Wing, "翅膀"},
            {EquipPart.Back, "背景"},
            {EquipPart.Leglet, "腿饰"},
            {EquipPart.Mask, "面具"},
            {EquipPart.Cyborg, ""},
            {EquipPart.Scar, ""},
        };

        private Dictionary<EquipPart, string> BaseNameWithGender = new Dictionary<EquipPart, string>()
        {
            {EquipPart.Costume, "装"},
            {EquipPart.Hair, "发"},
        };

        private string desc;
        public override string Description
        {
            get
            {
                if (desc == null)
                {
                    if (!string.IsNullOrEmpty(DescRef))
                    {
                        desc = I18nManager.I18n("PKT_REFIA" + DescRef);
                    }
                    else
                    {
                        desc = I18nManager.AvatarDescInternal(ID);
                    }
                }
                return desc;
            }
            set => desc = value;
        }

        /// <summary>
        /// i18n 专用，如果这个不为空，则 Description 从 I18n_Ref_zh （也在 I18nManager 统一管理) 中找对应的描述
        /// </summary>
        [TableColumn]
        public string DescRef { get; set; }

        #endregion

        [TableColumn]
        public EquipPart Part { get; set; }

        /// <summary>
        /// 部件分组
        /// <para>0: 不冲突。</para>
        /// <para>1: 指定部位（<see cref="ReplaceParts"/>）有穿装扮，且穿的装扮的 PartGroup = 1 则冲突。</para>
        /// <para>2: 指定部位（<see cref="ReplaceParts"/>）有穿装扮，则冲突。</para>
        /// <para>3: 不冲突。</para>
        /// </summary>
        public int PartGroup { get; set; }

        public List<EquipReplace> ReplaceParts { get; set; }

        [TableColumn]
        public Gender Gender { get; set; }

        /// <summary>
        /// 如果为 0, 根据标准名字生成逻辑
        /// 如果为 1，则读取 I18n 的名字，
        /// 如果为 2，在 0 的基础上增加套装后缀
        /// </summary>
        [TableColumn]
        public short I18nName { get; set; }

        /// <summary>
        /// 套装名，如果 I18nName为 false，则根据 Suit 生成名字
        /// </summary>
        [TableColumn]
        public string Suit { get; set; }

        /// <summary>
        /// 如果这个数据不为空，那么
        /// 1. 他是欧欧盒，并且
        /// 2. 出现了实际套系和显示名字不一致的情况
        /// 所以他的显示名字应该用这个套系的名字来拼
        /// </summary>
        [TableColumn]
        public string I18nSuitName { get; set; }

        [TableColumn]
        public string PartAlias { get; set; }

        [TableColumn(DBType = "nvarchar(200)")]
        public string Icon { get; set; }

        [TableColumn(DBType = "nvarchar(500)")]
        public string IconBundle { get; set; }

        [TableColumn(DBType = "nvarchar(200)")]
        public string Prefab { get; set; }

        [TableColumn(DBType = "nvarchar(500)")]
        public string PrefabBundle { get; set; }

        [TableColumn]
        public string Texture { get; set; }

        [TableColumn]
        public string TextureBundle { get; set; }

        [TableColumn]
        public string ParentBone { get; set; }

        [TableColumn]
        public int SubType { get; set; }

        [TableColumn]
        public int ShowPart { get; set; }

        [TableColumn]
        public bool HideInBag { get; set; }

        [TableColumn]
        public bool NoHint { get; set; }

        [TableColumn]
        public string Sn { get; set; }

        [TableColumn]
        public int Version { get; set; }

        [TableColumn]
        public string Code { get; set; }

        [TableColumn]
        public string Slot { get; set; }

        [TableColumn]
        public int PiVersion { get; set; }

        [TableColumn(DBType = "varchar(500)", FormatType = typeof(JsonListFormat<string>))]
        public List<string> Actions { get; set; }

        [TableColumn(DBType = "varchar(500)", FormatType = typeof(JsonListFormat<string>))]
        public List<string> PlatformTags { get; set; } = new List<string>();

        public override bool UpdateClientOnGet => !HideInBag;

        [TableColumn(DBType = "nvarchar(max)", FormatType = typeof(DynamicCollectionFormat<EquipAvatarFunction, JsonDynamicList<EquipAvatarFunction>>))]
        public JsonDynamicList<EquipAvatarFunction> Functions { get; set; }

        [TableColumn]
        public string IdleAct { set; get; }

        [TableColumn]
        public bool HasFashionShow { set; get; }

        /// <summary>
        /// 获取的活动ID，没有就是0
        /// </summary>
        [TableColumn]
        public long ActivityID { get; set; } = 0;


        public bool WillHideHair => PlatformTags != null && PlatformTags.Contains("remove_hair");

        public override bool WillTriggerHelpCenter => true;

        public override void TriggerHelpCenter(Character character)
        {
            base.TriggerHelpCenter(character);
            Functions?.ForEach(func =>
            {
                if (func.ShowHelpCenterId == 0)
                {
                    return;
                }
                character.ListDatas.CheckHelpCenterAward(func.ShowHelpCenterId);
            });
        }

        public EquipTypeData ProtoData
        {
            get
            {
                var data = new EquipTypeData();
                data.Id = ID;
                //data.Icon = Icon;
                //data.IconBundle = IconBundle;
                //data.PrefabBundle = PrefabBundle;
                //data.Prefab = Prefab;
                //data.TextureBundle = TextureBundle;
                //data.Texture = Texture;

                data.I18nName = I18nName;
                data.I18nSuitName = I18nSuitName;
                data.DescRef = DescRef;
                data.SuitName = Suit;
                data.PartAlias = PartAlias;

                data.Gender = Gender;
                //data.Quality = Quality;
                data.Available = Available;

                data.NoHint = NoHint;
                data.HideInBag = HideInBag;
                data.Code = Code;

                data.Sn = Sn;
                data.Version = Version;
                data.PiVersion = PiVersion;

                data.Part = Part;
                data.Slot = Slot;
                data.IdleAct = IdleAct;

                data.HasFashionShow = HasFashionShow;

                data.CanTrade = CanTrade;
                data.GuidingPrice = GuidingPrice;
                data.TradingScore = TradingScore;
                data.AuctionItemType = AuctionItemType;

                data.Quality = Quality;
                data.Activityid = ActivityID;

                if (PlatformTags?.Count > 0) data.PlatformTags.AddRange(PlatformTags);
                if (Actions?.Count > 0) data.Actions.AddRange(Actions);
                if (Sources?.Count > 0) data.Sources.AddRange(Sources);

                if (Functions != null)
                {
                    foreach (var f in Functions)
                    {
                        data.FunctionStrings.Add(f.ToJson(null, false).ToString());
                    }
                }
                if (SourceRedirectionFunctions?.Count > 0)
                    data.SourceRedirectionFunctionStrings.AddRange(SourceRedirectionFunctions.Select(p => p.ToJson().ToString()));
                return data;
            }
        }

        public bool CanEquipAsTemp()
        {
            return true;
            //switch (OwnerGroup)
            //{
            //    case OwnerGroup.新手:
            //    case OwnerGroup.初始:
            //    case OwnerGroup.交互:
            //        return true;
            //    default:
            //        return false;
            //}
        }

        //public T GetRoleFunction<T>()
        //    where T : EquipRoleFunction
        //{
        //    if (RoleFunctions != null)
        //    {
        //        foreach (var function in RoleFunctions)
        //        {
        //            if (function is T other)
        //            {
        //                return other;
        //            }
        //        }
        //    }
        //    return default(T);
        //}

        //public T GetFunction<T>()
        //    where T : EquipFunction
        //{
        //    return Functions?.Find(p => p is T) as T;
        //}

        public bool CanEquip(Character character)
        {
            if (character == null)
                throw new ArgumentNullException(nameof(character));

            if (!Available)
            {
                //不可用
                return false;
            }
            //if (Gender != Gender.无 && Gender != character.Gender)
            //{
            //    //性别限制
            //    return false;
            //}
            //var avatarType = character.SceneCharacter.AvatarTypeData;
            //if (avatarType.EquipGroups != null && !avatarType.EquipGroups.Contains(NameGroup))
            //{
            //    //不可用的装扮
            //    return false;
            //}

            return true;
        }

        public override void Invalidate()
        {
            base.Invalidate();
            name = null;
        }

        public sealed class GrowthDescription : JsonFormatObject, IProtoObject<EquipGrowthDescription>
        {
            [JsonMember]
            public AdditionType AdditionType { get; set; }

            [JsonMember]
            public int Value { get; set; }

            public EquipGrowthDescription ProtoData
            {
                get
                {
                    var data = new EquipGrowthDescription();
                    data.AdditionType = AdditionType;
                    data.Value = Value;
                    return data;
                }
            }
        }
    }

    public enum EquipRecordTypeEnum : byte
    {
        None = 0,
        WeaponRecord = 1
    }

    public partial class EquipReplace : JsonFormatObject, IProtoObject<EquipReplaceData>
    {
        [JsonMember]
        public EquipPart Part { get; set; }

        [JsonMember]
        public long Type { get; set; }

        [JsonMember]
        public string Model { get; set; }

        public EquipReplaceData ProtoData
        {
            get
            {
                var data = new EquipReplaceData();
                data.Part = Part;
                data.Type = Type;
                data.Model = Model;
                return data;
            }
        }
    }

    public class EquipSkillSetting : JsonFormatObject
    {
        [JsonMember]
        public long Type { get; set; }
    }
}