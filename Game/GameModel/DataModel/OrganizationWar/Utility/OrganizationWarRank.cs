using DataBase;
using HelpBase.Json;

namespace GameModel
{
    [EntityTable(DbGroup.Entity)]
    [EntityIndex(IndexCategory.NonClustered, nameof(OrganizationID))]
    public class OrganizationWarRank : GameDataObject, ICacheSelf
    {
        /// <summary>
        /// 第几轮的排行
        /// </summary>
        [TableColumn]
        public int Round { get; set; }

        [TableColumn]
        public long OrganizationID { get; set; }

        [TableColumn]
        public long CharacterID { get; set; }

        [TableColumn]
        public int Rank { get; set; }

        [TableColumn(DBType = "varchar(100)")]
        public string Name { get; set; }

        [TableColumn]
        public float Score { get; set; }

        [TableColumn(DBType = "varchar(100)")]
        public DateTime Time { get; set; }

        private Organization _organization;
        public Organization Organization
        {
            get
            {
                return _organization ??= GameApplication.OrganizationManager.TryGetOrganization(OrganizationID);
            }
        }

        private Character _character;
        public Character Character
        {
            get
            {
                return _character ??= GameApplication.UserManager.FindCharacter(CharacterID);
            }
        }
    }
}
