using System;
using System.Json;
using DataBase;
using Proto.GameModel;

namespace GameModel
{
    public partial class Area
    {
        private long type;
        private AreaType typeData;

        /// <summary>
        /// 购买时间
        /// </summary>
        [TableColumn]
        public DateTime BuyTime { get; set; }

        [TableColumn]
        public long Type
        { 
            get => type;
            set
            {
                var oldval = type;
                type = value;
                if (SaveUsage != SaveUsage.NoSave)
                {
                    TypeChanged(oldval, value);
                }
            }
        }

        [TableColumn]
        public string Name { get; set; }

        [TableColumn]
        public SellStatus SellStatus { get; set; }

        [TableColumn]
        public double SellPrice { get; set; }

        public AreaType TypeData
        {
            get => typeData ?? (typeData = (Type > 0 ? GameApplication.DataManager.FindValue<AreaType>(Type) : null));
            set => typeData = value;
        }

        /// <summary>
        /// 是否已经创建过默认资源
        /// </summary>
        //[TableColumn]
        public InitializeStatus InitializeStatus { get; set; } = InitializeStatus.Created;

        public void CheckResource()
        {
            if (InitializeStatus < InitializeStatus.Initialize)
            {
                lock (this)
                {
                    if (InitializeStatus < InitializeStatus.Initialize)
                    {
                        InitializeStatus = InitializeStatus.Initialize;
                        CreateDefaultObjects();
                        Save();
                    }
                }
            }
        }

        void CreateDefaultObjects()
        {
            var arss = GameApplication.DataManager.FindValue<AreaResourceSetting>(p => p.SceneType == Scene.Type && p.X == X && p.Y == Y);
            if (arss.Count > 0)
            {
                foreach (var ars in arss)
                {
                    var transform = (Transform)ars.Transform.Clone();
                    if (!LocalContains(transform.Position.X, transform.Position.Z))
                    {
                        transform.Position = WorldPositionToLocal(transform.Position);
                    }

                    var so = NewSceneObject(Character.System, ars.Type, transform, true,"创建默认物品");
                    if (ars.Data?.Count > 0)
                    {
                        foreach (JsonObject json in ars.Data)
                        {
                            var function = so.GetFunctionRecord(json, false);
                            if (function == null) so.GetFunctionRecord(json, true);
                            else function.Update(json);
                        }
                    }

                    so.Initialize();
                    so.Save();
                    Add(so, false);
                }

                ResetActiveObjectCount();
            }
        }

        public string ValidName => string.IsNullOrEmpty(Name) ? Scene?.Name : Name;
    }
}
