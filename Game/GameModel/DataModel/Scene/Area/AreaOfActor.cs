using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using HelpBase.Linq;

namespace GameModel
{
    public partial class Area
    {
        private ConcurrentDictionary<long, SceneCharacter> _players;

        public ConcurrentDictionary<long, SceneCharacter> Players => _players ??= new ConcurrentDictionary<long, SceneCharacter>();

        public int PlayerCount => Players.Count;

        public SceneCharacter FindPlayer(long id)
        {
            Players.TryGetValue(id, out SceneCharacter re);
            return re;
        }

        public SceneCharacter FindPlayerInAround(long key)
        {
            return Scene.FindPlayer(this, key);
        }

        public IEnumerable<SceneCharacter> FindPlayer(Func<SceneCharacter, bool> predicate)
        {
            if (predicate == null)
                throw new ArgumentNullException(nameof(predicate));

            return Players.Values.Where(predicate);
        }

        public SceneCharacter FirstOrDefault(Func<SceneCharacter, bool> predicate)
        {
            if (predicate == null)
                throw new ArgumentNullException(nameof(predicate));

            return Players.Values.FirstOrDefault(predicate);
        }

        public void AddPlayer(SceneCharacter player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            player.CurrentArea?.RemovePlayer(player);
            Players[player.ID] = player;

            player.OnEnterArea(this);
            OnEntered(player);
        }

        public void RemovePlayer(SceneCharacter player)
        {
            if (player == null) throw new ArgumentNullException(nameof(player));

            Players.TryRemove(player.ID, out _);
            player.OnLeaveArea();
        }
    }
}