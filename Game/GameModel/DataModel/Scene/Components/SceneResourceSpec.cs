using System.Collections.Generic;
using DataBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel
{
    public abstract partial class SceneResourceSpec : TypeDataObject
    {
        [TableColumn(DBType = "nvarchar(500)", ShowName = "名字")]
        public string Name { get; set; } = "小方块";

        /// <summary>
        /// 包名
        /// </summary>
        [TableColumn(DBType = "nvarchar(300)")]
        public string PrefabBundle { get; set; } = "";

        /// <summary>
        /// 资源名
        /// </summary>
        [TableColumn(DBType = "nvarchar(300)")]
        public string Prefab { get; set; } = "";

        [TableColumn(DBType = "VarChar(Max)", FormatType = typeof(JsonListFormat<long>))]
        public List<long> SceneTypes { get; set; }

        [TableColumn]
        public int PerformanceCost { get; set; }

        [TableColumn]
        public int MinLv { get; set; }

        [TableColumn]
        public int MinVIPLv { get; set; }

        [TableColumn]
        public int ShowIndex { get; set; }

        protected virtual T CreateProto<T> ()where T: SceneResourceSpecData,new()
        {
            T botd = new T();
            botd.Id = ID;
            botd.Name = Name;
            botd.Prefab = Prefab;
            botd.PrefabBundle = PrefabBundle;
             
            botd.PerformanceCost = PerformanceCost;
            botd.ShowIndex = ShowIndex;
            botd.MinLv = MinLv;
            botd.MinVIPLv = MinVIPLv;

            if (SceneTypes != null)
            {
                foreach (var st in SceneTypes)
                {
                    botd.SceneTypes.Add(st);
                }
            }
            return botd;
        }
    }

    public abstract partial class BlockResourceSpec:SceneResourceSpec
    {
        [TableColumn]
        public short MinX { get; set; }
        [TableColumn]
        public short MaxX { get; set; }
        [TableColumn]
        public short MinY { get; set; }
        [TableColumn]
        public short MaxY { get; set; }

     
    }
}