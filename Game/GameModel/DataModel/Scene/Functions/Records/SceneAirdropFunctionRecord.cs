using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using GameModel.Functions.SceneObjects;
using HelpBase;
using Proto.GameModel;

namespace GameModel
{
    public class SceneAirdropFunctionRecord : SceneFunctionRecord
    {
        private readonly Dictionary<long, ContainerManager> containerManagers = new Dictionary<long, ContainerManager>();
        private readonly List<Controller> controllers = new List<Controller>();
        private TimeAction timer;

        private void OnTimerCallbacked()
        {
            lock (controllers)
            {
                foreach (var controller in controllers)
                {
                    lock (controller)
                    {
                        controller.Execute();
                    }
                }
            }
        }

        private void StopTimer()
        {
            if (timer != null)
            {
                TimerManager.RemoveTimer(timer);
                timer = null;
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (!IsDisposed)
            {
                try 
                {
                    StopTimer();
                }
                finally
                {
                    base.Dispose(disposing);
                }
            }
        }

        public ContainerManager GetContainerManager(long containerType, bool system = true)
        {
            lock (containerManagers)
            {
                if (!containerManagers.TryGetValue(containerType, out ContainerManager containerManager))
                {
                    containerManager = new ContainerManager(this, containerType);
                    foreach (var area in Owner.Areas.Values.Where(p => !system || p.Category == AreaCategory.系统用地))
                    {
                        area.CheckResource();
                        foreach (var obj in area.FindObject(p => p.Type == containerType))
                        {
                            var container = new Container(obj);
                            containerManager.Containers.Add(container);
                        }
                    }

                    containerManagers[containerType] = containerManager;
                }

                return containerManager;
            }
        }

        public void AddController(Controller controller)
        {
            if (controller == null)
                throw new ArgumentNullException(nameof(controller));

            lock (controllers)
            {
                controllers.Add(controller);
                if (controllers.Count == 1)
                {
                    var interval = TimeSpan.FromSeconds(5);
                    timer = TimerManager.Run(interval, new Action(OnTimerCallbacked));
                }
            }
        }

        public bool RemoveController(Controller controller)
        {
            if (controller == null)
                throw new ArgumentNullException(nameof(controller));

            lock (controllers)
            {
                var r = controllers.Remove(controller);
                if (controllers.Count == 0)
                {
                    StopTimer();
                }

                return r;
            }
        }

        public override void Initialize(Scene owner)
        {
            base.Initialize(owner);
            var function = owner.GetFunction<SceneAirdropFunction>();
            if (function != null)
            {
                if (!function.IsSystem)
                {
                    owner.CheckCacheArea();
                }
                if (owner.Areas.Count > 250)
                {
                    Logger.GameError.Write($"scene:{owner.ID} function:{nameof(SceneAirdropFunction)} not supported.");
                    return;
                }
                if (function.Options?.Count > 0)
                {
                    var now = DateTimeExtension.Now;
                    foreach (var option in function.Options)
                    {
                        var containerManager = GetContainerManager(option.ContainerType, function.IsSystem);
                        var controller = new Controller(containerManager, option, now + option.Delay);
                        AddController(controller);
                    }

                    foreach (var controller in controllers)
                    {
                        controller.Execute();
                    }
                }
            }
        }

        public class Container
        {
            public Container(SceneObject susceptor)
            {
                Susceptor = susceptor;
            }

            public SceneObject Susceptor { get; }

            public SceneObject Resource { get; private set; }

            public DateTime Deadline { get; private set; }

            public bool IsEmpty
            {
                get 
                {
                    var obj = Resource;
                    return obj == null || obj.Deleted;
                }
            }

            public void CreateResource(SceneAirdropOption option)
            {
                var area = Susceptor.Area;
                var resource = area.NewSceneObject(Character.System, option.ResourceType, Susceptor.Transform, true, nameof(SceneAirdropFunction));
                resource.Initialize();

                //var record = resource.GetFunctionRecord<EatFoodFunctionRecord>(false);
                //if (record != null)
                //{
                //    record.UserLimit = FunctionUserLimit.None;
                //    record.VirtualCurrencyType = option.VirtualCurrencyType;
                //    record.Price = option.VirtualCurrency;
                //}

                area.Add(resource, true);

                Resource = resource;
                Deadline = DateTimeExtension.Now + option.Survival;
            }

            public void Destroy()
            {
                var obj = Resource;
                if (obj != null && !obj.Deleted)
                {
                    obj.Delete(obj.Count, nameof(SceneAirdropFunction));
                }
            }
        }

        public class ContainerManager
        {
            public ContainerManager(SceneAirdropFunctionRecord owner, long containerType)
            {
                Owner = owner;
                ContainerType = containerType;
            }

            public SceneAirdropFunctionRecord Owner { get; }

            public long ContainerType { get; }

            public List<Container> Containers { get; } = new List<Container>();
        }

        public class Controller
        {
            private const int NoneSentinel = 0;
            private const int DestroyedSentinel = 1;

            private readonly List<Container> containers = new List<Container>();
            private readonly ContainerManager containerManager;
            private readonly SceneAirdropOption source;
            private DateTime nextDeliveryTime;
            private DateTime lastLifecycleTime;
            private int lifecycleCount;
            private int state;

            public Controller(ContainerManager containerManager, SceneAirdropOption source, DateTime nextDeliveryTime)
            {
                this.source = source;
                this.containerManager = containerManager;
                this.nextDeliveryTime = nextDeliveryTime;
            }

            private void Delivery()
            {
                nextDeliveryTime = DateTimeExtension.Now + source.Interval;
                containers.RemoveAll(p => p.IsEmpty);
                if (containers.Count < source.Count)
                {
                    var completed = 0;
                    var scene = containerManager.Owner.Owner;
                    lock (containerManager)
                    {
                        var limit = source.Total != 0 ? source.Total - lifecycleCount : int.MaxValue;
                        var count = Math.Min(source.Count - containers.Count, limit);
                        var emptys = containerManager.Containers.Where(p => p.IsEmpty).ToList();
                        while (count > 0 && emptys.Count > 0)
                        {
                            var index = RandomEvent.Next(0, emptys.Count);
                            var container = emptys[index];
                            emptys.RemoveAt(index);

                            count--;
                            completed++;
                            container.CreateResource(source);
                            containers.Add(container);
                        }
                    }
                    if (completed > 0)
                    {
                        lifecycleCount += completed;
                        if (!string.IsNullOrEmpty(source.RollMessage))
                        {
                            scene.RollMessage(source.RollMessage);
                        }
                        if (source.Message != null)
                        {
                            source.Message.Send(scene);
                        }
                    }

                    Logger.Debug.Write($"scene:{scene.ID} {nameof(SceneAirdropFunction)} container:{source.ContainerType} resource:{source.ResourceType} delivery:{completed}");
                }
            }

            public void Destroy()
            {
                if (Interlocked.CompareExchange(ref state, DestroyedSentinel, NoneSentinel) == NoneSentinel)
                {
                    foreach (var container in containers)
                    {
                        if (!container.IsEmpty)
                        {
                            container.Destroy();
                        }
                    }

                    containers.RemoveAll(p => p.IsEmpty);
                }
            }

            public void Execute()
            {
                if (Volatile.Read(ref state) != NoneSentinel) return;

                var now = DateTimeExtension.Now;
                if (source.Survival > TimeSpan.Zero && containers.Count > 0)
                {
                    //清理过期的资源
                    foreach (var container in containers)
                    {
                        if (!container.IsEmpty && container.Deadline <= now)
                        {
                            container.Destroy();
                        }
                    }
                }
                if (source.Fragments == null || source.Fragments.Count == 0 || source.Fragments.Exists(p => p.Available(now)))
                {
                    if (DateTimeExtension.Now >= nextDeliveryTime)
                    {
                        if (source.Total > 0)
                        {
                            if (lastLifecycleTime.Date != now.Date)
                            {
                                //每日重置刷新数量
                                lifecycleCount = 0;
                                lastLifecycleTime = now;
                            }
                            if (lifecycleCount < source.Total)
                            {
                                Delivery();
                            }
                        }
                        else
                        {
                            Delivery();
                        }
                    }
                }
                else if (source.IsDestroy)
                {
                    //清理存货的资源
                    foreach (var container in containers)
                    {
                        if (!container.IsEmpty)
                        {
                            container.Destroy();
                        }
                    }

                    containers.Clear();
                }
            }
        }
    }
}
