using GameModel.Activities;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions
{
    public class MilkTeaSaleEvent : PicaGameDialogEvent
    {
        public override void Do(SceneCharacter character)
        {
            var act = GameApplication.ActivityManager.FirstActivity<MilkTeaSeasonActivity>();
            if (act != null)
            {
                (act as IInteractionActivity).DoActivityInteraction(character.Owner, 5, ActionTargetCategory.Player, 0, null);
            }
        }
    }
}
