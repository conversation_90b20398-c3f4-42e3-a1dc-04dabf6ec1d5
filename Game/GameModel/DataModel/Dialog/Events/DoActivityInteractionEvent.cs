using GameModel.Activities;
using HelpBase;
using HelpBase.Json;
using Proto.GameModel;

namespace GameModel.Functions
{
    public class DoActivityInteractionEvent : PicaGameDialogEvent
    {
        [JsonMember]
        public long ActivityID { get; set; }

        [JsonMember]
        public int Action { get; set; }

        [JsonMember]
        public long TargetID { get; set; }

        [JsonMember]
        public int IntParam { get; set; }

        public override void Do(SceneCharacter character)
        {
            var act = GameApplication.ActivityManager.GetActivity(ActivityID);
            if (act != null)
            {
                if (act.Phase != ActivityPhase.开启)
                {
                    character.Owner.ShowMessage("活动未开启。" + ActivityID);
                    return;
                }
                if (act.Function is not IInteractionActivity)
                {
                    character.Owner.ShowMessage("该活动不是 IInteractionActivity。" + ActivityID);
                    return;
                }
                (act.Function as IInteractionActivity).DoActivityInteraction(character.Owner, (byte)Action, ActionTargetCategory.Player, TargetID, 
                                                                                                    ProtoHelp.GetBytes(new Int32Message { Value = IntParam }));
            }
            else
            {
                character.Owner.ShowMessage("未找到活动。" + ActivityID);
            }
        }
    }
}
