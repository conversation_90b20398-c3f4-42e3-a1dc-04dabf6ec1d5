using GameModel.Functions.SceneObjects;
using HelpBase.Json;
using Proto.GameModel;
using System.Numerics;

namespace GameModel.Functions
{
    public class AddSceneObjectEvent : PicaGameDialogEvent
    {
        [JsonMember]
        public int ElementId { get; set; }

        [JsonMember]
        public long ElementTypeId { get; set; }

        [JsonMember]
        public int PosX { get; set; }

        [JsonMember]
        public int PosY { get; set; }

        [JsonMember]
        public Direction Direction { get; set; } = Direction.LOWER_LEFT;

        [JsonMember]
        public string AnimationName { get; set; } = "idle";

        [JsonMember]
        public string NPCName { get; set; }

        [JsonMember]
        public long GameDialogId { get; set; }

        [JsonMember]
        public float FadeIn { get; set; }

        [JsonMember(FormatType = (typeof(DynamicCollectionFormat<SceneObjectFunctionRecord, JsonDynamicList<SceneObjectFunctionRecord>>)))]
        public JsonDynamicList<SceneObjectFunctionRecord> Records { get; set; }

        public override void Do(SceneCharacter character)
        {
            base.Do(character);
            Transform ts = new Transform()
            {
                Direction = Direction,
                Position = new Vector3(PosX, PosY, 0)
            };
            var so = character.CurrentArea?.CreateObject(Character.System, ElementTypeId, ts, "dialog创建物件");
            so?.ChangeStatus(0, AnimationName);
        }
    }
}
