using HelpBase;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Proto.GameModel;
using GameModel.Award;
using GameModel.Managers;
using System.Json;
using DataBase;

namespace GameModel.Functions.Organizations
{
    public class OrganizationOrderFunction : OrganizationBuildingFunction<OrganizationOrderRecord, OrganizationOrderCharacterRecord>
    {
        public override bool CanUse(Organization organization, OrganizationAgent agent, long targetId, byte[] bytes)
        {
            return base.CanUse(organization, agent, targetId, bytes);
        }

        public override void Use(Organization organization, OrganizationAgent agent, long targetId, byte[] bytes)
        {
            if (bytes == null)
            {
                var func_record0 = GetFunctionRecord(organization, true, true);
                var charac_record = GetCharacterRecord(agent.Owner, true, true);
                agent.Owner.SendGoogleProto(ClientMethod.OpenOrganizationOrderData, new OrganizationOrderPanelData() { OrderData = func_record0.ProtoData, PersonalData = charac_record.ProtoData });
                return;
            }
            var use_data = ProtoHelp.GetObject<OrganizationOrderUseReq>(bytes);
            switch (use_data.Action)
            {
                case 1:
                    var func_record0 = GetFunctionRecord(organization, true, true);
                    var charac_record = GetCharacterRecord(agent.Owner, true, true);
                    agent.Owner.SendGoogleProto(ClientMethod.OpenOrganizationOrderData, new OrganizationOrderPanelData() { OrderData = func_record0.ProtoData, PersonalData = charac_record.ProtoData });
                    break;
                case 2:
                    DoDonateOrder(agent.Owner, use_data, organization);
                    break;
                case 3:

                    if (!agent.CheckPosition())
                    {
                        agent.Owner.ShowErrorTipsMessage(I18nManager.I18n("PKT_Organization0012"));
                        return;
                    }
                    var func_record = GetFunctionRecord(organization, true, true);
                    func_record.Category = use_data.SettingCategory;
                    agent.Owner.ShowTipsMessage(I18nManager.I18n("PKT_Organization0020"));
                    func_record.Save();
                    func_record.UpdateToCharacters();
                    break;
                case 4:
                    DoTakeWeeklyProcessAward(agent.Owner, organization);
                    break;
            }
        }

        private void DoDonateOrder(Character character, OrganizationOrderUseReq reqData, Organization organization)
        {
            var item_count_check = true;
            var config = GameApplication.DataManager.FindValue<OrganizationOrderType>(reqData.DonateOrderId);
            var character_record = GetCharacterRecord(character, true, true);
            var orga_record = GetFunctionRecord(organization, true, true);

            lock (orga_record)
            {
                var total_times = 0;
                reqData.DonateItems.ForEach(d => total_times += d.Value);
                if (!orga_record.OrderProcess.TryGetValue(config.ID, out var _))
                {
                    //没有这个订单
                    return;
                }
                if (character_record.TodayOrderProcess.TryGetValue(config.OrderQuality, out var times))
                {
                    if (times + total_times > Constants.DailyDonateOrderTimePerQuality)
                    {
                        character.ShowErrorTipsMessage(I18nManager.I18n("PKT_Organization0019"));
                        //提交数量大于每日上限
                        return;
                    }
                }
                else if (total_times > Constants.DailyDonateOrderTimePerQuality)
                {
                    return;
                }
                foreach (var data in reqData.DonateItems)
                {
                    if (!config.NeedItems.Contains(data.ID))
                    {
                        item_count_check = false;
                        break;
                    }

                    if (!character.CheckEntityCountEnough(data.ID, data.Value))
                    {
                        item_count_check = false;
                        break;
                    }
                }

                if (!item_count_check)
                {
                    //道具数量不足
                    return;
                }

                var submit_suc = true;
                var cost_items = new JsonArray();
                foreach (var data in reqData.DonateItems)
                {
                    if (!character.DeleteEntity(data.ID, data.Value, "提交家族订单"))
                    {
                        submit_suc = false;
                        break;
                    }

                    var json = new JsonObject();
                    json["Name"] = I18nManager.ItemName(data.ID);
                    json["Type"] = data.ID;
                    json["Count"] = data.Value;
                    cost_items.Add(json);
                }

                if (!submit_suc)
                {
                    //道具数量不足
                    return;
                }

                if (!character_record.TodayOrderProcess.ContainsKey(config.OrderQuality))
                {
                    character_record.TodayOrderProcess[config.OrderQuality] = 0;
                }
                if (!character_record.OrderDailyProcessAutoClear)
                {
                    character_record.TodayOrderProcess[config.OrderQuality] += total_times;
                }
                character.GetSystemAward(config.DonatorAwardId, "提交家族订单", extra: total_times);
                organization.GetSystemAward(config.OrganizationAwardId, "提交家族订单", extra: total_times);

                character_record.UpdateToCharacter();
                character_record.Save();
                var old_process = orga_record.OrderProcess[config.ID];
                var add_process = config.SingleOrderProcessAddValue * total_times;
                orga_record.OrderProcess[config.ID] += add_process;
                var process_after = 0;
                if (orga_record.OrderProcess[config.ID] >= 1000)
                {
                    orga_record.RefreshByConfig(config);
                    orga_record.WeeklyProcess += config.WeeklyProcessAddValue;
                }
                else
                {
                    process_after = orga_record.OrderProcess[config.ID];
                }
                orga_record.Save();
                character.GetLeiTingTrackWriter("submit_organization_order")
                .WriteStringEntry("organization_id", organization.ID)
                .WriteEntry("organization_name", organization.Name)
                .WriteEntry("cost_items", cost_items.ToString())
                .WriteEntry("order_id", config.ID)
                .WriteEntry("process_before", old_process)
                .WriteEntry("add_process", add_process)
                .WriteEntry("process_after", process_after)
                .WriteToTaLogger();

                character.CheckUpdateAction(character, UserAction.交付家族订单, value: total_times);

                if (character_record.TodayOrderProcess[config.OrderQuality] == Constants.DailyDonateOrderTimePerQuality)
                {
                    var record = RowAdapter.Create<OrganizationRecord>();
                    record.CreateSuccess();
                    record.Owner = organization.ID;
                    record.Action = OrganizationAction.DoOrder;
                    record.Operate = character;
                    record.Initialize();
                    record.Save();
                    organization.AddRecord(record);

                    orga_record.Save();
                }
            }
            orga_record.UpdateToCharacters();
        }

        private void DoTakeWeeklyProcessAward(Character character, Organization organization)
        {
            var chara_record = GetCharacterRecord(character, true, true);
            var func_record = GetFunctionRecord(organization, true, true);

            var award_ids_to_take = GameApplication.DataManager.FindValue<OrganizationProcessAwardType>(o => o.ProcessThreshold > chara_record.ReachAwardProcess && o.ProcessThreshold <= func_record.WeeklyProcess);

            if (award_ids_to_take.Count == 0)
            {
                return;
            }

            var res = new List<AwardSpec>();

            foreach (var award_conf in award_ids_to_take)
            {
                res.AddRange(character.GetSystemAward(award_conf.AwardId, "领取家族订单周进度奖励"));

                if (chara_record.ReachAwardProcess < award_conf.ProcessThreshold)
                {
                    chara_record.ReachAwardProcess = award_conf.ProcessThreshold;
                }

                var record = RowAdapter.Create<OrganizationRecord>();
                record.CreateSuccess();
                record.Owner = organization.ID;
                record.Action = OrganizationAction.TakeOrderAward;
                record.Operate = character;
                record.Value = award_conf.ProcessThreshold;
                record.Initialize();
                record.Save();
                organization.AddRecord(record);
            }
            chara_record.Save();
            chara_record.UpdateToCharacter();
            character.SendHighLightAwards(res);
        }
    }
}
