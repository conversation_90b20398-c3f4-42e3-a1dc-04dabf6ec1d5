using System;
using HelpBase.Json;

namespace GameModel.Configurations
{
    public class SystemSwitches : JsonFormatObject
    {
        // [JsonMember(ShowName = "能否进入副本")]
        // public bool CanEnterDimension { get; set; } = true;
        //
        // [JsonMember(ShowName = "能否创建矿洞")]
        // public bool CanCreateDimension { get; set; } = true;
        //
        // [JsonMember(ShowName = "能否召唤Boss")]
        // public bool CanCreateDimensionScene { get; set; } = true;
        //
        // [JsonMember(ShowName = "能否购买物品")]
        // public bool CanBuyProp { get; set; } = true;
        //
        // [JsonMember(ShowName = "能否充值")]
        // public bool CanPay { get; set; } = true;
        //
        // [JsonMember(ShowName = "能否充值")]
        // public bool CanFish { get; set; } = true;
        //
        // [JsonMember(ShowName = "可否回收场景所有家具")]
        // public bool CanPickUpAllSceneObject { get; set; } = true;
        //
        // [JsonMember(ShowName = "可否重置房间")]
        // public bool CanResetScene { get; set; } = true;

        [JsonMember(ShowName = "可否交易")]
        public bool CanTrade { get; set; } = true;

        [JsonMember(ShowName = "充值金额验证")]
        public bool PayPriceVerify { get; set; } = true;

        [JsonMember(ShowName = "登录设备号验证")]
        public bool LoginDeviceVerify { get; set; } = true;

        [JsonMember(ShowName = "创建设备号验证")]
        public bool CreateDeviceVerify { get; set; } = true;

        [JsonMember(ShowName = "邮件自动领取删除")]
        public bool MailAuthDeleted { get; set; } = true;

        // [JsonMember(ShowName = "是否有邀请收益")]
        // public bool CanGetInviteIncome { get; set; } = true;

        [JsonMember(ShowName = "邀请收益账期(分钟)")]
        public double InviteIncomeInvestMinute { get; set; } = 60;

        // [JsonMember(ShowName = "邀请收益创角日期")]
        // public DateTime InviteIncomeCreateDate { get; set; } = new DateTime(2018, 12, 22, 16, 0, 0);
        //
        // [JsonMember(ShowName = "邀请收益持续天数")]
        // public int InviteIncomeContinueDays { get; set; } = 90;
        //
        // [JsonMember(ShowName = "是否发放产业区奖励")]
        // public bool CanIndustrialDistrictDayAward { get; set; } = true;
        //
        // [JsonMember(ShowName = "是否启用eos邀请返利")]
        // public bool IsInviteIncomeEnabled { get; set; } = true;
        //
        // [JsonMember(ShowName = "是否启用营业额分成")]
        // public bool CanBossGoday { get; set; } = true;
        //
        // [JsonMember(ShowName = "可否领取账单")]
        // public bool CanUnLockNash { get; set; } = true;
        //
        // [JsonMember(ShowName = "是否启用幸运奖池注入")]
        // public bool CanInjectionLuckyPool { get; set; } = true;

        [JsonMember(ShowName = "是否启用系统结算检测")]
        public bool IsEnabledSystemSettlementChecked { get; set; } = true;

        // [JsonMember(ShowName = "启用图鉴提示")]
        // public bool IsIllustrationTooltipEnabled { get; set; }
    }
}
