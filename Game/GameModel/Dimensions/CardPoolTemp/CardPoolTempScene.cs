using DataBase;
using GameModel.Functions.SceneObjects;
using Proto.GameModel;
using System.Collections.Generic;
using System.Json;
using System.Linq;
using System;
using System.Numerics;
using HelpBase.Json;
using GameModel.Activities;

namespace GameModel.Dimensions
{
    [EntityTable(DbGroup.Entity, typeof(Scene))]
    public class CardPoolTempScene : PicaScene
    {
        public override void InitScene()
        {
            base.InitScene();

            //List<PicaActorBotObjectFunctionRecord> model_record_list = new List<PicaActorBotObjectFunctionRecord>();
            //List<long> male_equips = new List<long>();
            //List<long> female_equips = new List<long>();
            //DrawCardActivity active_func = null;

            ////先确定模特是不是在房间中 以及数量够不够
            //foreach (var area in Areas)
            //{
            //    foreach (var so in area.Value.Objects.Values)
            //    {
            //        if (so.Type == 530726)
            //        {
            //            var so_func = so.GetFunction<PicaActorBotObjectFunction>();
            //            if (so_func != null)
            //            {
            //                model_record_list.Add(so_func.GetRecord(so));
            //            }
            //        }
            //    }
            //}
            //if (model_record_list.Count != 2)
            //{
            //    Logger.Error.Write("Scene" + Type + " has no enough models");
            //    return;
            //}

            ////找到对应的活动
            //foreach (var activity in GameApplication.ActivityManager.Activities.Values.Where(p =>
            //    p.Function != null &&
            //    p.Function.ClientShowType == ActivityShowType.活动中心 &&
            //    p.Function.ClientType ==  ActivityClientType.大厅 &&
            //    p.EndDate > DateTimeExtension.Now))
            //{
            //    if (activity.Function.ClassName != "DrawCardActivity")
            //        continue;

            //    var func = (DrawCardActivity)(activity.Function);

            //    if (func.TemplateSceneId == Type)
            //    {
            //        active_func = func;
            //        break;
            //    }
            //}
            //if (active_func == null)
            //{
            //    Logger.Error.Write("Scene" + Type + " has no active view");
            //    return;
            //}


            //var male_model_record = model_record_list[0];
            //var female_model_record = model_record_list[1];

            ////从活动中提取出需要的服装
            //foreach (var pool in active_func.Pools)
            //{
            //    if (pool.Type == DrewCardPoolType.装备)
            //    {
            //        foreach (var equip_award in pool.Awards)
            //        {
            //            var equip = GameApplication.DataManager.FindValue<EntityType, EquipType>(equip_award.ID);
            //            switch (equip.Gender)
            //            {
            //                case Gender.男:
            //                    male_equips.Add(equip.ID);
            //                    break;
            //                case Gender.女:
            //                    female_equips.Add(equip.ID);
            //                    break;
            //                default:
            //                    male_equips.Add(equip.ID);
            //                    female_equips.Add(equip.ID);
            //                    break;
            //            }
            //        }
            //    }
            //}

            ////换装
            //male_model_record.ChangeAvatar(male_equips);
            //female_model_record.ChangeAvatar(female_equips);
        }
    }
}
