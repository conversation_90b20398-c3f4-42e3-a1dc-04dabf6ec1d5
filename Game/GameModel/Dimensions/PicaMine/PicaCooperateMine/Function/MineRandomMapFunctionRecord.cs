using GameModel.Dimensions.PicaMine;
using HelpBase.Collections.Concurrent;
using Proto.GameModel;
using System.Numerics;

namespace GameModel
{
    public class MineRandomMapFunctionRecord : SceneFunctionRecord, ISceneMineListener
    {
        public enum MineRoomTileType
        {
            空 = 0,
            地块 = 1,
            安全区 = 2,
            出生点 = 3,
        }

        public new PicaScene Owner { get; set; }

        protected Area Area
        {
            get
            {
                return Owner?.FindArea(0);
            }
        }

        /// <summary>
        /// 地图宽度
        /// </summary>
        public int MapWidth { get; set; }
        /// <summary>
        /// 地图高度
        /// </summary>
        public int MapHeight { get; set; }

        public MineRoomConfigNewType RoomConfig { get; set; }

        /// <summary>
        /// 格子对应的坐标
        /// </summary>
        ConvenientDictionary<Vector2, MineTile> Map = new ConvenientDictionary<Vector2, MineTile>();
        /// <summary>
        /// 格子对应的物件
        /// </summary>
        ConvenientDictionary<Vector2, SceneObject> SceneObjectMap = new ConvenientDictionary<Vector2, SceneObject>();
        /// <summary>
        /// 记录每种矿石的数量
        /// </summary>
        private ConvenientDictionary<long, int> MineAmountDic = new ConvenientDictionary<long, int>();

        public override void Initialize(Scene owner)
        {
            base.Initialize(owner);
            Owner = owner as PicaScene;
        }

        public void SetMineRoomConfig(MineRoomConfigNewType roomType)
        {
            RoomConfig = roomType;
            RandomMap();
        }

        public void ExecuteAction(Character character)
        {
            MineAction action = new MineAction();
            if (GMActions.ContainsKey(character.ID))
            {
                var actions = GMActions[character.ID];
                if (actions != null && actions.Count > 0)
                {
                    actions[0].Do(character);
                    actions.RemoveAt(0);
                }
            }
            else if (RoomConfig.Actions != null && RoomConfig.Actions.Count > 0)
            {
                action = RoomConfig.Actions.GetRandomItem().Action;
            }
            action?.Do(character);
        }

        #region 矿石的生成和销毁
        public SceneObject GenRandomOre(Vector3 p, bool sendMessage = false)
        {
            RandomList<MineStoreRandomWeight> mineList = new RandomList<MineStoreRandomWeight>();
            foreach (var item in RoomConfig.Weights)
            {
                if (item.MaxAmount > 0 && MineAmountDic.ContainsKey(item.MineTypeId) && MineAmountDic[item.MineTypeId] >= item.MaxAmount)
                {
                    continue;
                }
                mineList.Add(item);
            }
            MineStoreRandomWeight data = mineList.GetRandomItem();
            if (data == null)
            {
                return null;
            }
            if (data.MaxAmount > 0)
            {
                if (!MineAmountDic.ContainsKey(data.MineTypeId)) MineAmountDic[data.MineTypeId] = 0;
                MineAmountDic[data.MineTypeId]++;
            }
            var so = CreateSceneObject(data.MineTypeId, p, sendMessage);
            //var func = so.GetFunction<PicaMineFunction>();
            //if (func != null)
            //{
            //    if (func.IsInvalid) InvalidMineCount++;
            //}

            return so;
        }

        private SceneObject CreateSceneObject(long typeId, Vector3 vector, bool sendMessage = false)
        {
            Transform ts = new Transform()
            {
                Direction = Direction.LOWER_LEFT,
                Position = vector
            };
            var so = Area.CreateTempObject(Character.System, typeId, ts, "矿石生成", null, 0, sendMessage);
            return so;
        }

        public void RemoveElement(SceneObject sceneObject)
        {
            var mineTile = Map.FirstOrDefault(m => m.PixelPosition == sceneObject.Transform.Position);
            if (mineTile != null)
            {
                // 属于自动生成的物件 处理的刷新等逻辑
                var interval = RoomConfig.RefreshInterval.Random();
                if (interval > 0)
                {
                    //var data = new SceneTimeCheckRecordData();
                    //data.SceneID = ID;
                    //data.AreaID = Area.ID;
                    //data.CheckTime = DateTimeExtension.NowTimeStamp + interval;
                    //data.Positon.Add(sceneObject.Transform.Position.ProtoData());
                    //SendToSceneService(SceneManagerClientMethod.AddSceneTimeCheckRecord, data);
                    //var RC = GetRecord(Owner, true);
                    //RC.AddRecord(data);
                }

                if (MineAmountDic.ContainsKey(sceneObject.TypeData.ID))
                {
                    MineAmountDic[sceneObject.TypeData.ID]--;
                }

                SceneObjectMap[mineTile.TilePosition] = null;
            }

            Area.Remove(sceneObject);
            Area.UpdateCacheVersionToSceneServer();
        }

        #endregion

        #region 生成随机地图
        private void RandomMap()
        {
            if (RoomConfig.MapWidth == 0 || RoomConfig.MapHeight == 0)
            {
                // 说明这个场景不需要随机样式刷矿
                return;
            }
            var cols = (int)Owner.TypeData.SceneNode.size.cols;
            var rows = (int)Owner.TypeData.SceneNode.size.rows;
            MapWidth = RoomConfig.MapWidth == 0 ? cols : Math.Min(cols, RoomConfig.MapWidth);
            MapHeight = RoomConfig.MapHeight == 0 ? cols : Math.Min(rows, RoomConfig.MapHeight);

            MineRoomTileType[,] map = new MineRoomTileType[MapHeight, MapWidth];
            List<Vector2> stack = new List<Vector2>();

            // 随机找一个点作为出生点 周围不生成矿石
            int spawnPointX = RandomEvent.Next((int)(MapWidth * 0.2), (int)(MapWidth * 0.8) + 1);
            int spawnPointY = RandomEvent.Next((int)(MapHeight * 0.2), (int)(MapHeight * 0.8) + 1);
            for (int y = spawnPointY - RoomConfig.SafetyArea; y <= spawnPointY + RoomConfig.SafetyArea; y++)
            {
                for (int x = spawnPointX - RoomConfig.SafetyArea; x <= spawnPointX + RoomConfig.SafetyArea; x++)
                {
                    map[y, x] = MineRoomTileType.安全区;
                    stack.Add(new Vector2(x, y));
                }
            }
            map[spawnPointY, spawnPointX] = MineRoomTileType.出生点;

            // 随机生成
            var curObjectCount = 0;
            MineRoomTileType[,] _map = new MineRoomTileType[MapHeight, MapWidth];
            while (curObjectCount < RoomConfig.MineObjectRange.Min)
            {
                TotalMineCount = 0;
                Array.Copy(map, _map, map.Length);
                curObjectCount = ExtendMap(_map, stack, MapWidth, MapHeight, RoomConfig.SpreadModulus);
                Logger.Debug.Write($"RandomMapCount : [{curObjectCount}] [{RoomConfig.MineObjectRange.Min}] [{RoomConfig.MineObjectRange.Max}]");
            }
            SmoothMap(_map, MapWidth, MapHeight);

            // 设置地块
            List<int> tileIds = RoomConfig.TileSn.Select(p => Owner.GetTileSetId(p)).ToList();
            List<int> tileMapData = new List<int>();
            List<bool> unwalkable = new List<bool>();
            for (int y = 0; y < MapHeight; y++)
            {
                for (int x = 0; x < MapWidth; x++)
                {
                    switch (_map[y, x])
                    {
                        case MineRoomTileType.地块:
                            tileMapData.Add(tileIds.Random());
                            unwalkable.Add(true);
                            var v3 = TransformTo90(new Vector3(x, y, 0), Owner.TypeData.SceneNode.size.tileWidth, Owner.TypeData.SceneNode.size.tileHeight);
                            var v2 = new Vector2(y, x);
                            Map.TryAdd(v2, new MineTile(v2, v3));
                            SceneObjectMap.TryAdd(new Vector2(y, x), GenRandomOre(v3));
                            break;
                        case MineRoomTileType.安全区:
                            tileMapData.Add(tileIds.Random());
                            unwalkable.Add(true);
                            break;
                        case MineRoomTileType.出生点:
                            tileMapData.Add(tileIds.Random());
                            unwalkable.Add(true);
                            var spawnPoint = TransformTo90(new Vector3(x, y, 0), Owner.TypeData.SceneNode.size.tileWidth, Owner.TypeData.SceneNode.size.tileHeight);
                            Area.EntranceTransform = new Transform() { Position = spawnPoint };
                            break;
                        default:
                            tileMapData.Add((int)TileSetSpecialIdConst.Empty);
                            unwalkable.Add(false);
                            break;
                    }
                }
            }
            Area.ChangeTileMap(tileMapData, unwalkable);
        }

        private int TotalMineCount;
        private int ExtendMap(MineRoomTileType[,] map, List<Vector2> stack, int width, int height, int probability)
        {
            var _floorCount = 0;
            List<Vector2> _stack = new List<Vector2>();
            void _ff(int x, int y)
            {
                if (x >= 0 && x < width && y >= 0 && y < height && map[y, x] == MineRoomTileType.空)
                {
                    if (RandomEvent.Next(1, 101) <= probability)
                    {
                        if (TotalMineCount >= RoomConfig.MineObjectRange.Max)
                        {
                            return;
                        }
                        map[y, x] = MineRoomTileType.地块;
                        _stack.Add(new Vector2(x, y));
                        _floorCount++;
                        //特殊矿洞达到指定的地块数量后就停止
                        TotalMineCount++;
                    }
                }
            }
            foreach (var f in stack)
            {
                _ff((int)(f.X + 1), (int)f.Y);
                _ff((int)(f.X - 1), (int)f.Y);
                _ff((int)(f.X), (int)(f.Y + 1));
                _ff((int)(f.X), (int)(f.Y - 1));
            }
            if (_stack.Count() > 0)
            {
                _floorCount += ExtendMap(map, _stack, width, height, probability);
            }
            return _floorCount;
        }

        private MineRoomTileType[,] CloneMap(MineRoomTileType[,] map, int width, int height)
        {
            MineRoomTileType[,] _map = new MineRoomTileType[height, width];
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    _map[y, x] = map[y, x];
                }
            }
            return _map;
        }

        private Vector3 TransformTo90(Vector3 pos, int tileWidth, int tileHeight)
        {
            return new Vector3((pos.X - pos.Y) * (tileWidth / 2.0f), (pos.X + pos.Y) * (tileHeight / 2.0f) + 15f, 0f);
        }
        #endregion

        #region 平滑地块
        // 平滑地块
        void SmoothMap(MineRoomTileType[,] map, int width, int height)
        {
            for (int y = 0; y < width; y++)
            {
                for (int x = 0; x < height; x++)
                {
                    if (map[x, y] == MineRoomTileType.安全区 || map[x, y] == MineRoomTileType.出生点) continue;
                    int surroundingWallCount = GetSurroundingWallCount(map, x, y, height, width);

                    if (surroundingWallCount > 5)
                        map[x, y] = MineRoomTileType.地块;
                }
            }
        }
        int GetSurroundingWallCount(MineRoomTileType[,] map, int gridX, int gridY, int width, int height)
        {
            int wallCount = 0;

            for (int neighborX = gridX - 1; neighborX <= gridX + 1; neighborX++)
            {
                for (int neighborY = gridY - 1; neighborY <= gridY + 1; neighborY++)
                {
                    if (neighborX >= 0 && neighborX < width && neighborY >= 0 && neighborY < height)
                    {
                        if (neighborX != gridX || neighborY != gridY)
                        {
                            wallCount += (int)map[neighborX, neighborY];
                        }
                    }
                    else
                    {
                        wallCount++;
                    }
                }
            }
            return wallCount;
        }


        #endregion

        #region GM
        public Dictionary<long, List<MineAction>> GMActions { get; set; } = new Dictionary<long, List<MineAction>>();
        public void TestMineSetNextAction(Character character, MineAction action)
        {
            List<MineAction> _action = new List<MineAction>();
            if (GMActions.ContainsKey(character.ID))
            {
                _action = GMActions[character.ID];
            }
            else
            {
                GMActions.Add(character.ID, _action);
            }

            _action.Add(action);
        }
        #endregion
    }
}
