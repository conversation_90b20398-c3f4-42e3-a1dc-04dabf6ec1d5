using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions
{
    public class PicaMathGameDimension : PicaDimension<PicaMathGameScene>
    {
        public override DimensionType Type => DimensionType.PicaMathGame;

        public override bool IsVisited => false;

        public override bool TryEnterAfterRelogin(SceneCharacter character)
        {
            if (character.CurrentScene is PicaMathGameScene current && current.Dimension == this && current.CanEnter(character.Owner))
            {
                return current.Enter(character);
            }

            return false;
        }
    }
}
