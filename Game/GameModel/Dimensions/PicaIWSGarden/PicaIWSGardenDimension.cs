using GameModel.Award;
using GameModel.Functions.SceneObjects;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions
{
    public class PicaIWSGardenDimension : PicaDimension<PicaIWSGardenScene>
    {
        public override DimensionType Type => DimensionType.PicaIWSGarden;

        public override StatisticsSceneType? StatisticsType => StatisticsSceneType.新手引导;

        protected sealed override SceneExpandType ExpandType => SceneExpandType.SinglePlayerScene;

        public override Scene GetAvailableScene(SceneCharacter sc, DimensionLevel level)
        {
            //if (!GuideAwardSent(sc)) SendGuideAward(sc);
            return level.CreateScene(sc.Owner);
        }

        //private bool GuideAwardSent(SceneCharacter sc)
        //{
        //    return false;
        //}

        //private void SendGuideAward(SceneCharacter sc)
        //{
        //    //第一次进庭舍引导时发奖励
        //    var mainhouse = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(530413);
        //    var warehouse = GameApplication.DataManager.FindValue<EntityType, SceneObjectType>(530414);
        //    var waterpot = GameApplication.DataManager.FindValue<NpcShopItemType>(110500);
        //    var awards = new AwardGroup();
        //    foreach (var req in mainhouse.GetFunction<PicaFixGardenFunction>().Requirement)
        //    {
        //        awards.Add(new ObjectAward(req.Type, req.Count));
        //    }
        //    foreach (var req in warehouse.GetFunction<PicaFixGardenFunction>().Requirement)
        //    {
        //        awards.Add(new ObjectAward(req.Type, req.Count));
        //    }
        //    foreach (var req in waterpot.Requirement)
        //    {
        //        if (req.Type < 10) awards.Add(new VirtualCurrencyAward(req.Type, req.Count));
        //        else awards.Add(new ObjectAward(req.Type, req.Count));
        //    }
        //    sc.Owner.GetAwards(awards, "GardenGuide");
        //}
    }
}
