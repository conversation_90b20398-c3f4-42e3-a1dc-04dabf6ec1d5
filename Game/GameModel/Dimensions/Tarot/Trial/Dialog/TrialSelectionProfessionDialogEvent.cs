using GameModel.Games;
using HelpBase.Json;

namespace GameModel.Dimensions.Tarot.Trial
{
    /// <summary>
    /// 表示选择职业对话事件的类
    /// </summary>
    public sealed class TrialSelectionProfessionDialogEvent : GameDialogEvent
    {
        [JsonMember]
        public long ProfessionType { get; set; }

        [JsonMember]
        public string Tips { get; set; }

        public override void Do(<PERSON><PERSON>haracter character)
        {
            if (character.CurrentScene.Dimension is not TrialDimension)
            {
                character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000165);
                return;
            }
            if (character.GamePlayer is not TrialPlayer player)
            {
                character.ShowTipsMessage(PicaLocalityStrings.PKT_SYS2_0000003);
                return;
            }
            if (player.ProfessionType == ProfessionType)
            {
                //重复操作
                return;
            }
            if (!player.Game.ProfessionTypes.TryGetValue(ProfessionType, out var template))
            {
                character.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000082);
                return;
            }

            var record = character.Owner.GetGameRecord<TrialGameCharacterRecord>(player.Game);
            record.ProfessionType = template.ID;
            record.Save();

            player.ChangeProfession(template, true);
            if (!string.IsNullOrEmpty(Tips))
            {
                player.ShowI18nTipsMessage(Tips);
            }
        }
    }
}
