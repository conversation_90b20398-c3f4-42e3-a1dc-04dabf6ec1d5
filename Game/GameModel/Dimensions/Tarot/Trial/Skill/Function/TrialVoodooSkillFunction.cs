using GameModel.Award;
using HelpBase.Json;

namespace GameModel.Dimensions.Tarot.Trial
{
    /// <summary>
    /// 表示替身技能功能的类
    /// </summary>
    public sealed class TrialVoodooSkillFunction : TrialTriggerSkillFunction
    {
        [JsonMember]
        public string Tips { get; set; }

        [JsonMember]
        public byte ClearBuffCategory { get; set; }

        [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
        public JsonDynamicList<AwardBase> Awards { get; set; }

        [JsonMember]
        public string ActorEffect { get; set; }

        public override void Do(TrialPlayer player, TrialSkill skill)
        {
            skill.Use();
            player.DeleteBuffs(ClearBuffCategory);
            player.GetAwards(Awards);
            if (!string.IsNullOrEmpty(Tips))
            {
                player.ShowI18nTipsMessage(Tips);
            }
            if (!string.IsNullOrEmpty(ActorEffect))
            {
                player.Owner.SceneCharacter.PlayActorEff(ActorEffect);
            }
        }
    }
}
