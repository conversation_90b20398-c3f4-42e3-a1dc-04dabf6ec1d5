using DataBase;
using GameModel.Activities;
using GameModel.Functions.SceneObjects;
using HelpBase.Linq;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Dimensions
{
    [EntityTable(DbGroup.Entity, typeof(Scene))]
    public class LuckyAdventureScene : TimerCheckScene, IElasticBranchScene
    {
        [TableColumn]
        public int RoomIndex { set; get; }

        [TableColumn]
        public bool CapsuleLoaded { get; set; }

        protected override bool CanAutoCloseScene => false;
        protected override void OnCapsuleCreate()
        {
            base.OnCapsuleCreate();
            InitActivityRecord();
        }

        public override bool NeedSaveSceneObject => true;
        protected override void CreatFromCapsule()
        {
            if (CapsuleLoaded)
            {
                GameApplication.SceneManager.GetDimensionLevel(TypeData).AddSceneInstance(this);
                return;
            }
            base.CreatFromCapsule();
            CapsuleLoaded = true;
            Save();
        }

        public void InitActivityRecord()
        {
            var act = GameApplication.ActivityManager.FirstActivity<LuckyAdventureFunction>();
            if (act == null) { return; }

            var machine_list = Areas.First().Value.Objects.Values.Where(o => LuckyAdventureFunction.LuckyMachineType.Contains(o.Type));
            var conf = act.LuckyAdventureAwards.FirstOrDefault(l => l.TemplateSceneId == Type);
            if (conf == null) { return; }
            var award_id = conf.AwardId;
            machine_list.ForEach(m =>
            {
                var func = m.GetFunction<LuckyAdventureMachineFunction>();
                func.GetRecord(m, true);
                var record = func.GetActivityRecord(m);
                if (record == null)
                {
                    record = act.CreateSceneObjectRecord(m, award_id);
                }
                else
                {
                    record.RefreshAwards();
                    record.Save();
                }
            });
        }

        public override void TimerChecked(SceneTimeCheckRecordData stcrd)
        {
            var scene_object_ids = ProtoSerializer.Deserialize<IDListMessage>(stcrd.Bytes);

            scene_object_ids.Items.ForEach(s =>
            {
                var scene_object = Areas.First().Value.FindObject(s);
                var func = scene_object.GetFunction<LuckyAdventureMachineFunction>();
                func?.TimeCheckRefresh(scene_object);
            });
        }

        protected internal override void OnLeaved(SceneCharacter character)
        {
            base.OnLeaved(character);

            var machine_list = Areas.First().Value.Objects.Values.Where(o => LuckyAdventureFunction.LuckyMachineType.Contains(o.Type));
            machine_list.ForEach(m =>
            {
                var func = m.GetFunction<LuckyAdventureMachineFunction>();
                var record = func.GetRecord(m, false);
                if (record.SubCharacters.Contains(character.ID))
                {
                    lock (record.SubCharacters)
                    {
                        record.SubCharacters.Remove(character.ID);
                    }
                }
                if (record.LineUpCharacters.Contains(character.ID))
                {
                    lock (record.LineUpCharacters)
                    {
                        record.LineUpCharacters.Remove(character.ID);
                    }
                }
            });
        }

        public int GetIndex()
        {
            return RoomIndex;
        }

        public void SetIndex(int index)
        {
            RoomIndex = index;
            Save();
        }
    }
}
