using GameModel.Award;
using HelpBase.Json;
using Proto.GameModel;
using ProtoBuf;

namespace GameModel.Dimensions.FlightChess
{
    public sealed class FlightChessFlipCardGameEventFunction : FlightChessEventFunction
    {
        [JsonMember]
        public byte Type { get; set; }

        [JsonMember(FormatType = typeof(JsonListFormat<Option>))]
        public List<Option> Options { get; set; }

        public override FlightChessEvent CreateEvent(FlightChessPlayer player)
        {
            return new FlightChessFlipCardGameGameEvent(player, this);
        }

        public sealed class Option : JsonFormatObject
        {
            [JsonMember]
            public byte Times { get; set; }

            [JsonMember(FormatType = typeof(DynamicCollectionFormat<AwardBase, JsonDynamicList<AwardBase>>))]
            public JsonDynamicList<AwardBase> Awards { get; set; }
        }
    }

    public sealed class FlightChessFlipCardGameGameEvent : FlightChessInteractionEvent<FlightChessFlipCardGameEventFunction>
    {
        public FlightChessFlipCardGameGameEvent(FlightChessPlayer player, FlightChessFlipCardGameEventFunction template)
            : base(player, template)
        { }

        public override string Display => "翻牌游戏事件";

        [JsonMember]
        public byte Type => Template.Type;

        public override void Interact(ActionBytesMessage req)
        {
            var value = ProtoSerializer.Deserialize<Int32Message>(req.Bytes).Value;
            var option = Template.Options?.FindLast(p => p.Times <= value);
            if (option == null)
            {
                Player.ShowTipsMessage(PicaLocalityStrings.PKT_NSYS0000082);
                return;
            }

            Player.GetAwards(option.Awards, true);
            Complete();
        }
    }
}
