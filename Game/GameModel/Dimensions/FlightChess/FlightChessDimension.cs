#nullable enable

using GameModel.Dimensions.FlightChess;
using GameModel.Functions.SceneObjects;
using GameModel.Games;
using Proto.GameModel;

namespace GameModel.Dimensions
{
    public sealed class FlightChessDimension : GameDimension<GameScene, FlightChessGame, FlightChessPlayer>
    {
        private const string GroupProperty = "group";
        private const string EventsProperty = "events";
        private const string NextProperty = "next";

        private static readonly IReadOnlyList<string> GridSns = new List<string>() 
        {
            "64abc8b4e4e9290012697947",
            "64a7d8bee4e929001269789d",
            "64a7d916c863ac0020c03a1c",
            "64a7d9afe4e92900126978a2",
            "64a7d7cee4e9290012697899",
            "64a7d890c863ac0020c03a18",
        };

        public override DimensionType Type => DimensionType.StepStep;

        protected override bool TryCreateGame(SceneCharacter character, [Maybe<PERSON>ull<PERSON>hen(false)] out FlightChessGame game, [Maybe<PERSON><PERSON><PERSON>hen(true)] out PicaLocalityString error)
        {
            if (character == null) throw new ArgumentNullException(nameof(character));
            if (character.CurrentScene is not GameScene scene || scene.Dimension != this)
            {
                game = null;
                error = PicaLocalityStrings.PKT_NSYS0000165;
                return false;
            }
            var template = GameApplication.DataManager.FindValue<GameType>((long)Type);
            if (template == null)
            {
                game = null;
                error = PicaLocalityStrings.unknown;
                return false;
            }

            game = null;
            var valueType = (int)AttrValueType.StrVal;
            var roadGridBinders = new Dictionary<int, Dictionary<int, FlightChessGrid>>();
            foreach (var areaEntry in scene.Areas)
            {
                foreach (var obj in areaEntry.Value.FindObject(p => GridSns.Contains(p.TypeData.Sn)))
                {
                    var record = obj.GetFunctionRecord<PicaBuildingAttrFunctionRecord>(false);
                    if (record == null) continue;
                    var label = record.Attrs.Find(p => p.Key == GroupProperty && p.ValueType == valueType);
                    if (label == null) continue;
                    if (string.IsNullOrEmpty(label.ValueData)) continue;
                    if (!FlightChessPoint.TryParse(label.ValueData, out var point))
                    {
                        Logger.GameError.Write("{0} game. obj:{1} invalid group data", nameof(FlightChessDimension), obj.ID);
                        error = PicaLocalityStrings.GetUndefinedString("{0} game. obj:{1} invalid group data", nameof(FlightChessDimension), obj.ID);
                        return false;
                    }
                    if (!roadGridBinders.TryGetValue(point.Road, out var gridBinders))
                    {
                        gridBinders = new Dictionary<int, FlightChessGrid>();
                        roadGridBinders.Add(point.Road, gridBinders);
                    }
                    List<FlightChessEventType>? events = null;
                    label = record.Attrs.Find(p => p.Key == EventsProperty && p.ValueType == valueType);
                    if (label != null && !string.IsNullOrEmpty(label.ValueData))
                    {
                        foreach (var item in label.ValueData.Split(',', StringSplitOptions.RemoveEmptyEntries))
                        {
                            if (!long.TryParse(item, out var key))
                            {
                                Logger.GameError.Write("{0} game. obj:{1} invalid events data", nameof(FlightChessDimension), obj.ID);
                                error = PicaLocalityStrings.GetUndefinedString("{0} game. obj:{1} invalid events data", nameof(FlightChessDimension), obj.ID);
                                return false;
                            }
                            var evt = GameApplication.DataManager.FindValue<FlightChessEventType>(key);
                            if (evt == null)
                            {
                                Logger.GameError.Write("{0} game. obj:{1} event:{2} not found", nameof(FlightChessDimension), obj.ID, key);
                                error = PicaLocalityStrings.GetUndefinedString("{0} game. obj:{1} event:{2} not found", nameof(FlightChessDimension), obj.ID, key);
                                return false;
                            }

                            events ??= new List<FlightChessEventType>();
                            events.Add(evt);
                        }
                    }

                    FlightChessPoint? next = null;
                    label = record.Attrs.Find(p => p.Key == NextProperty && p.ValueType == valueType);
                    if (label != null && !string.IsNullOrEmpty(label.ValueData))
                    {
                        if (!FlightChessPoint.TryParse(label.ValueData, out var nextPoint))
                        {
                            Logger.GameError.Write("{0} game. obj:{1} invalid next data", nameof(FlightChessDimension), obj.ID);
                            error = PicaLocalityStrings.GetUndefinedString("{0} game. obj:{1} invalid next data", nameof(FlightChessDimension), obj.ID);
                            return false;
                        }

                        next = nextPoint;
                    }

                    var grid = new FlightChessGrid(point.Grid, obj, next, events);
                    gridBinders.Add(grid.Number, grid);
                }
            }

            if (roadGridBinders.Count == 0)
            {
                error = PicaLocalityStrings.PKT_NSYS0000088;
                return false;
            }
            var roads = new Dictionary<int, FlightChessRoad>(roadGridBinders.Count);
            foreach (var gridGroupEntry in roadGridBinders)
            {
                var grids = gridGroupEntry.Value;
                roads[gridGroupEntry.Key] = new FlightChessRoad(gridGroupEntry.Key, grids);
            }
            foreach (var road in roads.Values)
            {
                foreach (var grid in road.Grids)
                {
                    if (grid.Next.HasValue)
                    {
                        var next = grid.Next!.Value;
                        if (next.Road == road.Number && next.Grid == grid.Number)
                        {
                            error = PicaLocalityStrings.PKT_NSYS0000082;
                            var point = new FlightChessPoint(road.Number, grid.Number);
                            Logger.GameError.Write("game:{0} node:{1} next:{2} is self", ID, point, next);
                            return false;
                        }
                        if (!roads.TryGetValue(next.Road, out var nextRoad))
                        {
                            error = PicaLocalityStrings.PKT_NSYS0000082;
                            var point = new FlightChessPoint(road.Number, grid.Number);
                            Logger.GameError.Write("game:{0} node:{1} next:{2} road not found", ID, point, next);
                            return false;
                        }
                        if (!nextRoad.TryGetGrid(next.Grid, out _))
                        {
                            error = PicaLocalityStrings.PKT_NSYS0000082;
                            var point = new FlightChessPoint(road.Number, grid.Number);
                            Logger.GameError.Write("game:{0} node:{1} next:{2} grid not found", ID, point, next);
                            return false;
                        }
                    }
                    if (grid.Events != null)
                    {
                        foreach (var branchEventType in grid.Events.Where(p => p.Function is FlightChessBranchEventFunction))
                        {
                            var branchEventFunction = (FlightChessBranchEventFunction)branchEventType.Function;
                            foreach (var branch in branchEventFunction.Branches)
                            {
                                if (!roads.TryGetValue(branch.Group, out var branchRoad))
                                {
                                    error = PicaLocalityStrings.PKT_NSYS0000082;
                                    var point = new FlightChessPoint(road.Number, grid.Number);
                                    var branchPoint = new FlightChessPoint(branch.Group, branch.Number);
                                    Logger.GameError.Write("game:{0} node:{1} branch:{2} road not found", ID, point, branchPoint);
                                    return false;
                                }
                                if (!branchRoad.TryGetGrid(branch.Number, out _))
                                {
                                    error = PicaLocalityStrings.PKT_NSYS0000082;
                                    var point = new FlightChessPoint(road.Number, grid.Number);
                                    var branchPoint = new FlightChessPoint(branch.Group, branch.Number);
                                    Logger.GameError.Write("game:{0} node:{1} branch:{2} grid not found", ID, point, branchPoint);
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

            var chessGame = new FlightChessGame(template, roads);

            error = null;
            game = chessGame;
            return true;
        }

        public override bool TryCreateGamePlayer(SceneCharacter character, [MaybeNullWhen(false)] out FlightChessPlayer player, [MaybeNullWhen(true)] out PicaLocalityString error)
        {
            if (character == null) throw new ArgumentNullException(nameof(character));

            if (!TryGetGame(character, out var game, out error))
            {
                player = null;
                return false;
            }

            player = new FlightChessPlayer(game, character.Owner);
            return true;
        }

        public override bool TryEnterAfterRelogin(SceneCharacter character)
        {
            if (character.CurrentScene is GameScene current && current.Dimension == this && character.GamePlayer is FlightChessPlayer)
            {
                Enter(character, current.DimensionLevel);
                return true;
            }

            return false;
        }
    }
}
