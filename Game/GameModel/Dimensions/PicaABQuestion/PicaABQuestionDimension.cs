using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions
{
    public class PicaABQuestionDimension : PicaDimension<PicaABQuestionScene>
    {
        public override DimensionType Type => DimensionType.PicaABQuestion;

        public override StatisticsSceneType? StatisticsType => StatisticsSceneType.小游戏;

        public override bool IsVisited => false;

        public override bool TryEnterAfterRelogin(SceneCharacter character)
        {
            if (character.CurrentScene is PicaABQuestionScene current && current.Dimension == this && current.InABQuestionGaming() && current.EnterRule < SceneEnterRule.Closing)
            {
                current.Enter(character);
                return true;
            }

            return false;
        }
    }
}
