using HelpBase.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions
{
    public class PicaRandomCreateElementOnCapsuleFunction : SceneFunction
    {
        [JsonMember(FormatType = typeof(JsonDictionaryFormat<long, int>))]
        public Dictionary<long, int> RandomElement { set; get; } = new Dictionary<long, int>();

        public List<long> GetInvisableElements()
        {
            var res = new List<long>();

            foreach (var element in RandomElement)
            {
                var invisable = RandomEvent.Next(0, 100) > element.Value;
                if (invisable)
                {
                    res.Add(element.Key);
                }
            }

            return res;
        }
    }

}
