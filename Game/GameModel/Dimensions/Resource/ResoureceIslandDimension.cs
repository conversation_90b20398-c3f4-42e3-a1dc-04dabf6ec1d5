using System;
using System.Linq;
using Proto.GameModel;

namespace GameModel.Dimensions
{
    public class ResoureceIslandDimension : Dimension<ResoureceIslandScene>
    {
        public override DimensionType Type => DimensionType.ResoureceIsland;

        public override Scene GetAvailableScene(SceneCharacter sc, DimensionLevel level)
        {
            if (sc == null)
                throw new ArgumentNullException(nameof(sc));
            if (level == null)
                throw new ArgumentNullException(nameof(level));

            var scene = level.Scenes.Values.Where(p => p.PlayerCount < p.TypeData.MaxPlayerCount).OrderBy(p => p.PlayerCount).FirstOrDefault();
            return scene ?? level.CreateScene(Owner);
        }
    }
}
