using Proto.GameModel;

namespace GameModel.Dimensions
{
    public class PicaExploreLevelDimension : PicaDimension<PicaExploreLevelScene>
    {
        public sealed override bool IsVisited => false;

        public override DimensionType Type => DimensionType.PicaExploreLevel;

        protected override SceneExpandType ExpandType => SceneExpandType.SinglePlayerScene;

        public override StatisticsSceneType? StatisticsType => StatisticsSceneType.寻物;

        protected override bool UseSystemForSceneOwner => false;

        public override bool TryEnterAfterRelogin(SceneCharacter character)
        {
            if (character.CurrentScene != null && 
                character.CurrentScene is PicaExploreLevelScene current &&
                current.Dimension == this &&
                current.EnterRule < SceneEnterRule.Closing &&
                current.InGaming())
            {
                current.Enter(character);
                return true;
            }

            return false;
        }
    }
}
