using GameModel.Activities;
using GameModel.Functions.SceneObjects;
using HelpBase;
using HelpBase.Linq;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Numerics;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions.PaoPao
{
    public class PaoPaoScene : GameScene
    {
        public static int CloseSceneTime = 30;
        

        public override bool CanPetEnter => false;

        private PaoPaoDimension PaoPaoDimension => Dimension as PaoPaoDimension;

        public PaoPaoGame Game => PaoPaoDimension.Game;

   
        public Area Area => Areas.Values.First();

        private List<PaoPaoPlayer> GamePlayers
        {
            get
            {
                return FindArea(0).Players.Values.Select(p => p.GamePlayer as PaoPaoPlayer).ToList();
            }
        }

        Dictionary<int, List<Vector3>> TeamSpawnPoints;
        Dictionary<string, List<Vector3>> PaoPaoPositions;

        PaoPaoGameData GameData = new PaoPaoGameData();

        public override void InitScene()
        {
            base.InitScene();
            TeamSpawnPoints = new Dictionary<int, List<Vector3>>();
            foreach (var areaEntry in Areas)
            {
                foreach (var obj in areaEntry.Value.Objects)
                {
                    var so = obj.Value;
                    if (so.TypeData.Sn == "6819855db2d749002668974c") // 不同队伍的出生点物件
                    {
                        var rc = so.GetFunctionRecord<PicaBuildingAttrFunctionRecord>(false);
                        if (rc != null)
                        {
                            var team = (int)rc.GetAttr("team");
                            if (!TeamSpawnPoints.TryGetValue(team, out var positions))
                            {
                                positions = new List<Vector3>();
                                TeamSpawnPoints.TryAdd(team, positions);
                            }
                            positions.Add(so.Transform.Position);
                        }
                    }
                }
            }

        }

        protected internal override void OnEntered(SceneCharacter character)
        {
            base.OnEntered(character);
        }

        protected internal override void OnLeaved(SceneCharacter character)
        {
            base.OnLeaved(character);
            if (GameStatus == PaoPaoStage.游戏中)
            {
                character.GamePlayer?.Stop();
                //var player = GameData.Players.FirstOrDefault(p => p.Character.Id == character.ID);
                //if (player != null && player.State < PaoPaoPlayerState.死亡)
                //{
                //    player.State = PaoPaoPlayerState.离线;
                //    BroadcastGameData();
                //    //CheckGameOver();
                //}
            }
        }

        public override void OnAllCharacterReadyComplete()
        {
            if (GameStatus > 0) return;
            base.OnAllCharacterReadyComplete();
            //SendToSceneService(SceneManagerClientMethod.PaoPaoGameStart, new SceneAreaMessage { SceneID = ID, AreaID = Areas.Values.First().ID });
            ShowCloudMask();
        }

        #region 游戏流程
        private DelayAwaiter delayAwaiter;
        public PaoPaoStage GameStatus;

        private void ShowCloudMask()
        {
            GameStatus = PaoPaoStage.显示过场动画;
            FindArea(0).Players.Values.ForEach(p =>
            {
                try
                {
                    if (!PaoPaoDimension.TryCreateGamePlayer(p, out var player, out var error))
                    {
                        p.Owner.ShowTipsMessage(error);
                        return;
                    }
                    p.GamePlayer = player;
                    p.GamePlayer.Start();
                    player.GameStage = PaoPaoStage.显示过场动画;
                    player.UpdatePaoPaoStage();
                }
                catch (Exception e)
                {
                    Logger.Error.Write($"{nameof(PaoPaoScene)} ShowCloudMask error: {e.ToString()}");
                }
            });

            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(2.5), GameReady);
        }

        private void GameReady()
        {
            if (!CheckHasPlayers()) return;
            GameStatus = PaoPaoStage.准备开始;
            GameData = new PaoPaoGameData();

            var teams = TeamSpawnPoints.Keys.ToList();
            GamePlayerForEach(p =>
            {
                p.StartGame();
                var team = teams.Random(true);
                var point = TeamSpawnPoints[team].Random();
                p.Team = team;
                p.Owner.SceneCharacter.DoAction(new ActorActionListData().ChangePosition(point));
                p.GameStage = PaoPaoStage.准备开始;
                p.UpdatePaoPaoStage(DateTimeExtension.Now.AddSeconds(3).ToTimestamp());
                GameData.Players.Add(p.GetPlayerState());
            });

            BroadcastGameData();
            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(3), OnGameStart);
        }

        private void OnGameStart()
        {
            if (!CheckHasPlayers()) return;
            GameStatus = PaoPaoStage.游戏中;
            CreatePaoPaoTimer();
            GamePlayerForEach(p =>
            {
                p.GameStage = PaoPaoStage.游戏中;
                p.UpdatePaoPaoStage(DateTimeExtension.Now.Add(Game.Config.GameTime).ToTimestamp());
            });

            delayAwaiter = TimerManager.Delay(Game.Config.GameTime, OnGameShowResult);
        }

        private void OnGameShowResult()
        {
            //if (!CheckHasPlayers()) return;
            delayAwaiter.Cancel();
            GameStatus = PaoPaoStage.显示结果;

            TimerManager.Release(ref PaoPaoTimer);

            GamePlayerForEach(p =>
            {
                p.GameStage = PaoPaoStage.显示结果;
                p.UpdatePaoPaoStage(3);
                //p.Owner.SendGoogleProto(ClientMethod.ShowPaoPaoResult, GameData);
            });
            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(3), OnGameOver);
        }

        private void OnGameOver()
        {
            //if (!CheckHasPlayers()) return;
            GameStatus = PaoPaoStage.游戏结束;

            // 显示排行结果 结算 
            ShowRanking();

            GamePlayerForEach(p =>
            {
                p.GameStage = PaoPaoStage.游戏结束;
                p.UpdatePaoPaoStage(DateTimeExtension.Now.Add(TimeSpan.FromSeconds(CloseSceneTime)).ToTimestamp());
                p.Stop();
            });
            Closed += OnSceneClosed;
            Close(CloseSceneTime);
        }

        private void ShowRanking()
        {
            var activity = GameApplication.ActivityManager.FirstActivity<PaoPaoActivity>();
            if (activity != null)
            {
                var data = new PicaMiniGameResultData();
                data.MiniGameCode = MiniGameCodeType.PaoPao;
                GameData.Players.ForEach(p =>
                {
                    data.ActorDatas.Add(new PicaMiniGameActorScore()
                    {
                        Id = p.Character.Id,
                        Score = p.Score,
                        Team = p.Team,
                    });
                });
                activity.ShowRankingResult(this, data);
            }
        }

        private void OnSceneClosed(object sender, EventArgs e)
        {
            foreach (var sceneCharacter in FindArea(0).Players.Values)
            {
                if (!sceneCharacter.Owner.IsOnline) continue;
                sceneCharacter.Owner.EnterPicaPublicScene(DimensionType.PicaLobby, 135);
            }
        }
        #endregion

        TimeAction PaoPaoTimer;
        TimeSpan PaoPaoTimeSpan;
        List<PaoPaoGenerateConfiguration> TempPaoPaoGenerateConfiguration;
        private void CreatePaoPaoTimer()
        {
            PaoPaoPositions = new();
            foreach (var areaEntry in Areas)
            {
                foreach (var obj in areaEntry.Value.Objects)
                {
                    var so = obj.Value;
                    if (Game.Config.PaoPaoGenerateSN.Contains(so.TypeData.Sn))
                    {
                        if (!PaoPaoPositions.ContainsKey(so.TypeData.Sn))
                        {
                            PaoPaoPositions.TryAdd(so.TypeData.Sn, new List<Vector3>());
                        }
                        PaoPaoPositions[so.TypeData.Sn].Add(obj.Value.Transform.Position);
                    }
                }
            }

            TempPaoPaoGenerateConfiguration = new List<PaoPaoGenerateConfiguration>();
            foreach (var fwConfig in Game.Config.PaoPaoGenerateConfiguration)
            {
                TempPaoPaoGenerateConfiguration.Add(fwConfig);
            }
            TempPaoPaoGenerateConfiguration = TempPaoPaoGenerateConfiguration.OrderByDescending(p => p.TimePoint).ToList();

            PaoPaoTimer = TimerManager.Run(TimeSpan.FromSeconds(1.2f), OnPaoPaoTimerCheck);
        }

        private void OnPaoPaoTimerCheck()
        {
            PaoPaoTimeSpan = PaoPaoTimeSpan.Add(TimeSpan.FromSeconds(1.2f));
            foreach (var fwConfig in TempPaoPaoGenerateConfiguration)
            {
                if (PaoPaoTimeSpan >= fwConfig.TimePoint)
                {
                    TempPaoPaoGenerateConfiguration.Remove(fwConfig);
                    var playerCount = GamePlayers.Count;
                    var genCount = (int)fwConfig.PaoPaoCountEval(playerCount);
                    var positions = PaoPaoPositions[fwConfig.PointSn].Randoms(genCount);
                    foreach (var v3 in positions)
                    {
                        var transform = new Transform()
                        {
                            Direction = Direction.LOWER_LEFT,
                            Position = v3,
                        };
                        Area.CreateTempObject(Character.System, fwConfig.ElementId, transform, "泡泡乐园");
                    }
                    return;
                }
            }
        }





        #region 游戏功能
        /// <summary>
        /// 安全遍历玩家
        /// </summary>
        public void GamePlayerForEach(Action<PaoPaoPlayer> action)
        {
            GamePlayers.ForEach(p =>
            {
                try
                {
                    action(p);
                }
                catch (Exception e)
                {
                    Logger.Error.Write($"{nameof(PaoPaoScene)} error: {e.ToString()}");
                }
            });
        }

        public PaoPaoPlayer FindPlayerByTeam(int team)
        {
            return GamePlayers.FirstOrDefault(p => p.Team == team);
        }

        private bool CheckHasPlayers()
        {
            if (GamePlayers == null || GamePlayers.Count == 0)
            {
                Close();
                return false;
            }
            return true;
        }

        /// <summary>
        /// 广播游戏数据
        /// </summary>
        public void BroadcastGameData()
        {
            var gamePlayers = GamePlayers;
            GameData.Players.ForEach(p =>
            {
                try
                {
                    var player = gamePlayers.FirstOrDefault(x => x.ID == p.Character.Id);
                    if (player != null)
                    {
                        p.Score = player.Score;
                    }
                }
                catch (Exception e)
                {
                    Logger.Error.Write($"{nameof(PaoPaoScene)} BroadcastGameData error: {e.ToString()}");
                }
            });

            Broadcast(ClientMethod.UpdatePaoPaoGameData, GameData);
        }

        #endregion


    }
}
