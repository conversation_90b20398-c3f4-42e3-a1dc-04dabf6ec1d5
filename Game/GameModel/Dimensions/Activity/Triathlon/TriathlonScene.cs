using GameModel.Activities;
using GameModel.Functions.SceneObjects;
using GameModel.Managers;
using HelpBase;
using HelpBase.Linq;
using Proto.GameModel;

namespace GameModel.Dimensions.Triathlon
{
    public class TriathlonScene : GameScene
    {
        public static int CloseSceneTime = 30;


        private TriathlonDimension TriathlonDimension => Dimension as TriathlonDimension;

        private TriathlonGame Game => TriathlonDimension.Game;

        private List<TriathlonPlayer> GamePlayers
        {
            get
            {
                return FindArea(0).Players.Values.Select(p => p.GamePlayer as TriathlonPlayer).ToList();
            }
        }

        private Area Area
        {
            get
            {
                return FindArea(0);
            }
        }

        private DelayAwaiter delayAwaiter;
        public TriathlonStatus GameStatus;
        public DateTime GameStartTime;

        private TriathlonActivity _activity;
        private TriathlonActivity Activity
        {
            get
            {
                if (_activity == null)
                {
                    _activity = GameApplication.ActivityManager.FirstActivity<TriathlonActivity>(p => p.TargetSceneId == TypeData.ID);
                }
                return _activity;
            }
        }

        public override void CreateSuccess(int createDepth = 0)
        {
            base.CreateSuccess(createDepth);
            var srecord = GetFunctionRecord<TriathlonSceneFunctionRecord>(true);
            srecord.StartTime = Activity == null ? 0 : Activity.GetNextActivityTime(DateTimeExtension.Now).ToTimestamp(); // 理论上没有活动这边是进不来的，这里只是不让他报错
        }

        public override bool CanEnter(Character character, string password, bool attemptPrivileged, bool showOnError)
        {
            if (Activity == null)
            {
                if (showOnError) character.ShowMessage(I18nManager.I18n("PKT_SYS2_0000214"));
                return false;
            }
            if (!Activity.CanEnter(character))
            {
                if (showOnError) character.ShowMessage(I18nManager.I18n("PKT_SYS2_0000215"));
                return false;
            }
            if (!Activity.HasShow(character))
            {
                if (showOnError) character.ShowMessage(LocalityStrings.limitedLv);
                return false;
            }
            var now = DateTimeExtension.Now;
            var nextTime = Activity.GetNextActivityTime(now);
            if (nextTime == now)
            {
                character.ShowMessage(I18nManager.I18n("PKT_SYS2_0000268"));
                return false;
            }
            if (Activity.IsInPersonalActivity() || GameStatus > 0)
            {
                character.ShowMessage(I18nManager.I18n("PKT_SYS2_0000269", nextTime.ToString("MM月dd日 HH时mm分")));
                return false;
            }
            return base.CanEnter(character, password, attemptPrivileged, showOnError);
        }

        public override void OnAllCharacterReadyComplete()
        {
            if (GameStatus > 0) return;
            base.OnAllCharacterReadyComplete();

            //ShowCloudMask();
        }

        public override void OnReadyEnterScene(SceneCharacter character)
        {
            base.OnReadyEnterScene(character);
            var srecord = GetFunctionRecord<TriathlonSceneFunctionRecord>(true);
            var nextTime = Activity.GetNextActivityTime(DateTimeExtension.Now);
            if (srecord.StartTime != nextTime.ToTimestamp())
            {
                srecord.StartTime = nextTime.ToTimestamp();
                srecord.UpdateTo();
            }
        }

        protected internal override void OnLeaved(SceneCharacter character)
        {
            base.OnLeaved(character);
            if (!IsInGaming()) return;

            character.GamePlayer?.Stop();
        }

        public void OnActivityStart(TriathlonActivity af)
        {
            if (GameStatus > 0) return;
            var srecord = GetFunctionRecord<TriathlonSceneFunctionRecord>(true);
            srecord.StartTime = 0;
            srecord.IsStarted = true;
            srecord.UpdateTo();
            ShowCloudMask();
        }

        public void OnActivityEnd()
        {
            //Closed += PicaABQuestionScene_Closed;
            //DimensionLevel.Remove(this);
            //Close(PicaABQuestionActivity.CloseTime);
        }

        #region 游戏流程
        private void ShowCloudMask()
        {
            GameStatus = TriathlonStatus.ShowCloud;
            FindArea(0).Players.Values.ForEach(p =>
            {
                if (!TriathlonDimension.TryCreateGamePlayer(p, out var player, out var error))
                {
                    p.Owner.ShowTipsMessage(error);
                    return;
                }
                p.GamePlayer = player;
                p.GamePlayer.Start();
            });

            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(2.5d), GameReady);
        }

        private void GameReady()
        {
            if (CheckCloseScene()) return;

            GameStatus = TriathlonStatus.Ready;
            //var spawns = TypeData.SceneNode.getSpawnPointNode().ToList();
            var startingPoints = Game.StartingPoints.ToList();
            GamePlayersForEach(p =>
            {
                var point = startingPoints.Random(true);
                if (point != null)
                {
                    p.Owner.SceneCharacter.SetPosition(point.Transform.Position);
                }
                else
                {
                    Logger.Error.Write("TriathlonScene GameReady StartingPoints is null");
                }
                p.GameReady();
            });

            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(3d), OnGameStart); //Game.Config.GameReadyDuration
        }

        private void OnGameStart()
        {
            if (CheckCloseScene()) return;

            GameStatus = TriathlonStatus.Gaming;
            GamePlayersForEach(p =>
            {
                p.GameStart();
                Activity.WriteStartMiniGame(p.Owner);
            });
            //CreateAirdropTimer();
            //CreateFireWorksTimer();
            //CreateMonsterTimer();
            //InitWallObject();

            ReportGameData();
            GameTrigger(p => p.GameStart());
            GameStartTime = DateTimeExtension.Now;

            delayAwaiter = TimerManager.Delay(Game.Config.GetTotalGameDuration(TypeData.ID), () => { OnGameShowResult(false); });
        }

        /// <summary>
        /// true:所有人完成比赛   false:时间到
        /// </summary>
        private void OnGameShowResult(bool success)
        {
            if (!IsInGaming()) return;
            if (CheckCloseScene()) return;

            GameStatus = TriathlonStatus.ShowResult;
            GamePlayersForEach(p => p.ShowResult(success));
            delayAwaiter = TimerManager.Delay(TimeSpan.FromSeconds(2), () => { OnGameOver(); });
        }


        private void OnGameOver()
        {
            if (CheckCloseScene()) return;

            GameStatus = TriathlonStatus.End;
            delayAwaiter.Cancel();
            //TimerManager.Release(ref AirdropTimer);
            //TimerManager.Release(ref FireWorksTimer);
            //TimerManager.Release(ref MonsterTimer);

            var srecord = GetFunctionRecord<TriathlonSceneFunctionRecord>(true);
            srecord.IsStarted = false;
            srecord.UpdateTo();

            // 显示排行结果 结算 
            ShowRanking();

            GameTrigger(p => p.GameEnd());
            GamePlayersForEach(p => p?.GameEnd());
            DimensionLevel.Remove(this);
            Closed += OnSceneClosed;
            Close(CloseSceneTime);
        }

        private void ShowRanking()
        {
            if (Activity != null)
            {
                var data = new PicaMiniGameResultData();
                data.MiniGameCode = Activity.MiniGameCode;
                GamePlayersForEach(p =>
                {
                    if (p != null)
                    {
                        data.ActorDatas.Add(p.ActorScoreData);
                    }
                });
                Activity.ShowRankingResult(this, data, false);
            }
        }

        private void OnSceneClosed(object sender, EventArgs e)
        {
            foreach (var sceneCharacter in FindArea(0).Players.Values)
            {
                if (!sceneCharacter.Owner.IsOnline) continue;
                sceneCharacter.Owner.EnterPicaPublicScene(DimensionType.PicaLobby, 135);
            }
        }

        #endregion

        #region 游戏功能
        public bool IsInGaming()
        {
            return GameStatus == TriathlonStatus.Gaming;
        }

        public bool IsGameOver()
        {
            return GameStatus >= TriathlonStatus.ShowResult;
        }

        public void UpdatePlayerDistance(TriathlonPlayerDistanceData data) 
        {
            if (!IsInGaming()) return;
            GamePlayersForEach(p =>
            {
                if (p.IsComplete()) return;
                var pdd = data.Datas.FirstOrDefault(d => d.Id == p.ID);
                if (pdd != null)
                {
                    p.UpdateDistance(Game.TriathlonDistanceManager.GetTotalDistance(pdd.Position.ToVector3()));
                }
            });
            ReportGameData();
        }

        public void FinishedCompetition(TriathlonPlayer player)
        {
            if (!IsInGaming()) return;
            player.FinishedCompetition();

            if (!CheckGameOver())
            {
                player.Owner.ShowMessage("结束赛程，请等待其他玩家。如提前退出奖无法获得奖励");
            }
        }

        private bool CheckGameOver()
        {
            if (GamePlayers.FirstOrDefault(p => !p.IsComplete()) == null)
            {
                OnGameShowResult(true);
                return true;
            }
            return false;
        }

        private void GamePlayersForEach(Action<TriathlonPlayer> action)
        {
            GamePlayers.ForEach((p) =>
            {
                try
                {
                    action(p);
                }
                catch (Exception ee)
                {
                    Logger.Error.Write(ee.Message + ee.StackTrace);
                }
            });
        }

        private bool CheckCloseScene()
        {
            if (GamePlayers == null || GamePlayers.Count == 0 || Game == null)
            {
                Close();
                return true;
            }
            return false;
        }
        #endregion



        private void GameTrigger(Action<TriathlonFunctionRecordBase> action)
        {
            Area.Objects.ForEach(o =>
            {
                if (o.Value.FunctionRecords == null) return;
                foreach (var item in o.Value.FunctionRecords)
                {
                    if (item is TriathlonFunctionRecordBase tfr)
                    {
                        action(tfr);
                    }
                }
            });
        }

        private void ReportGameData()
        {
            if (IsGameOver()) return;
            var data = new TriathlonGameData();
            var cd = GamePlayers
                .OrderBy(p => p.CompleteTime == DateTimeExtension.MinTime ? DateTime.MaxValue : p.CompleteTime)
                .ThenByDescending(p => p.CurrentDistance)
                .Select(p => p.TriathlonData) 
                .ToList();
            for (int i = 0; i < cd.Count(); i++)
            {
                cd[i].Rank = i + 1;
            }
            data.CharacterDatas.AddRange(cd);
            ProtoExtension.BroadCast(GamePlayers.Select(p => p.Owner), ClientMethod.UpdateTriathlonData, data);
        }

        public void TestCloseGame()
        {
            OnGameShowResult(false);
        }
    }
}
