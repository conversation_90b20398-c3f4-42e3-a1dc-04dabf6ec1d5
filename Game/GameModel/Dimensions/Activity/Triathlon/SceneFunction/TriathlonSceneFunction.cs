using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel
{
    public class TriathlonSceneFunction : SceneFunction<TriathlonSceneFunctionRecord>
    {
        public override void OnInitializeCompleted(Scene scene)
        {
            base.OnInitializeCompleted(scene);
            var rc = GetRecord(scene, true);
        }
    }
}
