using GameModel.Dimensions.Triathlon;
using Proto.GameModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GameModel.Dimensions
{
    public class RowingPlayer : ActivityGamePlayer<RowingGame, RowingScene, RowingGamePlayer>
    {
        internal override long InitialGameStateID => 90001;

        public int Step { get; set; }
        public DateTime CheckInTime { get; set; } = DateTime.MaxValue;


        public RowingPlayer(RowingGame game, Character owner) : base(game, owner)
        {
        }


        public bool HasShield()
        {
            return TryUseBuff<TriathlonShieldGameBuff>(out var buff);
        }

        public bool IsComplete()
        {
            return Step == int.MaxValue;
        }

        public override RowingGamePlayer PlayerProto()
        {
            return new RowingGamePlayer()
            {
                Character = Owner.MiniSpecData,
                Step = Step,
                TotalTime = (CheckInTime - GameScene.GameStartTime).TotalMilliseconds
            };
        }
    }
}
