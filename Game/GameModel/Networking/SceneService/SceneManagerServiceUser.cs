using ConnectionBase;
using ConnectionBase.Diagnostics;
using GameModel.Configurations;
using GameModel.Networking.SceneService;
using HelpBase.Threading;
using Proto.GameModel;

namespace GameModel.SceneService.Model
{
    public partial class SceneManagerServiceUser : ProtoCommandHostBase<SceneManagerServiceMethod, SceneManagerClientMethod, AbstractConnection>
    {
        private readonly AtomicInt32 scenes = new AtomicInt32();
        private SceneServiceStatusData status = new SceneServiceStatusData();

        public long ID => Account?.ID ?? 0;

        public string LogName => Name;

        public SceneServiceAccount Account { get; set; }

        public SceneServiceStatusData Status => status;

        public AtomicInt32 Scenes => scenes;

        public long Payload
        {
            get 
            {
                var config = GameApplication.Instance.GameConfig.SceneServerLoadBalance 
                    ?? SceneServerLoadBalanceConfiguration.Default;
                return scenes.Value * config.SingleScenePayload + status.UserCount * config.SingleUserPayload;
            }
        }

        protected override string GetProtoCommandName(short opcode)
        {
            return ((SceneManagerServiceMethod)opcode).ToString();
        }

        protected override void LogReceviceData(string command, IProtoObject p)
        {
            var logger = command == nameof(SceneManagerClientMethod.Synchronize) ? Logger.ReceiveSceneServiceTemp : Logger.ReceiveSceneService;
            if (logger.IsEnabled)
            {
                logger.Write("Receive[{0}][{1}][{2}]!", Name, command, p);
            }
        }

        public override bool SendGoogleProto(SceneManagerClientMethod opCode, IProtoObject data, CompressType ct = CompressType.None)
        {
            var connection = Connection;
            if (connection == null)
            {
                return false;
            }

            var length = connection.SendData(data, opCode, ct);
            var logger = opCode == SceneManagerClientMethod.Synchronize ? Logger.SendSceneServiceTemp : Logger.SendSceneService;
            if (logger.IsEnabled)
            {
                logger.Write("[{0}]发送谷歌协议[{1}][{2}]", LogName, opCode, data);
            }
            if (OpcodeManager.IsEnabled)
            {
                OpcodeManager.Gain(GetType(), (short)opCode).AddSend(length);
            }
            return true;
        }

        public override bool SendGoogleProtoByte(SceneManagerClientMethod opCode, byte[] datas, CompressType ct = CompressType.None)
        {
            if (Connection == null)
            {
                return false;
            }

            var length = Connection.SendOpcodeData((short)opCode, datas, ct);
            var logger = Logger.SendSceneService;
            if (logger.IsEnabled)
            {
                logger.Write("[{0}]发送谷歌协议Byte数组[{1}][{2}]", LogName, opCode, datas == null ? 0 : datas.Length);
            }
            if (OpcodeManager.IsEnabled)
            {
                OpcodeManager.Gain(GetType(), (short)opCode).AddSend(length);
            }
            return true;
        }
        
        public void SendSynchronize()
        {
            var st = new SystemTime();
            st.Time = DateTimeExtension.Now.ToTimestamp();
            st.Offset = TimeZoneInfo.Local.BaseUtcOffset.Ticks;

            var sd = new SynchronizationData();
            sd.SystemTime = st;
            SendGoogleProto(SceneManagerClientMethod.Synchronize, sd);
        }

        public void UpdateStatus(SceneServiceStatusData data)
        {
            if (data == null) throw new ArgumentNullException(nameof(data));

            status = data;
        }
    }
}