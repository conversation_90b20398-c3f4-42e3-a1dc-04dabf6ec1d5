<UserControl x:Class="WindowHost.HostView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
    xmlns:ce="http://icsharpcode.net/sharpdevelop/avalonedit"
    xmlns:mwbv="clr-namespace:ManagerWindowBase.View;assembly=ManagerWindowBase"
    mc:Ignorable="d" 
    d:DesignHeight="300" d:DesignWidth="500">
    <UserControl.Resources>
        <ResourceDictionary>
            <Style x:Key="userTab" TargetType="{x:Type TabControl}">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type TabControl}">
                            <DockPanel>
                                <DockPanel DockPanel.Dock="Left" LastChildFill="False" >
                                    <StackPanel IsItemsHost="True"  Panel.ZIndex="1"   Margin="0,0,0,-1"  />
                                </DockPanel>
                                <Border Grid.Row="1" BorderBrush="Black" BorderThickness="1" CornerRadius="0, 0, 0, 0" >
                                    <ContentPresenter ContentSource="SelectedContent" />
                                </Border>
                            </DockPanel>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="696*" />
            <ColumnDefinition Width="0*" />
        </Grid.ColumnDefinitions>
        <TabControl Name="MaintabControl" Grid.ColumnSpan="2">
            <TabControl.Resources>
                <Style TargetType="{x:Type TabItem}">
                    <Setter Property="Height" Value="25" />
                    <Setter Property="MinWidth" Value="80" />
                </Style>
            </TabControl.Resources>
            <TabItem Header="状态信息" Name="keyvalueroot">
                <DockPanel>
                    <Grid Margin="1,5,1,5" Height="40" DockPanel.Dock="Top">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="80" />
                            <ColumnDefinition />
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Name="button1" Click="button1_Click">保存数据</Button>
                        <Button Grid.Column="1" Name="launchBtn"  Click="launchBtn_Click">启动服务器</Button>
                    </Grid>
                    <DockPanel DockPanel.Dock="Top" >
                        <CheckBox Name="autoRefreshCheck" Padding="0,0,0,10">
                            自动刷新信息
                        </CheckBox>
                    </DockPanel>
                    <mwbv:KeyValueView Name="keyvalueview"></mwbv:KeyValueView>
                </DockPanel>

            </TabItem>
            <TabItem Header="日志信息" Name="loggerContainer">
                <mwbv:LogView Name="logview" ></mwbv:LogView>
            </TabItem>
            <TabItem Header="配置文件">
                <DockPanel Name="configRoot">
                </DockPanel>
            </TabItem>
            <TabItem Header="控制面板" Name="tabitem6" >
                <DockPanel >
                    <StackPanel DockPanel.Dock="Right" >
                        <StackPanel.Resources>
                            <Style TargetType="{x:Type Button}">
                                <Setter Property="Margin" Value="0,5,0,0" />
                            </Style>
                        </StackPanel.Resources>
                        <GroupBox Name="gp" >
                            <GroupBox.Header >
                                <TextBlock Text="选择脚本执行路径"></TextBlock>
                            </GroupBox.Header>
                            <StackPanel >
                                <RadioButton Name="checkserver" Margin="2,5,5,2">服务器</RadioButton>
                                <RadioButton Name="checkgame"  Margin="2,5,5,2" IsChecked="True">游戏</RadioButton>
                            </StackPanel>
                        </GroupBox>
                        <Button  Name="gamescript" Click="gamescript_Click" >执行脚本</Button>
                        <Button Content="卸载AppDomain"  Click="Button_Click_2"/>
                        <Button Content="加载AppDomain" Click="Button_Click_3" />
                    </StackPanel>
                    <DockPanel >
                        <TextBox Height="94" Name="putouttb" IsReadOnly="True" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Visible" DockPanel.Dock="Bottom" ></TextBox>

                        <DockPanel  DockPanel.Dock="Bottom">
                            <Button Name="clearputouttb" Click="clearputouttb_Click" Content="清除信息" DockPanel.Dock="Right" />
                            <StatusBar Name="sb1" Height="25" Background="LightPink"  DockPanel.Dock="Bottom"></StatusBar>
                        </DockPanel>
                        <DockPanel Name="ttp" DockPanel.Dock="Top">
                            
                        </DockPanel>
                    </DockPanel>
                </DockPanel>
            </TabItem>
            <TabItem Header="管理命令" Name="test">
                <DockPanel >
                    <TextBlock DockPanel.Dock="Top" Name="testResult">TestResult</TextBlock>
                    <StackPanel x:Name="panelCommands" Orientation="Vertical" MinWidth="60" HorizontalAlignment="Left" Margin="0,5,20,0">
                        <StackPanel.Resources>
                            <Style TargetType="{x:Type Button}">
                                <Setter Property="Margin" Value="0,0,0,10" />
                            </Style>
                        </StackPanel.Resources>
                    </StackPanel>
                </DockPanel>
            </TabItem>
            <TabItem Header="资源差异" Name="Diff">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid Grid.Row="0" HorizontalAlignment="Stretch" Margin="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="3*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Name="BasePathTextBoxTop" Text="X:/pre/WebRoot/Android" Margin="2,5,5,2" VerticalAlignment="Center" Grid.Column="0"/>
                        <Button Name="SetBasePathButtonTop" Content="设置基路径" Click="SetBasePathButtonClickTop" Margin="2,5,5,2" VerticalAlignment="Center" Grid.Column="1"/>
                    </Grid>
                    <DockPanel Grid.Row="1">
                        <StackPanel DockPanel.Dock="Right">
                            <StackPanel.Resources>
                                <Style TargetType="{x:Type Button}">
                                    <Setter Property="Margin" Value="0,5,0,0"/>
                                </Style>
                            </StackPanel.Resources>
                            <GroupBox Name="Functions">
                                <GroupBox.Header>
                                    <TextBlock Text="选择需要对比的版本"/>
                                </GroupBox.Header>
                                <StackPanel>
                                    <ComboBox Name="OldResources" Margin="2,5,5,2" DropDownOpened="SelectOldResources" Loaded="OldResources_Loaded" SelectionChanged="OldResources_SelectionChanged"/>
                                    <ComboBox Name="NewResources" Margin="2,5,5,2" DropDownOpened="SelectNewResources" Loaded="NewResources_Loaded" SelectionChanged="NewResources_SelectionChanged"/>
                                </StackPanel>
                            </GroupBox>
                            <CheckBox Name="ckboxShowTQResource" Click="ckboxShowTQResource_Click" IsChecked="True">显示 TQ Resource</CheckBox>
                            <Button Name="DiffFunction" Click="DiffClick">开始对比</Button>
                        </StackPanel>
                        <DockPanel>
                            <TextBox Name="Result" IsReadOnly="True" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Visible" DockPanel.Dock="Top"/>
                        </DockPanel>
                    </DockPanel>
                </Grid>
            </TabItem>
        </TabControl>
    </Grid>
</UserControl>
