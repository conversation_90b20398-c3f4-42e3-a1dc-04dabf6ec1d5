<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Fill Brushes -->
    <DrawingBrush x:Key="selectlitem" Stretch="Fill">
        <DrawingBrush.Drawing>
            <DrawingGroup>
                <DrawingGroup.Children>
                    <GeometryDrawing Brush="DarkMagenta"    Geometry="F1 M 194.761,88.5107L 480.745,88.5107C 491.238,88.5107 499.745,97.0172 499.745,107.511L 499.745,191.005C 499.745,201.498 491.238,210.005 480.745,210.005L 194.761,210.005C 184.268,210.005 175.761,201.498 175.761,191.005L 175.761,107.511C 175.761,97.0172 184.268,88.5107 194.761,88.5107 Z ">
                        <GeometryDrawing.Pen>
                            <Pen Thickness="0.5" LineJoin="Round">
                                <Pen.Brush>
                                    <LinearGradientBrush StartPoint="0.495441,0.0316216" EndPoint="0.495441,0.992094">
                                        <LinearGradientBrush.GradientStops>
                                            <GradientStop Color="#FF000000" Offset="0"/>
                                            <GradientStop Color="#FF7B7B7B" Offset="1"/>
                                        </LinearGradientBrush.GradientStops>
                                    </LinearGradientBrush>
                                </Pen.Brush>
                            </Pen>
                        </GeometryDrawing.Pen>
                    </GeometryDrawing>
                    <GeometryDrawing Geometry="F1 M 202.261,94.5105L 471.745,94.5105C 482.239,94.5105 498.745,103.017 498.745,113.51L 498.745,132.507C 498.745,143.001 482.239,151.507 471.745,151.507L 202.261,151.507C 191.767,151.507 183.261,143.001 183.261,132.507L 183.261,113.51C 183.261,103.017 191.767,94.5105 202.261,94.5105 Z ">
                        <GeometryDrawing.Brush>
                            <LinearGradientBrush StartPoint="0.495441,0.0337318" EndPoint="0.507596,0.990641">
                                <LinearGradientBrush.GradientStops>
                                    <GradientStop Color="#FFFFFFFF" Offset="0"/>
                                    <GradientStop Color="#32FFFFFF" Offset="1"/>
                                </LinearGradientBrush.GradientStops>
                            </LinearGradientBrush>
                        </GeometryDrawing.Brush>
                    </GeometryDrawing>
                </DrawingGroup.Children>
            </DrawingGroup>
        </DrawingBrush.Drawing>
    </DrawingBrush>
    
    <SolidColorBrush x:Key="ListBorder" Color="LightBlue"/>
    <SolidColorBrush x:Key="ListForeground" Color="Blue"/>
    <LinearGradientBrush x:Key="NormalBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Gold" Offset="0.0"/>
                <GradientStop Color="Maroon" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ListViewItemBackground" >
        <GradientStop Color="Snow"   Offset="0" />
        <GradientStop Color="LightBlue"   Offset="1" />
        <!--<GradientStop Color="Snow"  Offset="0.7" />
        <GradientStop Color="Gray"   Offset="1" />-->
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="HorizontalNormalBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Blue" Offset="0.0"/>
                <GradientStop Color="Red" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="LightBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Snow" Offset="0.0"/>
                <GradientStop Color="Purple" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="HorizontalLightBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Gold" Offset="0.0"/>
                <GradientStop Color="Green"  Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DarkBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Snow" Offset="0.0"/>
                <GradientStop Color="Gray" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PressedBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Blue"  Offset="0.0"/>
                <GradientStop Color="Red" Offset="0.1"/>
                <GradientStop Color="Pink" Offset="0.9"/>
                <GradientStop Color="Magenta" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="Gray" />

    <SolidColorBrush x:Key="DisabledBackgroundBrush" Color="Black" />

    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="LightCyan" />

    <SolidColorBrush x:Key="SelectedBackgroundBrush" Color="Red" />

    <!-- Border Brushes -->

    <LinearGradientBrush x:Key="NormalBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Goldenrod"  Offset="0.0"/>
                <GradientStop Color="Black" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="HorizontalNormalBorderBrush" StartPoint="0,0" EndPoint="1,0">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Yellow" Offset="0.0"/>
                <GradientStop Color="DarkBlue"  Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DefaultedBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="LightBlue"  Offset="0.0"/>
                <GradientStop Color="Snow" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="PressedBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="Snow" Offset="0.0"/>
                <GradientStop Color="DarkBlue" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <SolidColorBrush x:Key="DisabledBorderBrush" Color="DarkBlue" />

    <SolidColorBrush x:Key="SolidBorderBrush" Color="LightBlue"  />

    <SolidColorBrush x:Key="LightBorderBrush" Color="Gold" />

    <!-- Miscellaneous Brushes -->
    <SolidColorBrush x:Key="GlyphBrush" Color="Gainsboro" />

    <SolidColorBrush x:Key="LightColorBrush" Color="Firebrick" />

    <Style x:Key="{x:Static GridView.GridViewScrollViewerStyleKey}"
       TargetType="ScrollViewer">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <DockPanel Margin="{TemplateBinding Padding}">
                            <ScrollViewer DockPanel.Dock="Top"
              HorizontalScrollBarVisibility="Hidden"
              VerticalScrollBarVisibility="Hidden"
              Focusable="false">
                                <GridViewHeaderRowPresenter Margin="2,0,2,0"
                Columns="{Binding Path=TemplatedParent.View.Columns,
                          RelativeSource={RelativeSource TemplatedParent}}"
                ColumnHeaderContainerStyle="{Binding
                             Path=TemplatedParent.View.ColumnHeaderContainerStyle,
                             RelativeSource={RelativeSource TemplatedParent}}"
                ColumnHeaderTemplate="{Binding
                             Path=TemplatedParent.View.ColumnHeaderTemplate,
                             RelativeSource={RelativeSource TemplatedParent}}"
                ColumnHeaderTemplateSelector="{Binding 
                             Path=TemplatedParent.View.ColumnHeaderTemplateSelector,
                             RelativeSource={RelativeSource TemplatedParent}}"
                AllowsColumnReorder="{Binding
                             Path=TemplatedParent.View.AllowsColumnReorder,
                             RelativeSource={RelativeSource TemplatedParent}}"
                ColumnHeaderContextMenu="{Binding
                             Path=TemplatedParent.View.ColumnHeaderContextMenu,
                             RelativeSource={RelativeSource TemplatedParent}}"
                ColumnHeaderToolTip="{Binding
                             Path=TemplatedParent.View.ColumnHeaderToolTip,
                             RelativeSource={RelativeSource TemplatedParent}}"
                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                            </ScrollViewer>

                            <ScrollContentPresenter Name="PART_ScrollContentPresenter"
              KeyboardNavigation.DirectionalNavigation="Local"
              CanContentScroll="True" CanHorizontallyScroll="False" 
              CanVerticallyScroll="False"/>
                        </DockPanel>

                        <ScrollBar Name="PART_HorizontalScrollBar"
            Orientation="Horizontal"
            Grid.Row="1"
            Maximum="{TemplateBinding ScrollableWidth}"
            ViewportSize="{TemplateBinding ViewportWidth}"
            Value="{TemplateBinding HorizontalOffset}"
            Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"/>

                        <ScrollBar Name="PART_VerticalScrollBar"
            Grid.Column="1"
            Maximum="{TemplateBinding ScrollableHeight}"
            ViewportSize="{TemplateBinding ViewportHeight}"
            Value="{TemplateBinding VerticalOffset}"
            Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"/>

                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="GridViewColumnHeaderGripper" TargetType="Thumb">
        <Setter Property="Width" Value="18"/>
        <Setter Property="Background" Value="{StaticResource NormalBorderBrush}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Padding="{TemplateBinding Padding}"
          Background="Transparent">
                        <Rectangle HorizontalAlignment="Center"
            Width="2"
            Fill="{TemplateBinding Background}"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="GridViewColumnHeader"
       TargetType="GridViewColumnHeader">
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Foreground"
          Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GridViewColumnHeader">
                    <Grid>
                        <Border Name="HeaderBorder"
            BorderThickness="0,1,0,1"
            BorderBrush="{StaticResource NormalBorderBrush}"
            Background="{StaticResource LightBrush}"
            Padding="2,0,2,0">
                            <ContentPresenter Name="HeaderContent"
              Margin="0,0,0,1"
              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
              RecognizesAccessKey="True"
              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"/>
                        </Border>
                        <Thumb x:Name="PART_HeaderGripper"
            HorizontalAlignment="Right"
            Margin="0,0,-9,0"
            Style="{StaticResource GridViewColumnHeaderGripper}"/>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter TargetName="HeaderBorder"
                    Property="Background" Value="{StaticResource NormalBrush}"/>
                            <Setter Property="Foreground" Value="Snow"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter TargetName="HeaderBorder"
                    Property="Background" Value="{StaticResource PressedBrush}"/>
                            <Setter TargetName="HeaderContent"
                    Property="Margin" Value="1,1,0,0"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground"
                    Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="Role" Value="Floating">
                <Setter Property="Opacity" Value="0.7"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="GridViewColumnHeader">
                            <Canvas Name="PART_FloatingHeaderCanvas">
                                <Rectangle Fill="#60000000"
                Width="{TemplateBinding ActualWidth}"
                Height="{TemplateBinding ActualHeight}"/>
                            </Canvas>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="Role" Value="Padding">
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="GridViewColumnHeader">
                            <Border Name="HeaderBorder"
              BorderThickness="0,1,0,1"
              BorderBrush="{StaticResource NormalBorderBrush}"
              Background="{StaticResource LightBrush}"/>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="ListViewStyle" TargetType="ListView">
        <Setter Property="SnapsToDevicePixels" Value="true"/>
        <Setter Property="OverridesDefaultStyle" Value="true"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="true"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListView">
                    <Border Name="Border"
          BorderThickness="2"
          CornerRadius="0,0,12,12"
          Padding="2,2,2,2"
          BorderBrush="{StaticResource SolidBorderBrush}"
          Background="{StaticResource WindowBackgroundBrush}">
                        <ScrollViewer Style="{DynamicResource
                        {x:Static GridView.GridViewScrollViewerStyleKey}}">
                            <ItemsPresenter />
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsGrouping"
                   Value="true">
                            <Setter Property="ScrollViewer.CanContentScroll"
                    Value="false"/>
                        </Trigger>
                        <Trigger Property="IsEnabled"
                   Value="false">
                            <Setter TargetName="Border"
                    Property="Background"
                    Value="{StaticResource DisabledBorderBrush}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="ListViewItemStyle" TargetType="{x:Type ListViewItem}">
        <Setter Property="Background" Value="{StaticResource ListViewItemBackground}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ListBorder}"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Foreground" Value="{StaticResource ListForeground}"/>
        <Setter Property="FontSize" Value="13"/>
        <Setter Property="Height" Value="28"/>
        <Setter Property="Margin" Value="1"/>
        <Setter Property="Padding" Value="2,2,2,2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListViewItem}">
                    <Border Name="Bd" SnapsToDevicePixels="true" Background="{StaticResource ListViewItemBackground}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
                        <Grid Name="gd">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition/>
                            </Grid.ColumnDefinitions>
                      

                            <!--<Path Margin="6,0,0,0" SnapsToDevicePixels="true" Grid.Column="0" x:Name="Path" Width="6" Height="5" Stretch="Fill" StrokeThickness="1" StrokeMiterLimit="2.75" Stroke="{TemplateBinding BorderBrush}" Data="F1 M 0,0L 0,5L 6,5"/>-->

                            <Rectangle Margin="6,0" x:Name="whiteSquare" SnapsToDevicePixels="true" Grid.Column="1" Fill="Red" Width="3" Height="7">
                                <Rectangle.BitmapEffect>
                                    <OuterGlowBitmapEffect GlowColor="Red" GlowSize="0" Opacity="1"/>
                                </Rectangle.BitmapEffect>
                            </Rectangle>
                            <GridViewRowPresenter Name="gp">
                                <GridViewRowPresenter.BitmapEffect >
                                    <OuterGlowBitmapEffect GlowColor="Magenta"  GlowSize="0" Opacity="0.4"/>
                                </GridViewRowPresenter.BitmapEffect>
                            </GridViewRowPresenter>
                        </Grid>
                    </Border>
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="FadeIn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="gp" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.GlowSize)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00" Value="0"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="7.5"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="gp" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="1"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.5" Value="0.65"/>
                            </DoubleAnimationUsingKeyFrames>
                            <ColorAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Bd" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                <SplineColorKeyFrame KeyTime="00:00:00" Value="LightBlue"/>
                                <SplineColorKeyFrame KeyTime="00:00:00.75" Value="Red"/>
                            </ColorAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="whiteSquare" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.GlowSize)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00" Value="0"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="7.5"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="whiteSquare" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="1"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.5" Value="0.65"/>
                            </DoubleAnimationUsingKeyFrames>

                        </Storyboard>
                        <Storyboard x:Key="FadeOut">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="gp" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.GlowSize)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="7.5"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.5" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="gp" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00" Value="0.65"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="1"/>
                            </DoubleAnimationUsingKeyFrames>
                            
                            <ColorAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Bd" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                <SplineColorKeyFrame KeyTime="00:00:00.75" Value="LightBlue"/>
                                <SplineColorKeyFrame KeyTime="00:00:00" Value="Red" />
                            </ColorAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="whiteSquare" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.GlowSize)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="7.5"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.5" Value="0"/>
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="whiteSquare" Storyboard.TargetProperty="(UIElement.BitmapEffect).(OuterGlowBitmapEffect.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00" Value="0.65"/>
                                <SplineDoubleKeyFrame KeyTime="00:00:00.25" Value="1"/>
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FadeIn}"/>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource FadeOut}"/>
                            </Trigger.ExitActions>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Setter Property="Background" TargetName="Bd" Value="Orange"/>
                            <Setter Property="BorderBrush" TargetName="Bd" Value="Gold"/>
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.HighlightTextBrushKey}}"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true"/>
                                <Condition Property="Selector.IsSelectionActive" Value="false"/>
                            </MultiTrigger.Conditions>
                            <Setter Property="Background" TargetName="Bd" Value="{StaticResource selectlitem}"/>
                            <Setter Property="Foreground" Value="Snow"/>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>