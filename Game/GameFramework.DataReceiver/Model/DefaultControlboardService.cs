using System.Json;
using GameFramework.DataReceiver.Statistics;
using Grpc.Core;
using ScriptCompiler;

namespace GameFramework.DataReceiver.Model
{
    internal class DefaultControlboardService : ControlboardService.ControlboardServiceBase
    {
        public override Task<ExecuteScriptResponse> ExecuteScript(ExecuteScriptRequest request, ServerCallContext context)
        {
            var rsp = new ExecuteScriptResponse();
            var scriptSources = new string[] { request.Script };
            var compilerResult = CSharpScriptCompiler.CompileFromSource(scriptSources);
            if (!compilerResult.Success)
            {
                rsp.Message = string.Join(Environment.NewLine, compilerResult.Diagnostics);
                rsp.Type = ExecuteScriptResponseType.Faulted;
                return Task.FromResult(rsp);
            }
            var assembly = compilerResult.Assembly;
            var entryPoint = CSharpScriptAssembly.GetEntryPoint(assembly);
            if (entryPoint == null)
            {
                rsp.Message = "Program entry point not found";
                rsp.Type = ExecuteScriptResponseType.Faulted;
                return Task.FromResult(rsp);
            }

            object? result;
            try
            {
                result = entryPoint.Invoke(null, null);
            }
            catch (Exception ex)
            {
                rsp.Message = ex.ToString();
                rsp.Type = ExecuteScriptResponseType.Faulted;
                return Task.FromResult(rsp);
            }

            rsp.Message = result?.ToString();
            rsp.Type = ExecuteScriptResponseType.Success;
            return Task.FromResult(rsp);
        }

        public override Task<DetailsResponse> GetDetails(DetailsRequest request, ServerCallContext context)
        {
            var data = new JsonObject();
            var db = new JsonObject();
            var dbTelemetryProducer = new JsonTelemetryProducer(db);
            DbStatisticsGroup.Track(dbTelemetryProducer);
            data["db"] = db;

            var redis = new JsonObject();
            var redisTelemetryProducer = new JsonTelemetryProducer(redis);
            RedisStatisticsGroup.Track(redisTelemetryProducer);
            data["redis"] = redis;

            var rsp = new DetailsResponse();
            rsp.Data = data.ToString();
            return Task.FromResult(rsp);
        }

        public override Task<ChannelsResponse> GetChannels(ChannelsRequest request, ServerCallContext context)
        {
            var data = new JsonArray();
            foreach (var channel in Receiver.Instance.Channels.Values)
            {
                var json = new JsonObject();
                json["typename"] = channel.Typename;
                data.Add(json);
            }

            var rsp = new ChannelsResponse();
            rsp.Data = data.ToString();
            return Task.FromResult(rsp);
        }

        public override Task<ChannelDetailsResponse> GetChannelDetails(ChannelDetailsRequest request, ServerCallContext context)
        {
            if (request.Typename == null)
            {
                throw new RpcException(new Status(StatusCode.InvalidArgument, "typename is null"));
            }
            var rsp = new ChannelDetailsResponse();
            if (Receiver.Instance.Channels.TryGetValue(request.Typename, out var channel))
            {
                var data = new JsonObject();
                var telemetryProducer = new JsonTelemetryProducer(data);
                channel.Statistics.Track(telemetryProducer);
                data["typename"] = channel.Typename;

                rsp.Data = data.ToString();
            }

            return Task.FromResult(rsp);
        }

        public override async Task<NumberOfRowsToSaveResponse> GetNumberOfRowsToSave(NumberOfRowsToSaveRequest request, ServerCallContext context)
        {
            var rsp = new NumberOfRowsToSaveResponse();
            foreach (var channel in Receiver.Instance.Channels.Values)
            {
                rsp.Rows += await channel.GetNumberOfRowsToSaveAsync();
            }

            return rsp;
        }
    }
}