using DataBase;
using GameModel.Db;
using GameModel;
using System.Text;
using Microsoft.AspNetCore.Mvc;

namespace GameFramework.DataReceiver.Model
{
    public class PrometheusController : ControllerBase
    {
        [HttpGet("/prometheus/v1/padding-rows")]
        public IActionResult GetPaddingRows()
        {
            var receiver = Receiver.Instance;
            var sb = new StringBuilder();
            var serverID = receiver.Config.ServerID;
            foreach (var channel in receiver.Channels.Select(p => p.Value))
            {
                var cursor = channel.Cursor;
                sb.Append("async_producer_server_info{id=\"").Append(serverID)
                    .Append("\",server=\"").Append(channel.SchemaEntity.Name).Append("\"} ")
                    .Append(cursor.AllRows)
                    .AppendLine();
            }

            return Ok(sb.ToString());
        }
    }
}
