using System.Collections.Generic;

namespace Proto.GameModel
{
    public static class Constants
    {
        public static bool RecordLargeActivityStream = false;

        public const bool GuideOpen = true;
        //public const long PlaneGuideOver = 2;
        public const long GardenBuildDone = 11;
        //public const long GoToIWSgarden = 5;
        //public const long LeaveIWSgarden = 4;
        //public const long GuideEnterSquareStep = 5;
        //public const long GuideEnterLobbyStep = 6;
        //public const long GuideEnterRoomStep = 7;
        public const long NewGuideTotalSteps = 10;
        public const long FarmGuideTotalSteps = 2;
        public const long GardenGuideTotalSteps = 3;
        public const long RoomGuideTotalSteps = 8;
        public const long MissionGuideTotalSteps = 1;
        public const long PastureGuideTotalSteps = 5;

        public const int ServantGuideDone = 5;
        public const int CookGuideDone = 5;
        public const int EngineerGuideDone = 5;
        public const int StylistGuideDone = 5;
        public const int CookDishDoneStep = 7;
        public const int CookDishStartStep = 5;

        public const long EnterMineEntrance = 2;
        public const long GotoNoviceMineLevel1 = 9;
        public const long GotoNoviceMineLevel2 = 11;
        public const long GotoNoviceMineLevel3 = 15;
        public const long NoviceMineTotalStep = 17;


        //--
        //public const long GoToIWSgarden = 4;
        //public const long LeaveIWSgarden = 11;

        //public const long GuideEnterRoomStep = 12;
        //public const long GuideLeaveRoomStep = 19;

        //public const long GuideEnterLobbyStep = 3;

        public const long GuideCreateHome = 11;

        //--

        #region guide 8.0
        public const long GoToIWSgarden = 1;
        public const long LeaveIWSgarden = 3;

        public const long GuideEnterRoomStep = 15;
        public const long GuideEnterFarm = 11;

        public const long MaxNewGuideSteps = 12;

        public const long PetGuideEnterGarden = 2;
        public const long MaxPetGuideSteps = 2;

        public static int GotoGardenSecondTime = 6;
        public const long MaxPestureGuideSteps = 7;

        public const long GotoFarmFirstTime = 2;
        public const long MaxMapGuideSteps = 2;


        public const long MaxFishGuideSteps = 5;
        #endregion

        //public const long NovicePastureGoHome = 6;
        public const long MaxPetsCountPerRoom = 10;

        public const long WaiterWorkEndStep = 5;
        public const long CookWorkEndStep = 6; // 这个多一步
        public const long StylistWorkEndStep = 5;
        public const long EnginnerEndStep = 5;

        public const long GetRealPetStep = 2;
        public const long PetSecondGuideEndStep = 2;

        public const long SpeakerItemId = 200210;
        public const long SuperSpeakerItemId = 200575;
        public const long PowerFruit = 200160;
        public const int PublicCookCount = 5;
        public const int PublicCookGap = 10;
        public static long[] MaleCook = new long[3] { 300828, 300829, 300833 };
        public static long[] FemaleCook = new long[3] { 300834, 300835, 300833 };

        public static int TableCategory = 10007; // 这个类型的家具
        public static long GiftHour = 2;//送礼过期时间
        public static int MaxRoomNameLength = 9;
        public static int MinRoomNameLength = 4;
        public static long GiftBoxId = 403059;
        public static int GiftCostDiamond = 50;
        public static long GardenShopId = 530414;

        public static int MaxPetNameLength = 6;
        public static int MinPetNameLength = 2;

        public static int FreePetBackProbability = 10;
        public static int PetDaycarePrice = 30;
        public static int PetDaycareDays = 30;

        public static int VisitGardenCD = 300;
        public static int VisitGardenMinLv = 10;

        public static int ApplyOrganizationMinLv = 20;
        public static int ApplyOrganizationLimitHour = 12;

        public static int CrownMaxCollectTime = 480;
        public static int NormalMaxCollectTime = 360;
        public static int NormalFastCollectTimes = 1;
        public static int CrownFastCollectTimes = 3;
        public static bool AllowAssign = true;

        public static bool AllowTaLog = true;
        public static bool AllowInternalTestingAwardsLog = false;
        public static bool RecordRoomVersion = false;

        public static bool AllowChargeSucBackLog = false;

        public static bool UseBEChargeDatas = true;

        public static long DefaultSkinId = 300002;
        public static long DefaultMaleSuit = 300033;
        public static long DefaultFemaleSuit = 300034;

        public static HashSet<long> DefaultSuits = new HashSet<long>(3) { DefaultSkinId, DefaultMaleSuit, DefaultFemaleSuit };

        public static long ShareSystemAward = 6100;

        public static int AchieveMissionunlockIndex = 2;
        public static int DailyMissionunlockIndex = 6;
        public static int DailyWeddingMissionunlockIndex = 3;
        public static int ActivityMissionunlockIndex = 6;

        public static long BithdayMailId = 10027;

        public static int SignatureMaxLenth = 60;

        public static int OrderSlut6Level = 10;
        public static int OrderSlut8Level = 20;
        public static int OrderSlut10Level = 30;

        public static int CreditScoreLimit = 10000;

        public static int LeafletCount = 30;

        public static int DailyCrownLevel = 5;

        public static int NormalPlayerOrderRefreshDailyCount = 1;
        public static int CrownPlayerOrderRefreshDailyCount = 30;

        public static int PetGraveCost = 30000;
        public static int MaxPetSlot = 50;

        public static int AttentionCharacterLimit = 500;
        public static int CrownAttentionCharacterLimit = 1000;
        public static int FollowCharacterLimit = 500;
        public static int SendCharacterToPortalLevelTHreshold = 10;

        public static int NoteLimitEachRoom = 10;
        public static int RecordLimit = 50;
        public static int RoomNoticeLenthLimit = 120;

        public static int PalCardIncomeLimit = 300000;

        public static int LobbySpwanPoint = 1035943539;

        public static bool CloseRankingMail = false;

        public static bool TestPetLeft = false;

        public static bool RefreshFishRankingOnRestart = false;
        public static int RankingDropOffDays = 14;

        public static bool useOldCrop = false;

        public const long BaseTopoId = 161;
        public const long BaseMainHouseLookId = 157;
        public const long WeddingExploreLevel = 60001;

        public const long CloseFriendTreeType = 140090;

        public static int NoCrownWardrobeSize = 3;
        public static int CrownWardrobeSize = 12;

        public static int NoOperationExitGroupPhotoTime = 180;
        public static int OperateExitGroupPhotoTime = 60;
        public static int InviteExitGroupPhotoTime = 25;

        public static int DefaultGroupPhotoGroundId = 12101;

        public static int IntimacyForCloseFriend = 1200;

        public static int CloseFriendLimit = 7;

        public static int MaxOrganizationInfoCount = 200;

        public static long OrganizationBaseSceneId = 76000;

        public static long OrganizationChangeNameCostItem = 207062;

        public static int OrganizationCreateCost = 1500;

        public static int OrganizationBuildThreshold = 3;

        public static int DailyDonateOrderTimePerQuality = 5;
		
        public static int SendActivityGiftCountPerDay = 5;

        public static int TradingPasswordResetDay = 3;
        public static int WrongTradingPasswordThresoldPerHour = 5;

        public static int MaxFavouriteSceneCount = 100;
        public static int RefreshCacheSeconds = 60;

        public static long SosofruitId = 208135;

        public static int ShopMaxCanBuy = 3000;

        public static int HonoeyMoonPhotoPrice = 200;

        public static int DailyMissionCreateCount = 7;
        public static int DailyMissionExtraAwardId = 10001;

        public static int RecentRoomRecordLimitCount = 24;

        public static long WeeklyMissionAwardStarId = 1039;

        public static int WeeklyMissionAwardCost = 100;
        public static int RefreshWeeklyMissionAwardCost = 20;

        public static int HangRedUtaBasePrice = 52;
        public static int HangRedUtaPrice = 52;
        public static int RedUtaPraiseCountPerDay = 10;
        public static long OnRedUtaHungEvent = 105220010;
        public static long OnRedUtaCompleteEvent = 105220013;
        public static long RedUtaTreeObjectType = 650181;
        public static long HongLuanJieTreeType = 650339;

        public static int MaxLeafletPraiseNameLength = 50;
        
        public static long ChangeVehicleNumberItem = 212426;
        public static long VehicleRefitItem = 212427;
        public static int ViewVehicleNumberCount = 6;
        public static int CanGetWhiteVehicleNumberTimes = 6;
        public static long VehicleDrivingSkill = 756;

        public static int DressAwardTakenThreshold = 4;
        public static int DressAwardDailyTurnUpTime = 2;

        public static bool Debug = true;

        public static List<string> CloseFriendApplyStrs = new List<string>() { "PKT_CloseFriend0007", "PKT_CloseFriend0008", "PKT_CloseFriend0009" };

        public const long WeeklyMissionStartItem = 200594;

        public static PhantomShowMode PhantomDefaultShowMode = PhantomShowMode.登场触发 | PhantomShowMode.自然待机 | PhantomShowMode.问候 | PhantomShowMode.AB答题正确 | PhantomShowMode.家族战或森林死亡 | PhantomShowMode.点赞 | PhantomShowMode.换装 | PhantomShowMode.变身 | PhantomShowMode.摇摇罐;

        public static Dictionary<string, LiveSkillCategory> LiveSkillPropertyMapping = new Dictionary<string, LiveSkillCategory>()
        {
                {"FarmLevel", LiveSkillCategory.农场},
                {"CookLevel", LiveSkillCategory.烹饪},
                {"MineLevel", LiveSkillCategory.挖矿},
                {"FishLevel", LiveSkillCategory.钓鱼},
                {"HuntLevel", LiveSkillCategory.打猎},
                {"RanchLevel", LiveSkillCategory.牧场},
        };

        public static HashSet<long> ProcessGalleryIdsOnRecycle = new HashSet<long>();

        public static Dictionary<string, HashSet<long>> TALogBlackList = new Dictionary<string, HashSet<long>>();

        public static bool CheckToWriteTALog(string eventName, long typeId)
        {
            if (!TALogBlackList.TryGetValue(eventName, out var black_list_items))
            {
                return true;
            }

            return !black_list_items.Contains(typeId);
        }

        public sealed class Vocation
        {
            public static class Property
            {
                public const long HP = 10001;
                public const long MP = 10002;
                public const long Attack = 10003;
                public const long Defense = 10004;
            }
        }

        public enum StylistCommunictionEnum
        {
            退出交互 = 0,
            喜欢修改 = 1,
            否定修改 = 2,

            发送预览 = 101,
        }

        public static long[] KeyItems = new long[]
        {
            403527,//水壶架
            403968,//农药架
            403967,//宠物喂食器
        };

        public static readonly Dictionary<SquareShowType, long> SquareSceneTypeDic = new Dictionary<SquareShowType, long>()
        {
            { SquareShowType.开服活动广场, 152 },
            { SquareShowType.吾皇广场, 154 },
            { SquareShowType.元旦广场, 999 },
            { SquareShowType.春节广场, 160 },
            { SquareShowType.美食庆典广场,159}
        };

        public static bool LogUnknownGameError { get; set; } = false;

        public static bool EnableSceneObjectSearch { get; set; } = true;

        public static List<int> MerchantNpcDispositionHighValueBlock = new List<int> { 0,0,0,0,0,0,0,1,1,1 };
        public static List<int> MerchantNpcDispositionNormalBlock = new List<int> { 3,3,3,3,5 };
        public static List<int> MerchantBoradCastRefreshHours = new List<int> { 2, 6, 10, 14, 18, 22 };

        public static class VirtualCurrency
        {
            public static long 银币 = 1;
            public static long 金币 = 2;
            public static long 矿洞积分 = 3;
            public static long 人民币 = 4;
            public static long 友情印记 = 5;
            public static long 心意结晶 = 6;
            public static long 飞行棋积分 = 51;
            public static long 魔力结晶 = 52;
            public static long 美梦值 = 53;
            public static long 趴趴值 = 54;
            public static long 好运值 = 55;
            public static long 福气值 = 56;
            public static long 轰隆值 = 57;
            public static long 金票 = 99;
            public static long 金钻 = 100;
        }

        public static double AuctionRequestCooldown { get; set; } = 3;


        public static int ExpectInputBorder = 100000;//前端传入数据的边界值

        public static Dictionary<long, int> ElementGachaGroupDic = new Dictionary<long, int>()
        {
            {203209, 1 },//牧歌
            {203211, 2 },//阳光
            {203213, 3 },//潮汐
            {203215, 4 },//珍宝
            {203217, 5 },//繁茂
        };

        public static class DrawCardShop
        {
            public static Dictionary<List<EquipPart>, int> star5EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Hair, EquipPart.Hat, EquipPart.Scar }, 60},
                {new List<EquipPart>(){ EquipPart.Costume }, 80},
                {new List<EquipPart>(){ EquipPart.Eye }, 100},
                {new List<EquipPart>(){ EquipPart.Chin, EquipPart.Face, EquipPart.Mask }, 30},
                {new List<EquipPart>(), 70},
            };

            public static Dictionary<List<EquipPart>, int> star4EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 45},
                {new List<EquipPart>(){ EquipPart.Hair }, 35},
                {new List<EquipPart>(), 40},
            };

            public static Dictionary<List<EquipPart>, int> star3EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 25},
                {new List<EquipPart>(){ EquipPart.Hair }, 15},
                {new List<EquipPart>(), 20},
            };

            public static Dictionary<List<EquipPart>, int> ElementStar5EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 600},
                {new List<EquipPart>(), 400},
            };

            public static Dictionary<List<EquipPart>, int> ElementStar4EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 180},
                //{new List<EquipPart>(){ EquipPart.Hair }, 120},
                {new List<EquipPart>(), 120},
            };

            public static Dictionary<List<EquipPart>, int> ElementStar3EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 140},
                //{new List<EquipPart>(){ EquipPart.Hair }, 70},
                {new List<EquipPart>(), 70},
            };

            public static Dictionary<Quality, int> SceneObjectPrice = new Dictionary<Quality, int>()
            {
                { Quality.五星, 90 },
                { Quality.四星, 30 },
                { Quality.三星, 10 },
                { Quality.二星, 3 },
                { Quality.一星, 3 }
            };

            public static Dictionary<Quality, int> ElementSceneObjectPrice = new Dictionary<Quality, int>()
            {
                { Quality.五星, 600 },
                { Quality.四星, 200 },
                { Quality.三星, 50 },
                { Quality.二星, 10 },
                { Quality.一星, 3 }
            };

            public static Dictionary<Quality, int> SceneObjectLimit = new Dictionary<Quality, int>()
            {
                { Quality.五星, 1 },
                { Quality.四星, 2 },
            };

            public static Dictionary<Quality, int> ElementItemPrice = new Dictionary<Quality, int>()
            {
                { Quality.五星, 900 },
                { Quality.四星, 300 },
                { Quality.三星, 100 },
                { Quality.二星, 30 },
            };
        }        

        public static class RouletteShop
        {
            public static Dictionary<List<EquipPart>, int> star5EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Back, EquipPart.Weapon, EquipPart.Shield, EquipPart.Mouse, EquipPart.Costume, EquipPart.Eye }, 900},
                {new List<EquipPart>(){ EquipPart.Hair, EquipPart.Hat, EquipPart.Scar }, 760},
                {new List<EquipPart>(), 500},
            };

            public static Dictionary<List<EquipPart>, int> star4EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 580},
                {new List<EquipPart>(){ EquipPart.Hair }, 520},
                {new List<EquipPart>(), 560},
            };

            public static Dictionary<List<EquipPart>, int> star3EquipPrice = new Dictionary<List<EquipPart>, int>()
            {
                {new List<EquipPart>(){ EquipPart.Costume }, 440},
                {new List<EquipPart>(){ EquipPart.Hair }, 340},
                {new List<EquipPart>(), 400},
            };

            public static Dictionary<Quality, int> SceneObjectPrice = new Dictionary<Quality, int>()
            {
                { Quality.五星, 1000 },
                { Quality.四星, 480 },
                { Quality.三星, 280 },
                { Quality.二星, 160 },
            };
        }

        public static class CraftEquip
        {
            public static long probabilityItem = 212384;
            public static int great_success_rate = 2;
            public static int base_success_rate = 50;
            public static double grow_success_rate = 45.0;
            public static Dictionary<Quality, CraftEquipSetting> CraftEquipSettingDic = new Dictionary<Quality, CraftEquipSetting>()
            {
                {Quality.二星, new CraftEquipSetting(1, 1, 224250) },
                {Quality.三星, new CraftEquipSetting(1, 1, 224251) },
                {Quality.四星, new CraftEquipSetting(2, 2, 224252) },
                {Quality.五星, new CraftEquipSetting(3, 3, 224253) },
            };

            public class CraftEquipSetting
            {
                public int Exp;
                public int ReqLevel;
                public long breakAward;
                public CraftEquipSetting(int exp, int reqLevel, long breakAward)
                {
                    this.Exp = exp;
                    this.ReqLevel = reqLevel;
                    this.breakAward = breakAward;
                }
            }
        }
    
}
}
