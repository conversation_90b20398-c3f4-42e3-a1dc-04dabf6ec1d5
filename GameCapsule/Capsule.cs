using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using GameCapsule.ConfigObjects;
using OpGameconfig01;

namespace GameCapsule
{
    using ID_t = Int32;
    public class Capsule: ChildrenIndex
    {
        public ConfigObjectFactory add;
        private readonly GameNode _root;
        private OpGameconfig01.Game _binConf;
        
        public NodeGenIdOperator nodeGenIdOperator = new NodeGenIdOperator();

        // 缓存同族对象的源对象。
        // 源对象指具有相同基因序列的第一个ConfigObject。
        private readonly Dictionary<string, ID_t>  _families = new Dictionary<string, ID_t>();
        private readonly Dictionary<ID_t, ID_t>  _ignoreSerialize = new Dictionary<ID_t, ID_t>();
    
        
        public Capsule()
        {
            // initialize the factory.
            this.add = new ConfigObjectFactory(this);
            // initialize the only game node.
            this._root = new GameNode(this);
        }

        public void resetPreprocessedData()
        {
            this._families.Clear();
            this._ignoreSerialize.Clear();
        }

        public Dictionary<string, ID_t> families => this._families;

        public Dictionary<ID_t, ID_t> ignoreList => _ignoreSerialize;

        public byte[] serialize()
        {
            if (_root == null)
            {
                throw new Exception("Capsule::serialize() Error: Empty root node, aka GameNode.");
            }

            _binConf = new OpGameconfig01.Game();
            var game = _binConf;

            // 1. serialize root node also known as GameNode first.
            this._root.serialize(game);

            // 2-1. pre-serialize
            this.resetPreprocessedData();
            foreach (var item in Objects.Values)
            {
                item.preSerialize();
            }

            // 2-2. serialize all other objects.
            foreach (var item in Objects.Values)
            {
                if (!_ignoreSerialize.ContainsKey(item.id))
                {
                    item.serialize(game);
                }
            }

            // 3. return binConfig buffer.
            var buffer = Helpers.GetBytes(_binConf);

            return buffer;
        }

        public byte[] serializeScene(ID_t sceneId)
        {
            if (null == _root)
            {
                throw new Exception("Capsule::serialize() Error: Empty root node, aka GameNode.");
            }

            _binConf = new OpGameconfig01.Game();
            var game = _binConf;
            Objects.TryGetValue(sceneId, out var sceneNode);
            if (sceneNode != null)
            {
                var id = sceneNode.parent;
                var name = sceneNode.name;
                game.Node = new OpGameconfig01.Node();
                game.Node.LockParent.Clear();
                game.Node.Parent = 0;
                game.Node.Id    = id;
                game.Node.Name = name;
                game.Node.Type = OpDef.NodeType.GameNodeType;

                var sceneReleatedObjects = iterateGetObjects(sceneNode);

                // 2-1. pre-serialize
                this.resetPreprocessedData();

                foreach( var obj in sceneReleatedObjects)
                {
                    obj.preSerialize();
                }

                // 2-2. serialize all other objects
                foreach( var obj in sceneReleatedObjects)
                {
                    if (!this._ignoreSerialize.ContainsKey(obj.id))
                        obj.serialize(game);
                }

            }

            var buffer = Helpers.GetBytes(_binConf);

            return buffer;

        }

        public byte[] serializeRoot()
        {
            if (null == _root)
            {
                throw new Exception("Capsule::serialize() Error: Empty root node, aka GameNode.");
            }

            _binConf = new OpGameconfig01.Game();
            var game = _binConf;

            this._root.serialize(game);

            this.resetPreprocessedData();
            
            if (this._root.moss != null)
            {
                var elements = this._root.moss.keyElementMap.Values;

                foreach (var element in elements) {
                    this.addPaletteObject(element);
                }
            }

            if (this._root.palette != null) {
                var terrains = this._root.palette.keyTerrainMap.Values;

                foreach (var terrain in terrains) {
                    this.addPaletteObject(terrain);
                }
            }
            

            var needSerializeObjs = Objects.Values.ToList().FindAll(obj =>
            {
                return (
                    obj.type != (int)OpDef.NodeType.SceneNodeType &&
                    obj.parent == this._root.id &&
                    !this._ignoreSerialize.ContainsKey(obj.id)
                );
            });
            needSerializeObjs.AddRange(this._palette_objects.Values);

            iterateSerialize(needSerializeObjs, game);
            var buffer = Helpers.GetBytes(_binConf);

            return buffer;
        }


        private void iterateSerialize(List<IConfigObject> objs, OpGameconfig01.Game game )
        {
            foreach (var obj in objs)
            {
                obj.serialize(game);
                if (obj.children != null && obj.children.Count > 0)
                {
                    this.iterateSerialize(obj.children, game);
                }
            }
        }


        private List<IConfigObject> iterateGetObjects(IConfigObject parnetNode)
        {
            var r = new List<IConfigObject>();
            return iterateGetObjects(parnetNode, r);
        }

        private List<IConfigObject> iterateGetObjects(IConfigObject parnetNode, List<IConfigObject> ret)
        {

            if (null == parnetNode)
            {
                return ret;
            }

            ret.Add(parnetNode);
            if (parnetNode.children != null && parnetNode.children.Count > 0)
            {
                foreach(var obj in parnetNode.children)
                {
                    iterateGetObjects(obj, ret);
                }
            }
            return ret;
        }

        public IConfigObject? getPaletteById(ID_t id)
        {
            if (!this._palette_objects.TryGetValue(id, out var r)) return null;
            return r;
        }
        protected void doDeserialize(Google.Protobuf.IMessage protocolObj)
        {
            if (null == protocolObj) return;
            var nodeAccessor = protocolObj.Descriptor.FindFieldByName("node").Accessor;
            if (!nodeAccessor.HasValue(protocolObj))
            {
                return;
            }

            var nodeValue = nodeAccessor.GetValue(protocolObj) as Google.Protobuf.IMessage;
            if (nodeValue == null)
            {
                return;
            }
            var nodeTypeAccessor = nodeValue.Descriptor.FindFieldByName("type").Accessor;
            if (!nodeTypeAccessor.HasValue(nodeValue))
            {
                return;
            }

            var nodeType = nodeTypeAccessor.GetValue(nodeValue);
            if (nodeType == null)
            { 
                return;
            }
            var nodeConstructor = Constants.NODE_T[(int)nodeType];
            if (Constants.NODE_T.IndexOf(nodeConstructor) == -1)
            {
                Console.WriteLine("Can not find class name to doDeserialize, " + nodeConstructor);
                return;
            }
            var node = IConfigObject.createConfigObjectByName(nodeConstructor, this);
            if (node == null)
            {
                return;
            }
            node.deserialize(protocolObj);

            // throw new NotImplementedException();
            /*
             if (protocolObj.hasOwnProperty("node")) {
                const nodeType: any = protocolObj["node"].type;
                const nodeConstructor: string = Constants.NODE_T[nodeType] || "";
                if (ConfigObjects.hasOwnProperty(nodeConstructor)) {
                    // @ts-ignore
                    const node = new (ConfigObjects as {
                        [key: string]: BaseConfigObject;
                    })[nodeConstructor](this);
                    node.deserialize(protocolObj);
                }
            }
             */
        }

        public void deserialize(byte[] buffer)
        {
            OpGameconfig01.Game message = OpGameconfig01.Game.Parser.ParseFrom(buffer);
            var delayDeserialize = new List<Google.Protobuf.IMessage>();

            // 1. deserialize game
            this._root.deserialize(message);
            // this._root.preDeserializePalettes(message);

            addNodeIds(message);
            

            // 2-1. deserialize other objects.
            foreach (var field in OpGameconfig01.Game.Descriptor.Fields.InDeclarationOrder())
            {
                // field.Accessor.GetValue(message);
                if (field.Name == "scenes")
                {
                    continue;
                }
                if (field.Name == "entityPalettes")
                {
                    continue ;
                }

                // 废弃
                if (field.Name == "terrain")
                {
                    doDeserialize(message.Terrain);
                }

                if (field.Name == "mossList")
                {
                    doDeserialize(message.MossList);
                }

                if (field.Name == "walls")
                {
                    doDeserialize(message.Walls);
                }
                // --------

                if (field.Name == "groundWalkableCollection")
                {
                    doDeserialize(message.GroundWalkableCollection);
                }

                if (field.Name == "placeableCollection")
                {
                    doDeserialize(message.PlaceableCollection);
                }

                if (field.Name == "customCollections")
                {
                    foreach (var customCollection in message.CustomCollections)
                    {
                        doDeserialize(customCollection);
                    }
                }
                
                if (field.Name == "combineElements")
                {
                    doDeserialize(message.CombineElements);
                }
                if (field.Name == "palette" || field.Name == "moss")
                {
                    List<EntityKeyPair> peers = new List<EntityKeyPair>();
                    if (field.Name == "palette" && message.Palette != null)
                    {
                        peers = message.Palette.Peers.ToList();
                    } else if (field.Name == "moss" && message.Moss != null)
                    {
                        peers = message.Moss.Peers.ToList();
                    }

                    foreach (var peer in peers)
                    {
                        var entity = peer.Entity;
                        this.doDeserialize(entity);
                    }
                }

                /*
                if (!field.Accessor.Descriptor.IsRepeated  && !field.Accessor.Descriptor.IsRequired && !field.Accessor.HasValue(message))
                {
                    continue;
                }*/
                var f = field.Accessor.GetValue(message) as IList;
                if (f == null) continue;
                if (field.IsRepeated)
                {
                    for (int i = 0; i < f.Count; i++)
                    {
                        var fieldNode = f[i] as Google.Protobuf.IMessage;
                        var node = fieldNode?.Descriptor.FindFieldByName("node").Accessor.GetValue(fieldNode) as OpGameconfig01.Node;
                        if (node == null) continue;
                        var nodeType = node.Type;
                        
                        if (nodeType == OpDef.NodeType.ForkType)
                        {
                            delayDeserialize.Add(fieldNode);
                        } else
                        {
                            doDeserialize(fieldNode);
                        }

                    }
                }
            }

            // 2-2. deserialize all Fork objects
            foreach (var proto in delayDeserialize)
            {
                doDeserialize(proto);
            }

            // 2-3. Link all ConfigObjects
            foreach (var obj in Objects)
            {
                obj.Value.link();
            }
            this._root.deserializePalettes(message);
        }

        public void addNodeIds(OpGameconfig01.Game message)
        {
            bool isGamePi = false;
            
            foreach (var field in OpGameconfig01.Game.Descriptor.Fields.InDeclarationOrder())
            {
                if (field.Name == "scenes")
                {
                    var f = field.Accessor.GetValue(message) as IList;

                    if (f.Count > 0)
                    {
                        isGamePi = true;
                    }
                    else
                    {
                        isGamePi = false;
                    }
                    
                    break;
                }
            }

            if (isGamePi)
            {
                foreach (var field in OpGameconfig01.Game.Descriptor.Fields.InDeclarationOrder())
                {
                    if (field.Name == "scenes")
                    {
                        continue;
                    }
                    if (field.Name == "entityPalettes")
                    {
                        continue ;
                    }
                    
                    var f = field.Accessor.GetValue(message) as IList;
                    if (f == null) continue;
                    if (field.IsRepeated)
                    {
                        for (int i = 0; i < f.Count; i++)
                        {
                            var fieldNode = f[i] as Google.Protobuf.IMessage;
                            var node = fieldNode?.Descriptor.FindFieldByName("node").Accessor.GetValue(fieldNode) as OpGameconfig01.Node;
                            if (node == null) continue;
                            var nodeType = node.Type;

                            Helpers.GameHashIDs.TryAdd(node.Id, true);
                        }
                    }
                }
            }
            else
            {
                foreach (var field in OpGameconfig01.Game.Descriptor.Fields.InDeclarationOrder())
                {
                    var f = field.Accessor.GetValue(message) as IList;
                    if (f == null) continue;
                    if (field.IsRepeated)
                    {
                        for (int i = 0; i < f.Count; i++)
                        {
                            var fieldNode = f[i] as Google.Protobuf.IMessage;
                            var node = fieldNode?.Descriptor.FindFieldByName("node").Accessor.GetValue(fieldNode) as OpGameconfig01.Node;
                            if (node == null) continue;
                            var nodeType = node.Type;

                            nodeGenIdOperator.GenHashIDs.Add(node.Id);
                        }
                    }
                }
            }
            
        }

        // public toJSON(byte[] buffer) {
        //     OpGameconfig01.Game message = OpGameconfig01.Game.Parser.ParseFrom(buffer);
        //
        //     return ;
        // }
        
        public GameNode root
        {
            get { return _root; }
        }


        public IConfigObject getObjectBySN(string sn)
        {
            foreach(var obj in objectList)
            {
                if (sn == obj.sn)
                {
                    return obj;
                }
            }
            return null;
        }


        public List<ID_t> getAllChildrenIds(IConfigObject node)
        {
            List<ID_t> result = new List<ID_t>();
            void WalkTree(IConfigObject tree)
            {
                if (tree.children != null && tree.children.Count > 0)
                {
                    for (int i = 0; i < tree.children.Count; i++)
                    {
                        var child = tree.children[i];
                        if (child != null)
                        {
                            result.Add(child.id);
                            WalkTree(child);
                        }
                    }
                }
            }
            WalkTree(node);
            return result;
        }

        // 在保存到我的素材功能中，导入的是展开的节点，这时需要将存在父子关系的objects去重
        protected List<IConfigObject> deduplicateNodes(List<IConfigObject> nodes)
        {

            var nodesMap = new Dictionary<ID_t, IConfigObject>();
            var nodesIds = new List<List<ID_t>>();
            foreach (var node in nodes)
            {
                nodesIds.Add(this.getAllChildrenIds(node));
                nodesMap.Add(node.id, node);
            }

            foreach (var ids in nodesIds)
            {
                foreach (var id in ids)
                {
                    if (nodesMap.ContainsKey(id))
                    {
                        nodesMap.Remove(id);
                    }
                }
            }
            return nodesMap.Values.ToList();
        }


        protected void emptyObjectCap(List<IConfigObject> objects)
        {
            foreach(var obj in objects)
            {
                obj.cap.families.Clear();
                if (obj.children != null && obj.children.Count > 0)
                {
                    emptyObjectCap(obj.children);
                }
            }
        }

        // 从当前的Capsule中导出Nodes
        public Capsule export(List<IConfigObject> objects)
        {
            objects = deduplicateNodes(objects);
            this.emptyObjectCap(objects);
            var newCap = new Capsule();
            void Travel(IConfigObject node, ID_t parent = 0)
            {
                var newNode = ObjectCopier.Clone(node);
                if (parent != 0) newNode.parent = parent;
                newCap.addObject(newNode);
                foreach (var child in node.children)
                {
                    Travel(child);
                }
            }
            foreach( var obj in objects)
            {
                Travel(obj, newCap.root.id);
            }
            return newCap;

            // throw new NotImplementedException();
            
        }

        public void importScene(Capsule src)
        {
            foreach( var obj in src.objectList)
            {
                if (obj.parent == src.root.id)
                {
                    obj.parent = this.root.id;
                }
                obj.cap = this;
                addObject(obj);
            }
        }

        // 从外部Capsule中导入Nodes
        // cap
        //   root
        //     sceneNode
        //       element1
        //
        // cap
        //  root
        //    element
        //      attr1
        //      attr2
        //
        // cap
        //   root
        //     sceneNode
        //       element1
        //       element
        //         attr1
        //         attr2
        //       element<B>
        //         attr1<B>
        //         attr2<B>

        public struct ImportNode
        {
            public ID_t id;
            public ID_t parent;

        }
        
        public List<ImportNode> import(Capsule src, IConfigObject targetNode, ID_t? rootId) {
            var sourceCapsule  = new Capsule();
            sourceCapsule.deserialize(src.serialize());

            var sourceNodes  = sourceCapsule.root.children;

            var importNodes = new List<ImportNode>();
            Action<List<IConfigObject>, IConfigObject, int?> settNodeParent = null;
            settNodeParent = (List<IConfigObject> nodes, IConfigObject target, ID_t? id) =>
            {
                foreach ( var node in nodes) {
                    var children  = node.children.ToList();
                    children.ForEach(child => { node.removeNode(child); });
                    node.renewId(id);
                    node.cap = target.cap;
                    node.parentObject = target;
                    target.cap.addObject(node);
                    target.appendNode(node);

                    importNodes.Add(new ImportNode(){
                        id= node.id, parent= node.parent
                    });

                    if (children != null && children.Count > 0)
                    {
                        settNodeParent(children, node, null);
                    }
                }
            };


            settNodeParent(sourceNodes, targetNode, rootId);

            return importNodes;
        }

        private List<ID_t> walkTopLevelNode(
            List<IConfigObject> nodes,
            bool returnAll = false
            )
        {
            List<ID_t> ret = new List<ID_t>();
            foreach (var node in nodes)
            {
                ret.Add(node.id);
                if (node.children != null && node.children.Count > 0)
                {
                    if (returnAll)
                    {
                        walkTopLevelNode(node.children, returnAll);
                    }
                }
            }
            return ret;
        }

        // 用于在同一个Capsule中克隆ConfigObject
        // 将会获得一个新的Node，如果此Node有子节点会同样克隆子节点
        public IConfigObject clone(
            ID_t srcId,
            IConfigObject parent,
            bool cloneFork = false)
        {
            var origin = this.getObject(srcId);
            if (origin == null)
            {
                // Console.WriteLine(String.Format("Origin object[{0}] does not exist.", src_id));
                return null;
            }

            var dolly = IConfigObject.createConfigObjectByName(origin.className, this);

            dolly.parentObject = parent;
            dolly.duplicate(origin);
            addObject(dolly);

            // 遍历克隆对象的子节点，克隆
            for (int i = 0; i < origin.children.Count; i++)
            {
                var child = origin.children[i];
                if (child is ForkNode && !cloneFork) continue;
                var son = this.clone(child.id, dolly);
                son?.link();
            }

            return dolly;
        }


        public void inspect()
        {
            // ???? 啥用 好像也没打印 也没return 啥都没干？
            /*
        const self = this;

        const printTree = function (id: number): any {
            const obj = self.getObject(id);
            if (!obj) {
                return;
            }

            let out: { [key: string]: any } = {};
            for (const child of obj.children) {
                out[`${child.className}[${child.id}]`] = printTree(child.id);
            }

            return out;
        };

        const cap: { [key: string]: any } = {};
        cap[`${self.root.className}[${self.root.id}]`] = printTree(
            self.root.id
        );
             */
            throw new NotImplementedException();
        }

        public void destroy()
        {
            foreach (var obj in Objects.Values)
            {
                obj.destroy();
            }
        }


    }



}
