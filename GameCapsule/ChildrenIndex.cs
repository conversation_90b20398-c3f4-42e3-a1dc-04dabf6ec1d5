using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using GameCapsule.ConfigObjects;

namespace GameCapsule
{
    using ID_t = Int32;
    public class ChildrenIndex
    {
        protected readonly Dictionary<ID_t, IConfigObject> Objects = new Dictionary<ID_t, IConfigObject>();
        protected readonly Dictionary<ID_t, IConfigObject> _palette_objects = new Dictionary<ID_t, IConfigObject>();
        private readonly Dictionary<ID_t, Dictionary<ID_t, IConfigObject>> _branchCache = new Dictionary<ID_t, Dictionary<ID_t, IConfigObject>>();


        public List<IConfigObject> getChildrenList(ID_t parent)
        {
            _branchCache.TryGetValue(parent, out var children);
            if (children == null) { return new List<IConfigObject>(); }
            return children.Values.ToList();
        }

        
        public void setPaletteObject(ID_t id, IConfigObject node) {
            this._palette_objects.Add(id, node);
        }
        
        public void addPaletteObject(IConfigObject obj) {
            this._palette_objects.Add(obj.id, obj);

            if (obj.children != null && obj.children.Count > 0) {
                foreach (var child in obj.children) {
                    this.addPaletteObject(child);
                }
            }
        }

        public void removePaletteObject(IConfigObject obj) {
            this._palette_objects.Remove(obj.id);
            this.removeObject(obj);

            if (obj.children != null && obj.children.Count > 0) {
                foreach (var child in obj.children) {
                    this.removePaletteObject(child);
                }
            }
        }



        public void addObject(IConfigObject obj)
        {
            if (obj == null)
            {
                throw new Exception("Empty Object");
            }

            if (Objects.ContainsKey(obj.id))
            {
                Objects.TryGetValue(obj.id, out var oldObj);

                if (oldObj.name != obj.name)
                {
                    Console.WriteLine("objType " + oldObj.type + ", parentObjId: " + oldObj.parentObject?.id);
                    Console.WriteLine("重复id: " + obj.id);
                }
                
            }
            // add to this._objects
            Objects[obj.id] = obj;
            // link to parent.
            // 如果是fork则不需要link到parent node
            if (!obj.isFork)
            {
                obj.link();
            }

            // add to cache
            if (!_branchCache.ContainsKey(obj.parent))
            {
                _branchCache[obj.parent] = new Dictionary<ID_t, IConfigObject>();
            }

            _branchCache.TryGetValue(obj.parent, out var branch);
            if (branch != null) branch[obj.id] = obj;

        }


        public void removeObject(IConfigObject obj)
        {
            if (obj == null)
            {
                throw new Exception("Empty Object");
            }
            // 1. remove object from parent as member property.
            obj.unlink();
            // 2. remove object from tree.
            Objects.Remove(obj.id);
            // 3. remove from cache
            _branchCache.TryGetValue(obj.parent, out var branch);
            if (branch != null) branch.Remove(obj.id);
        }

        public void removeObjectById(ID_t id)
        {
            Objects.Remove(id);
        }


        public void setObject(ID_t id, IConfigObject node)
        {
            if (Objects.ContainsKey(id) && Objects[id] == node)
            {
                return;
            }
            Objects.Add(id, node);
            // _objects[id] = node;
        }

        public IConfigObject getObject(ID_t childOrParentId)
        {
            Objects.TryGetValue(childOrParentId, out var obj);
            return obj;
        }

        public List<IConfigObject>  objectList => Objects.Values.ToList();
    }
}
